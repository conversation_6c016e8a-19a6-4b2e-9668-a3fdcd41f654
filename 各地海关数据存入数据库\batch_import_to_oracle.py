import pandas as pd
import cx_Oracle
import sys
import os
from datetime import datetime
from pathlib import Path

def import_single_csv_to_oracle(csv_file_path, db_config, table_name="T_STATISTICAL_CUS_TOTAL_CS"):
    """
    将单个CSV文件导入Oracle数据库
    """
    try:
        # 读取CSV文件
        print(f"正在读取CSV文件: {csv_file_path}")
        df = pd.read_csv(csv_file_path, encoding='utf-8-sig')
        
        print(f"CSV文件读取成功")
        print(f"总记录数: {len(df)}")
        print(f"字段数: {len(df.columns)}")
        
        # 连接Oracle数据库
        print(f"正在连接Oracle数据库...")
        conn = cx_Oracle.connect(**db_config)
        cursor = conn.cursor()
        
        print("数据库连接成功")
        
        # 准备插入SQL（包含完整46个字段）
        insert_sql = f"""
        INSERT INTO {table_name} (
            STAT_DATE, STAT_TYPE, STAT_NAME, STAT_CODE, STAT_CONTENT_RAW, STAT_CONTENT_CLEANSE,
            ACC_A_CNY_AMOUNT, ACC_A_CNY_YOY, ACC_A_USD_AMOUNT, ACC_A_USD_YOY,
            MON_A_CNY_AMOUNT, MON_A_CNY_YOY, MON_A_USD_AMOUNT, MON_A_USD_YOY,
            ACC_E_CNY_AMOUNT, ACC_E_CNY_YOY, ACC_E_USD_AMOUNT, ACC_E_USD_YOY,
            MON_E_CNY_AMOUNT, MON_E_CNY_YOY, MON_E_USD_AMOUNT, MON_E_USD_YOY,
            ACC_I_CNY_AMOUNT, ACC_I_CNY_YOY, ACC_I_USD_AMOUNT, ACC_I_USD_YOY,
            MON_I_CNY_AMOUNT, MON_I_CNY_YOY, MON_I_USD_AMOUNT, MON_I_USD_YOY,
            ACC_E_AMOUNT, ACC_E_AMOUNT_UNIT, ACC_E_AMOUNT_YOY,
            MON_E_AMOUNT, MON_E_AMOUNT_UNIT, MON_E_AMOUNT_YOY,
            ACC_I_AMOUNT, ACC_I_AMOUNT_UNIT, ACC_I_AMOUNT_YOY,
            MON_I_AMOUNT, MON_I_AMOUNT_UNIT, MON_I_AMOUNT_YOY,
            RANK_MARKERS, DATA_SOURCE, EMPHASIS_OR_EMERGING_MARK, CREATE_TIME
        ) VALUES (
            :1, :2, :3, :4, :5, :6,
            :7, :8, :9, :10,
            :11, :12, :13, :14,
            :15, :16, :17, :18,
            :19, :20, :21, :22,
            :23, :24, :25, :26,
            :27, :28, :29, :30,
            :31, :32, :33,
            :34, :35, :36,
            :37, :38, :39,
            :40, :41, :42,
            :43, :44, :45, :46
        )
        """
        
        # 批量插入数据
        batch_size = 100
        total_rows = len(df)
        inserted_rows = 0
        
        print(f"开始插入数据（批量大小: {batch_size}）...")
        
        for start_idx in range(0, total_rows, batch_size):
            end_idx = min(start_idx + batch_size, total_rows)
            batch_df = df.iloc[start_idx:end_idx]
            
            # 准备批量数据
            batch_data = []
            for _, row in batch_df.iterrows():
                # 处理数值字段的空值
                def get_numeric_value(value):
                    if pd.isna(value) or value == '' or str(value).lower() == 'nan':
                        return None
                    try:
                        return float(value)
                    except (ValueError, TypeError):
                        return None
                
                # 处理字符串字段的空值
                def get_string_value(value):
                    if pd.isna(value) or value == '' or str(value).lower() == 'nan':
                        return None
                    return str(value).strip()
                
                # 处理日期字段的空值和格式转换
                def get_date_value(value):
                    if pd.isna(value) or value == '' or str(value).lower() == 'nan':
                        return None
                    try:
                        from datetime import datetime, date
                        # 处理 STAT_DATE 格式：2024/10/1 -> datetime.date对象
                        date_str = str(value).strip()
                        if '/' in date_str and len(date_str.split('/')) == 3:
                            parts = date_str.split('/')
                            year, month, day = int(parts[0]), int(parts[1]), int(parts[2])
                            return date(year, month, day)
                        elif '-' in date_str:
                            # 已经是正确格式 YYYY-MM-DD
                            parts = date_str.split('-')
                            if len(parts) == 3:
                                year, month, day = int(parts[0]), int(parts[1]), int(parts[2])
                                return date(year, month, day)
                        # 尝试直接解析
                        return datetime.strptime(date_str, '%Y-%m-%d').date()
                    except:
                        return None
                
                # 处理日期时间字段的空值和格式转换
                def get_datetime_value(value):
                    if pd.isna(value) or value == '' or str(value).lower() == 'nan':
                        return None
                    try:
                        from datetime import datetime
                        # 处理 CREATE_TIME 格式：2025/08/04 13:54:38 -> datetime对象
                        datetime_str = str(value).strip()
                        if '/' in datetime_str:
                            # 分离日期和时间部分
                            if ' ' in datetime_str:
                                date_part, time_part = datetime_str.split(' ', 1)
                                parts = date_part.split('/')
                                if len(parts) == 3:
                                    year, month, day = int(parts[0]), int(parts[1]), int(parts[2])
                                    time_parts = time_part.split(':')
                                    hour, minute = int(time_parts[0]), int(time_parts[1])
                                    second = int(time_parts[2]) if len(time_parts) > 2 else 0
                                    return datetime(year, month, day, hour, minute, second)
                            else:
                                parts = datetime_str.split('/')
                                if len(parts) == 3:
                                    year, month, day = int(parts[0]), int(parts[1]), int(parts[2])
                                    return datetime(year, month, day)
                        elif '-' in datetime_str:
                            # 已经是正确格式
                            if ' ' in datetime_str:
                                return datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')
                            else:
                                return datetime.strptime(datetime_str, '%Y-%m-%d')
                        # 尝试直接解析
                        return datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')
                    except:
                        return None
                
                # 构建数据行（按表结构顺序，包含完整46个字段）
                data_row = (
                    # STAT_DATE, STAT_TYPE, STAT_NAME, STAT_CODE, STAT_CONTENT_RAW, STAT_CONTENT_CLEANSE
                    get_date_value(row['STAT_DATE']),
                    get_string_value(row['STAT_TYPE']),
                    get_string_value(row['STAT_NAME']),
                    get_string_value(row['STAT_CODE']),
                    get_string_value(row['STAT_CONTENT_RAW']),
                    get_string_value(row['STAT_CONTENT_CLEANSE']),
                    # ACC_A_CNY_AMOUNT, ACC_A_CNY_YOY, ACC_A_USD_AMOUNT, ACC_A_USD_YOY
                    get_numeric_value(row['ACC_A_CNY_AMOUNT']),
                    get_numeric_value(row['ACC_A_CNY_YOY']),
                    get_numeric_value(row['ACC_A_USD_AMOUNT']),
                    get_numeric_value(row['ACC_A_USD_YOY']),
                    # MON_A_CNY_AMOUNT, MON_A_CNY_YOY, MON_A_USD_AMOUNT, MON_A_USD_YOY
                    get_numeric_value(row['MON_A_CNY_AMOUNT']),
                    get_numeric_value(row['MON_A_CNY_YOY']),
                    get_numeric_value(row['MON_A_USD_AMOUNT']),
                    get_numeric_value(row['MON_A_USD_YOY']),
                    # ACC_E_CNY_AMOUNT, ACC_E_CNY_YOY, ACC_E_USD_AMOUNT, ACC_E_USD_YOY
                    get_numeric_value(row['ACC_E_CNY_AMOUNT']),
                    get_numeric_value(row['ACC_E_CNY_YOY']),
                    get_numeric_value(row['ACC_E_USD_AMOUNT']),
                    get_numeric_value(row['ACC_E_USD_YOY']),
                    # MON_E_CNY_AMOUNT, MON_E_CNY_YOY, MON_E_USD_AMOUNT, MON_E_USD_YOY
                    get_numeric_value(row['MON_E_CNY_AMOUNT']),
                    get_numeric_value(row['MON_E_CNY_YOY']),
                    get_numeric_value(row['MON_E_USD_AMOUNT']),
                    get_numeric_value(row['MON_E_USD_YOY']),
                    # ACC_I_CNY_AMOUNT, ACC_I_CNY_YOY, ACC_I_USD_AMOUNT, ACC_I_USD_YOY
                    get_numeric_value(row['ACC_I_CNY_AMOUNT']),
                    get_numeric_value(row['ACC_I_CNY_YOY']),
                    get_numeric_value(row['ACC_I_USD_AMOUNT']),
                    get_numeric_value(row['ACC_I_USD_YOY']),
                    # MON_I_CNY_AMOUNT, MON_I_CNY_YOY, MON_I_USD_AMOUNT, MON_I_USD_YOY
                    get_numeric_value(row['MON_I_CNY_AMOUNT']),
                    get_numeric_value(row['MON_I_CNY_YOY']),
                    get_numeric_value(row['MON_I_USD_AMOUNT']),
                    get_numeric_value(row['MON_I_USD_YOY']),
                    # ACC_E_AMOUNT, ACC_E_AMOUNT_UNIT, ACC_E_AMOUNT_YOY
                    get_numeric_value(row['ACC_E_AMOUNT']),
                    get_string_value(row['ACC_E_AMOUNT_UNIT']),
                    get_numeric_value(row['ACC_E_AMOUNT_YOY']),
                    # MON_E_AMOUNT, MON_E_AMOUNT_UNIT, MON_E_AMOUNT_YOY
                    get_numeric_value(row['MON_E_AMOUNT']),
                    get_string_value(row['MON_E_AMOUNT_UNIT']),
                    get_numeric_value(row['MON_E_AMOUNT_YOY']),
                    # ACC_I_AMOUNT, ACC_I_AMOUNT_UNIT, ACC_I_AMOUNT_YOY
                    get_numeric_value(row['ACC_I_AMOUNT']),
                    get_string_value(row['ACC_I_AMOUNT_UNIT']),
                    get_numeric_value(row['ACC_I_AMOUNT_YOY']),
                    # MON_I_AMOUNT, MON_I_AMOUNT_UNIT, MON_I_AMOUNT_YOY
                    get_numeric_value(row['MON_I_AMOUNT']),
                    get_string_value(row['MON_I_AMOUNT_UNIT']),
                    get_numeric_value(row['MON_I_AMOUNT_YOY']),
                    # RANK_MARKERS, DATA_SOURCE, EMPHASIS_OR_EMERGING_MARK, CREATE_TIME
                    get_string_value(row['RANK_MARKERS']),
                    get_string_value(row['DATA_SOURCE']),
                    get_string_value(row['EMPHASIS_OR_EMERGING_MARK']),
                    get_datetime_value(row['CREATE_TIME'])
                )
                batch_data.append(data_row)
            
            # 执行批量插入
            cursor.executemany(insert_sql, batch_data)
            conn.commit()
            
            inserted_rows += len(batch_data)
            progress = (inserted_rows / total_rows) * 100
            print(f"  已插入 {inserted_rows}/{total_rows} 条记录 ({progress:.1f}%)")
        
        print(f"数据插入完成！共插入 {inserted_rows} 条记录")
        
        # 关闭连接
        cursor.close()
        conn.close()
        
        return True, inserted_rows
        
    except cx_Oracle.DatabaseError as e:
        print(f"数据库错误: {e}")
        # 打印更详细的错误信息
        if hasattr(e, 'args') and len(e.args) > 0:
            error = e.args[0]
            if hasattr(error, 'message'):
                print(f"详细错误: {error.message}")
        return False, 0
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")
        return False, 0
    finally:
        try:
            if 'conn' in locals():
                conn.close()
        except:
            pass

def test_single_record(csv_file_path, db_config):
    """
    测试插入单条记录，用于调试
    """
    try:
        print(f"测试单条记录插入...")
        df = pd.read_csv(csv_file_path, encoding='utf-8-sig')
        
        if len(df) == 0:
            print("CSV文件为空")
            return
        
        # 取第一条记录
        first_row = df.iloc[0]
        print(f"第一条记录内容:")
        for col in df.columns:
            print(f"  {col}: {first_row[col]} (类型: {type(first_row[col])})")
        
        # 连接数据库
        conn = cx_Oracle.connect(**db_config)
        cursor = conn.cursor()
        
        # 测试简单插入（先测试不含日期的字段）
        test_sql = """
        INSERT INTO T_STATISTICAL_CUS_TOTAL_CS (
            STAT_TYPE, STAT_NAME, STAT_CONTENT_RAW, STAT_CONTENT_CLEANSE,
            MON_A_CNY_AMOUNT, DATA_SOURCE
        ) VALUES (
            :1, :2, :3, :4, :5, :6
        )
        """
        
        # 准备测试数据
        def get_date_value(value):
            if pd.isna(value) or value == '':
                return None
            date_str = str(value).strip()
            if '/' in date_str:
                parts = date_str.split('/')
                if len(parts) == 3:
                    year, month, day = parts[0], parts[1].zfill(2), parts[2].zfill(2)
                    return f"{year}-{month}-{day}"
            return str(value)
        
        def get_datetime_value(value):
            if pd.isna(value) or value == '':
                return None
            datetime_str = str(value).strip()
            if '/' in datetime_str:
                if ' ' in datetime_str:
                    date_part, time_part = datetime_str.split(' ', 1)
                    parts = date_part.split('/')
                    if len(parts) == 3:
                        year, month, day = parts[0], parts[1].zfill(2), parts[2].zfill(2)
                        return f"{year}-{month}-{day} {time_part}"
            return str(value)
        
        test_data = (
            str(first_row['STAT_TYPE']) if not pd.isna(first_row['STAT_TYPE']) else None,
            str(first_row['STAT_NAME']) if not pd.isna(first_row['STAT_NAME']) else None,
            str(first_row['STAT_CONTENT_RAW']) if not pd.isna(first_row['STAT_CONTENT_RAW']) else None,
            str(first_row['STAT_CONTENT_CLEANSE']) if not pd.isna(first_row['STAT_CONTENT_CLEANSE']) else None,
            float(first_row['MON_A_CNY_AMOUNT']) if not pd.isna(first_row['MON_A_CNY_AMOUNT']) else None,
            str(first_row['DATA_SOURCE']) if not pd.isna(first_row['DATA_SOURCE']) else None
        )
        
        print(f"准备插入的数据:")
        for i, val in enumerate(test_data):
            print(f"  参数{i+1}: {val} (类型: {type(val)})")
        
        cursor.execute(test_sql, test_data)
        conn.commit()
        
        print("基础字段插入成功！")
        
        # 如果基础字段成功，再测试日期字段
        print("\n测试日期字段插入...")
        cursor.execute("DELETE FROM T_STATISTICAL_CUS_TOTAL_CS WHERE STAT_TYPE = :1", (str(first_row['STAT_TYPE']),))
        conn.commit()
        
        # 测试完整插入（使用TO_DATE函数）
        full_test_sql = """
        INSERT INTO T_STATISTICAL_CUS_TOTAL_CS (
            STAT_DATE, STAT_TYPE, STAT_NAME, STAT_CONTENT_RAW, STAT_CONTENT_CLEANSE,
            MON_A_CNY_AMOUNT, DATA_SOURCE, CREATE_TIME
        ) VALUES (
            TO_DATE(:1, 'YYYY-MM-DD'), :2, :3, :4, :5, :6, :7, TO_DATE(:8, 'YYYY-MM-DD HH24:MI:SS')
        )
        """
        
        full_test_data = (
            get_date_value(first_row['STAT_DATE']),
            str(first_row['STAT_TYPE']) if not pd.isna(first_row['STAT_TYPE']) else None,
            str(first_row['STAT_NAME']) if not pd.isna(first_row['STAT_NAME']) else None,
            str(first_row['STAT_CONTENT_RAW']) if not pd.isna(first_row['STAT_CONTENT_RAW']) else None,
            str(first_row['STAT_CONTENT_CLEANSE']) if not pd.isna(first_row['STAT_CONTENT_CLEANSE']) else None,
            float(first_row['MON_A_CNY_AMOUNT']) if not pd.isna(first_row['MON_A_CNY_AMOUNT']) else None,
            str(first_row['DATA_SOURCE']) if not pd.isna(first_row['DATA_SOURCE']) else None,
            get_datetime_value(first_row['CREATE_TIME'])
        )
        
        print(f"完整测试数据:")
        for i, val in enumerate(full_test_data):
            print(f"  参数{i+1}: {val} (类型: {type(val)})")
        
        cursor.execute(full_test_sql, full_test_data)
        conn.commit()
        
        print("单条记录插入成功！")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"测试插入失败: {e}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")
        return False

def batch_import_to_oracle(csv_dir, db_config, truncate_table=True, test_mode=False):
    """
    批量导入CSV文件到Oracle数据库
    """
    # 获取所有CSV文件
    csv_files = []
    for file in os.listdir(csv_dir):
        if file.endswith('.csv') and file.startswith('T_STATISTICAL_CUS_TOTAL_'):
            csv_files.append(file)
    
    if not csv_files:
        print(f"在目录 {csv_dir} 中没有找到CSV文件")
        return
    
    print(f"找到 {len(csv_files)} 个CSV文件")
    
    # 测试模式：只处理第一个文件的第一条记录
    if test_mode:
        csv_path = os.path.join(csv_dir, csv_files[0])
        print(f"测试模式：处理文件 {csv_files[0]}")
        return test_single_record(csv_path, db_config)
    
    # 统计信息
    total_files = 0
    successful_files = 0
    total_records = 0
    
    # 清空表（可选）
    if truncate_table:
        try:
            print("正在清空目标表...")
            conn = cx_Oracle.connect(**db_config)
            cursor = conn.cursor()
            cursor.execute("TRUNCATE TABLE T_STATISTICAL_CUS_TOTAL_CS")
            conn.commit()
            cursor.close()
            conn.close()
            print("目标表已清空")
        except Exception as e:
            print(f"清空表时出错: {e}")
            return
    
    # 按文件名排序导入
    for csv_file in sorted(csv_files):
        total_files += 1
        
        csv_path = os.path.join(csv_dir, csv_file)
        
        print(f"\n{'='*60}")
        print(f"导入文件 {total_files}/{len(csv_files)}: {csv_file}")
        print(f"{'='*60}")
        
        try:
            success, record_count = import_single_csv_to_oracle(csv_path, db_config)
            if success:
                successful_files += 1
                total_records += record_count
                print(f"成功导入: {csv_file} ({record_count} 条记录)")
            else:
                print(f"导入失败: {csv_file}")
        except Exception as e:
            print(f"导入出错 {csv_file}: {e}")
    
    print(f"\n{'='*60}")
    print("批量导入完成！")
    print(f"{'='*60}")
    print(f"总文件数: {total_files}")
    print(f"成功导入: {successful_files}")
    print(f"失败文件: {total_files - successful_files}")
    print(f"总记录数: {total_records}")
    
    # 验证数据
    if successful_files > 0:
        try:
            print("\n正在验证数据...")
            conn = cx_Oracle.connect(**db_config)
            cursor = conn.cursor()
            
            # 检查总记录数
            cursor.execute("SELECT COUNT(*) FROM T_STATISTICAL_CUS_TOTAL_CS")
            db_count = cursor.fetchone()[0]
            print(f"数据库中记录数: {db_count}")
            
            # 按统计类型分组统计
            cursor.execute("""
                SELECT STAT_TYPE, STAT_NAME, COUNT(*) 
                FROM T_STATISTICAL_CUS_TOTAL_CS 
                GROUP BY STAT_TYPE, STAT_NAME 
                ORDER BY STAT_TYPE
            """)
            
            print("\n按统计类型分组统计:")
            for row in cursor.fetchall():
                print(f"  {row[0]} ({row[1]}): {row[2]} 条记录")
            
            # 按日期分组统计
            cursor.execute("""
                SELECT TO_CHAR(STAT_DATE, 'YYYY-MM-DD'), COUNT(*) 
                FROM T_STATISTICAL_CUS_TOTAL_CS 
                GROUP BY STAT_DATE 
                ORDER BY STAT_DATE
            """)
            
            print("\n按日期分组统计:")
            for row in cursor.fetchall():
                print(f"  {row[0]}: {row[1]} 条记录")
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            print(f"验证数据时出错: {e}")

if __name__ == '__main__':
    # 数据库连接配置
    db_config = {
        'user': 'manifest_dcb',
        'password': 'manifest_dcb',
        'dsn': '192.168.1.151/TEST'
    }
    
    # CSV文件目录
    csv_directory = "批量转换结果_智能单位检测"
    
    print("=== 海关统计数据批量导入Oracle数据库 ===")
    print(f"CSV目录: {csv_directory}")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 先运行测试模式
    print("运行测试模式...")
    test_success = batch_import_to_oracle(csv_directory, db_config, truncate_table=False, test_mode=True)
    
    if test_success:
        print("\n测试成功！开始批量导入...")
        batch_import_to_oracle(csv_directory, db_config, truncate_table=True, test_mode=False)
    else:
        print("\n测试失败，请检查错误后重试")
    
    print(f"\n结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")