<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd"><html><head><link href="http://beijing.customs.gov.cn/eportal/uiFramework/huilan-jquery-ui/js/skins/default.css" rel="stylesheet" id="lhgdialoglink">
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">  <title>统计数据</title>
<meta name="version" content="7.9.12.42">
<meta name="createDate" content="2025-08-04 09:33:08">
<meta name="cacheclearDate" content="2025-08-04 09:04:00">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="ColumnName" content="统计数据">
<meta name="ColumnType" content="数据发布">
<meta name="SiteName" content="中华人民共和国北京海关"><!--网站名称-->
<meta name="SiteDomain" content="http://beijing.customs.gov.cn"><!--网站域名-->
<meta name="SiteIDCode" content="bm28020001"><!--网站标识-->
<meta name="ColumnDescription" content="初始化频道：统计数据"><!--栏目描述-->
<meta name="ColumnKeywords" content="统计数据"><!--栏目关键词--><link rel="stylesheet" type="text/css" href="/eportal/uiFramework/huilan-jquery-ui/css/huilan-jquery-ui.css">
<!--[if lt IE 9]><script r='m'>document.createElement("section")</script><![endif]--><script type="text/javascript" src="/eportal/uiFramework/huilan-jquery-ui/js/huilan-jquery-min.js"></script>
<script type="text/javascript" src="/eportal/uiFramework/huilan-jquery-ui/js/huilan-jquery-ui.js?self=true&amp;skin=default"></script><link rel="stylesheet" href="http://beijing.customs.gov.cn/eportal/uiFramework/huilan-jquery-ui/js/skin/layer.css" id="layui_layer_skinlayercss" style="">
 <link rel="shortcut icon" href="/eportal/fileDir/customs/resource/cms/img_pc_site/2016/10/2016102614421014941.ico" type="image/x-icon">
<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0,user-scalable=no"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0,user-scalable=no">    <link rel="stylesheet" type="text/css" href="/eportal/fileDir/beijing_customs/template/beijing_customs.css?timestamp=1681370456758">
  <script type="text/javascript" src="/eportal/fileDir/beijing_customs/template/beijing_customs.js?timestamp=1681370456758"></script>
 </head>
 <body style="
										
					"><div style="position: absolute; left: -9999em; top: 446px; visibility: visible; width: auto; z-index: 1976;"><table class="ui_border ui_state_visible ui_state_focus"><tbody><tr><td class="ui_lt"></td><td class="ui_t"></td><td class="ui_rt"></td></tr><tr><td class="ui_l"></td><td class="ui_c"><div class="ui_inner"><table class="ui_dialog"><tbody><tr><td colspan="2"><div class="ui_title_bar"><div class="ui_title" unselectable="on" style="cursor: move;">视窗 </div><div class="ui_title_buttons"><a class="ui_min" href="javascript:void(0);" title="最小化" style="display: inline-block;"><b class="ui_min_b"></b></a><a class="ui_max" href="javascript:void(0);" title="最大化" style="display: inline-block;"><b class="ui_max_b"></b></a><a class="ui_res" href="javascript:void(0);" title="还原"><b class="ui_res_b"></b><b class="ui_res_t"></b></a><a class="ui_close" href="javascript:void(0);" title="关闭(esc键)" style="display: inline-block;">×</a></div></div></td></tr><tr><td class="ui_icon" style="display: none;"></td><td class="ui_main" style="width: auto; height: auto;"><div class="ui_content" style="padding: 10px;"><div class="ui_loading"><span>loading...</span></div></div></td></tr><tr><td colspan="2"><div class="ui_buttons" style="display: none;"></div></td></tr></tbody></table></div></td><td class="ui_r"></td></tr><tr><td class="ui_lb"></td><td class="ui_b"></td><td class="ui_rb" style="cursor: se-resize;"></td></tr></tbody></table></div>
  <link rel="stylesheet" type="text/css" href="/eportal/fileDir/beijing_customs/template/page/zwgk_2021/skin.css?timestamp=1681370456758"> <div style="display:none" easysite="easysiteHiddenDiv">
	<input id="contextPath" value="/eportal" type="hidden">
	<input id="isOnlyUseCkeditorSourceMode" value="$isOnlyUseCkeditorSourceMode" type="hidden">
	<input id="eprotalCurrentPageId" value="434774" type="hidden">
	<input id="eprotalCurrentSiteId" value="434749" type="hidden">
	<input id="eprotalCurrentSiteName" value="北京海关" type="hidden">
	<input id="eprotalCurrentSiteEnname" value="beijing_customs" type="hidden">
	<input id="eprotalCurrentSiteType" value="WEB" type="hidden">
	<input id="eprotalCurrentSiteHideMaskLayer" value="no" type="hidden">
	<input id="eprotalCurrentArticleKey" value="" type="hidden">
	<input id="eprotalCurrentColumnId" value="" type="hidden">
	<input id="isStaticRequest" value="yes" type="hidden">
	<input id="isOpenStaticPageList" value="yes" type="hidden">
	<input id="defaultPublishPage" value="3" type="hidden">
	<input type="hidden" id="eportalappPortletId" value="3">
	<input type="hidden" id="epsPortletId" value="1">
	<input type="hidden" id="portaltoolsPortletId" value="2">
	<script type="text/javascript" src="/eportal/uiFramework/js/counting/chanelCounting.js"></script>
	<input type="hidden" id="currentLoginMemberId" value="">
	<input type="hidden" id="currentLoginMemberName" value="">
	<input type="hidden" id="behaviourAnalysisSiteId" value=""> 
			<input type="hidden" id="portalLastRequestUrl" value="">
</div>   <div class="content_img"> 
   <p class="position" style="display:none;"><span><a class="SkinObject" href="/beijing_customs/sy2022/index.html" target="_parent">首页</a>&nbsp;&gt;&nbsp;<a class="SkinObject" href="/beijing_customs/434756/434804/434805/index.html" target="_parent">政务公开</a>&nbsp;&gt;&nbsp;<a class="SkinObject" href="/beijing_customs/434756/434804/434805/index.html" target="_parent">政府信息公开</a>&nbsp;&gt;&nbsp;<a class="SkinObject" href="/beijing_customs/434756/434804/2941702/bggg50/index.html" target="_blank">法定主动公开内容</a>&nbsp;&gt;&nbsp;<a class="SkinObject" href="/beijing_customs/434756/434804/2941702/434773/434774/index.html" target="_parent">本关统计</a>&nbsp;&gt;&nbsp;<a class="SkinObject" href="/beijing_customs/434756/434804/2941702/434773/434774/index.html" target="_parent">统计数据</a></span></p> 
   <div class="gkmlTit pubCon"> 
    <a target="_blank" class="logo" href="/eportal/ui?pageId=4761666"><img src="/eportal/fileDir/beijing_customs/template/common/header_2020/logo.png?timestamp=1603853245685"></a> 
    <h2>政府信息公开<i></i></h2> 
   </div> 
   <!-- 内容区域 start --> 
   <div class="customs-body-bg"> 
    <div class="listCon"> 
     <!-- 搜索 start --> 
     <div class="searchzfxx"> 
      <form action="/eportal/ui?pageId=4474408&amp;currentPage=1&amp;moduleId=3b9adc315a4e426fa111fc5fe47720a3" id="hgfgForm" method="post"> 
       <input name="filter_LIKE_TITLE" placeholder="请输入搜索关键字" type="text"> 
       <button onclick="queryArticleByCondition(this,'/eportal/ui?pageId=4474408&amp;currentPage=1&amp;moduleId=3b9adc315a4e426fa111fc5fe47720a3')"></button> 
      </form> 
     </div> 
     <!-- 搜索 end --> 
     <!-- 左侧导航 start --> 
     <div class="listCon_L column" name="二级导航" runat="server" id="conLeft" role="navigation" aria-label="导航">
         <div class="portlet" id="5cec4b8656da4268a51579d3b6a083cb" pagemoduleid="d50a88690ea94b8e9e8a6228ef011e86">
 <div align="left" class="portlet-header" style="display: none;"> 
  <span id="menu">
        </span> 
  <div id="submenu5cec4b8656da4268a51579d3b6a083cb" class="shadow dn"> 
   <ul class="float_list_ul">
        </ul> 
  </div> 
 </div> 
 <div>
   <style>
.left_daohang{ width:100%; overflow:hidden;margin-top:-15px;}
.left_zhong{ width:100%; float:left; overflow:hidden;}
.left_zhong li.class_A{ overflow:hidden;list-style:none; margin-top:15px;  position:relative;border:1px solid #ddd;background:#fff;} 
.left_zhong .erji_ul{background:#fff;border:1px solid #ddd;border-top:0;}
.left_zhong .class_A a{ display:block;  padding-left:80px; height:65px; line-height:30px; text-decoration:none; color:rgb(45,102,165); font-size:23px;  padding-top:5px; }
.left_zhong .nobg{background:none;}
.left_zhong .class_A1 a{ display:block; padding-left:32px; line-height:33px; text-decoration:none; color:#545454; font-size:14px; font-weight:bold;  width:100%;}
.left_zhong .class_A1 a:hover{ display:block;  line-height:33px; color:#ffffff; width:100%;}
.left_zhong .class_A1 a:visited{ display:block;  line-height:33px; width:100%;}

.left_zhong .class_B a{display:block; padding:5px 0 5px 85px;line-height:32px; text-decoration:none; color:#333; font-size:15px; font-weight:normal;background:url("/eportal/fileDir/beijing_customs/template/navigation/zwgk_nav/2019101218033326565.png?timestamp=1584591948488") no-repeat 55px center; line-height:1.5em;}
.left_zhong .class_B a:hover,.left_zhong .class_B a.on{background:rgb(230,245,255) url("/eportal/fileDir/beijing_customs/template/navigation/zwgk_nav/2019101218033326565.png?timestamp=1584591948488") no-repeat 55px center;color:#333;}
.jiaClass{background: url("/eportal/fileDir/beijing_customs/template/navigation/zwgk_nav/opimg.png?timestamp=1584591948488");width:60px;height:70px;  position:absolute; background-position:center; background-repeat:no-repeat; cursor:pointer;right:0;top:0;}
.jianClass{background:url("/eportal/fileDir/beijing_customs/template/navigation/zwgk_nav/cnimg.png?timestamp=1584591948488");width:60px;height:70px; position:absolute;  background-position:center;background-repeat:no-repeat; cursor:pointer;right:0;top:0;}
.left_zhong li.li_1{background:url("/eportal/fileDir/beijing_customs/template/navigation/zwgk_nav/gkzl_icon02.png?timestamp=1584591948488") no-repeat 20px center;}
.left_zhong li.li_2{background:url("/eportal/fileDir/beijing_customs/template/navigation/zwgk_nav/gkzl_icon01.png?timestamp=1584591948488") no-repeat 20px center;}
.left_zhong li.li_3{background:url("/eportal/fileDir/beijing_customs/template/navigation/zwgk_nav/gkzl_icon05.png?timestamp=1584591948488") no-repeat 20px center;}
.left_zhong li.li_4{background:url("/eportal/fileDir/beijing_customs/template/navigation/zwgk_nav/gkzl_icon04.png?timestamp=1584591948488") no-repeat 20px center;}
.left_zhong li.li_5{background:url("/eportal/fileDir/beijing_customs/template/navigation/zwgk_nav/gkzl_icon03.png?timestamp=1584591948488") no-repeat 20px center;}
.left_zhong li.li_6{background:url("/eportal/fileDir/beijing_customs/template/navigation/zwgk_nav/gkzl_icon05.png?timestamp=1584591948488") no-repeat 20px center;}
.left_zhong li.li_7{background:url("/eportal/fileDir/beijing_customs/resource/cms/2021/06/img_pc_site/gkzl_icon06.png") no-repeat 20px center;}
.left_zhong li.li_8{background:url("/eportal/fileDir/beijing_customs/resource/cms/2021/06/img_pc_site/gkzl_icon06.png") no-repeat 20px center;}
.left_zhong li:hover{background:rgb(45,102,165);}
.left_zhong li.li_1:hover{background:rgb(45,102,165) url("/eportal/fileDir/beijing_customs/template/navigation/zwgk_nav/gkzl_icon02b.png?timestamp=1584591948488") no-repeat 20px center;}
.left_zhong li.li_2:hover{background:rgb(45,102,165) url("/eportal/fileDir/beijing_customs/template/navigation/zwgk_nav/gkzl_icon01b.png?timestamp=1584591948488") no-repeat 20px center;}
.left_zhong li.li_3:hover{background:rgb(45,102,165) url("/eportal/fileDir/beijing_customs/template/navigation/zwgk_nav/gkzl_icon05b.png?timestamp=1584591948488") no-repeat 20px center;}
.left_zhong li.li_4:hover{background:rgb(45,102,165) url("/eportal/fileDir/beijing_customs/template/navigation/zwgk_nav/gkzl_icon04b.png?timestamp=1584591948488") no-repeat 20px center;}
.left_zhong li.li_5:hover{background:rgb(45,102,165) url("/eportal/fileDir/beijing_customs/template/navigation/zwgk_nav/gkzl_icon03b.png?timestamp=1584591948488") no-repeat 20px center;}
.left_zhong li.li_6:hover{background:rgb(45,102,165) url("/eportal/fileDir/beijing_customs/template/navigation/zwgk_nav/gkzl_icon05b.png?timestamp=1584591948488") no-repeat 20px center;}
.left_zhong li.li_7:hover{background:rgb(45,102,165) url("/eportal/fileDir/beijing_customs/resource/cms/2021/06/img_pc_site/gkzl_icon06b.png") no-repeat 20px center;}
.left_zhong li.li_8:hover{background:rgb(45,102,165) url("/eportal/fileDir/beijing_customs/resource/cms/2021/06/img_pc_site/gkzl_icon06b.png") no-repeat 20px center;}
.left_zhong li:hover a{color:#fff;}
.left_zhong li:hover .jiaClass{background:url("/eportal/fileDir/beijing_customs/template/navigation/zwgk_nav/jia_white.png?timestamp=1584591948488") no-repeat 20px center;}
.left_zhong li[cur=on1]{background:rgb(45,102,165) url("/eportal/fileDir/beijing_customs/template/navigation/zwgk_nav/gkzl_icon02b.png?timestamp=1584591948488") no-repeat 20px center;}
.left_zhong li[cur=on1] a{color:#fff;}
.left_zhong li[cur=on1] .jiaClass{background:url("/eportal/fileDir/beijing_customs/template/navigation/zwgk_nav/jia_white.png?timestamp=1584591948488") no-repeat 20px center;}
.left_zhong li[cur=on2]{background:rgb(45,102,165) url("/eportal/fileDir/beijing_customs/template/navigation/zwgk_nav/gkzl_icon01b.png?timestamp=1584591948488") no-repeat 20px center;}
.left_zhong li[cur=on2] a{color:#fff;}
.left_zhong li[cur=on2] .jiaClass{background:url("jia_white") no-repeat 20px center;}
.left_zhong li[cur=on3]{background:rgb(45,102,165) url("/eportal/fileDir/beijing_customs/template/navigation/zwgk_nav/gkzl_icon05b.png?timestamp=1584591948488") no-repeat 20px center;}
.left_zhong li[cur=on3] a{color:#fff;}
.left_zhong li[cur=on3] .jiaClass{background:url("/eportal/fileDir/beijing_customs/template/navigation/zwgk_nav/jia_white.png?timestamp=1584591948488") no-repeat 20px center;}
.left_zhong li[cur=on4]{background:rgb(45,102,165) url("/eportal/fileDir/beijing_customs/template/navigation/zwgk_nav/gkzl_icon04b.png?timestamp=1584591948488") no-repeat 20px center;}
.left_zhong li[cur=on4] a{color:#fff;}
.left_zhong li[cur=on4] .jiaClass{background:url("/eportal/fileDir/beijing_customs/template/navigation/zwgk_nav/jia_white.png?timestamp=1584591948488") no-repeat 20px center;}
.left_zhong li[cur=on5]{background:rgb(45,102,165) url("/eportal/fileDir/beijing_customs/template/navigation/zwgk_nav/gkzl_icon03b.png?timestamp=1584591948488") no-repeat 20px center;}
.left_zhong li[cur=on5] a{color:#fff;}
.left_zhong li[cur=on5] .jiaClass{background:url("/eportal/fileDir/beijing_customs/template/navigation/zwgk_nav/jia_white.png?timestamp=1584591948488") no-repeat 20px center;}
.left_zhong li[cur=on6]{background:rgb(45,102,165) url("/eportal/fileDir/beijing_customs/template/navigation/zwgk_nav/gkzl_icon05b.png?timestamp=1584591948488") no-repeat 20px center;}
.left_zhong li[cur=on6] a{color:#fff;}
.left_zhong li[cur=on6] .jiaClass{background:url("/eportal/fileDir/beijing_customs/template/navigation/zwgk_nav/jia_white.png?timestamp=1584591948488") no-repeat 20px center;}

.left_zhong li[cur=on7]{background:rgb(45,102,165) url("/eportal/fileDir/beijing_customs/resource/cms/2021/06/img_pc_site/gkzl_icon06b.png") no-repeat 20px center;}
.left_zhong li[cur=on7] a{color:#fff;}
.left_zhong li[cur=on7] .jiaClass{background:url("/eportal/fileDir/beijing_customs/template/navigation/zwgk_nav/jia_white.png?timestamp=1584591948488") no-repeat 20px center;}

.left_zhong li[cur=on8]{background:rgb(45,102,165) url("/eportal/fileDir/beijing_customs/resource/cms/2021/06/img_pc_site/gkzl_icon06b.png") no-repeat 20px center;}
.left_zhong li[cur=on8] a{color:#fff;}
.left_zhong li[cur=on8] .jiaClass{background:url("/eportal/fileDir/beijing_customs/template/navigation/zwgk_nav/jia_white.png?timestamp=1584591948488") no-repeat 20px center;}
/* 适配代码 */
@media screen and (max-width:1199px) {
	.listCon_L { text-indent: 0; }
	.left_zhong li.class_A { background-position: 10px center !important; display: flex; align-items: center; }
	.left_zhong .class_A a { padding-left: 50px; padding-top: 0; }
	.jianClass { height: 65px; }
	.left_zhong .class_B a { padding-left: 50px; background-position: 20px center !important; }
}
@media screen and (max-width:1024px) {
	.left_zhong li.class_A { margin-top: 10px; }
	.left_zhong .class_A a br { display: none; }
	.left_zhong .class_A a { line-height: 65px; }
}
@media screen and (max-width:460px) {
	.jiaClass, .jianClass { width: 40px; height: 40px; }
	.left_zhong li.class_A { background-size: 24px !important; }
	.left_zhong .class_A a { height: 40px; line-height: 40px !important; }
	.left_zhong .class_B a { line-height: 1.5em; }
}
</style>

<div class="left_daohang" role="navigation" aria-label="导航">
<div class="left_zhong">
    <ul>
          <li class="class_A li_1" id="parent_4968512">
          <div class="jiaClass" onclick="f(this)"></div><a href="http://www.customs.gov.cn/customs/302249/zfxxgk/4098653/zc92/index.html" target="_parent" style="line-height: 60px;">政策</a></li>
           <ul style="display:none" class="erji_ul">
                            <li class="class_B" parentid="parent_4968512" num="1" id="self_4968523">
                <a href="http://www.customs.gov.cn/customs/302249/zfxxgk/4098653/zc92/index.html" target="_parent">海关规章库</a></li> 
                            </ul>
               <li class="class_A li_2" id="parent_434805">
          <div class="jiaClass" onclick="f(this)" style="background:none;"></div><a href="/beijing_customs/434756/434804/434805/index.html" target="_parent">政府信息<br>公开指南</a></li>
           <ul style="display:none" class="erji_ul">
                          </ul>
               <li class="class_A li_3" id="parent_434807">
          <div class="jiaClass" onclick="f(this)" style="background:none;"></div><a href="/beijing_customs/434756/434804/434807/index.html" target="_parent">政府信息<br>公开制度</a></li>
           <ul style="display:none" class="erji_ul">
                          </ul>
               <li class="class_A li_4" id="parent_2941702" cur="on4">
          <div class="jianClass" onclick="f(this)"></div><a href="/beijing_customs/434756/434804/2941702/bggg50/index.html" target="_blank">法定主动<br>公开内容</a></li>
           <ul class="erji_ul">
                            <li class="class_B" parentid="parent_2941702" num="1" id="self_3135872">
                <a href="/beijing_customs/434756/434804/2941702/bggg50/index.html" target="_parent">京关通告</a></li> 
                              <li class="class_B" parentid="parent_2941702" num="2" id="self_3135995">
                <a href="/beijing_customs/434756/434804/2941702/3135995/index.html" target="_parent">最新消息</a></li> 
                              <li class="class_B" parentid="parent_2941702" num="3" id="self_3136008">
                <a href="http://www.customs.gov.cn/customs/302249/zfxxgk/2799825/2799837/index.html" target="_parent">履职依据</a></li> 
                              <li class="class_B" parentid="parent_2941702" num="4" id="self_434783">
                <a href="/beijing_customs/434756/434804/2941702/434783/434784/index.html" target="_parent">人事信息</a></li> 
                              <li class="class_B" parentid="parent_2941702" num="5" id="self_3136068">
                <a href="/beijing_customs/434756/434804/2941702/3136068/index.html" target="_parent">建议提案</a></li> 
                              <li class="class_B" parentid="parent_2941702" num="6" id="self_434776">
                <a href="/beijing_customs/434756/434804/2941702/434776/434777/index.html" target="_parent">计划总结</a></li> 
                              <li class="class_B" parentid="parent_2941702" num="7" id="self_4977956">
                <a href="/beijing_customs/434756/434804/2941702/4977956/index.html" target="_parent">拍卖信息</a></li> 
                              <li class="class_B" parentid="parent_2941702" num="8" id="self_434779">
                <a href="/beijing_customs/434756/434804/2941702/434779/434780/index.html" target="_parent">政府采购</a></li> 
                              <li class="class_B" parentid="parent_2941702" num="9" id="self_434816">
                <a href="/beijing_customs/434756/434804/2941702/434816/index.html" target="_parent">财政信息</a></li> 
                              <li class="class_B" parentid="parent_2941702" num="10" id="self_434773">
                <a class="on" href="/beijing_customs/434756/434804/2941702/434773/434774/index.html" target="_parent">本关统计</a></li> 
                              <li class="class_B" parentid="parent_2941702" num="11" id="self_2276032">
                <a href="/beijing_customs/434756/434804/2941702/wzgl66/index.html" target="_parent">网站管理</a></li> 
                              <li class="class_B" parentid="parent_2941702" num="12" id="self_3327434">
                <a href="/beijing_customs/434756/434804/2941702/3327434/index.html" target="_parent">双随机、一公开</a></li> 
                              <li class="class_B" parentid="parent_2941702" num="13" id="self_3412592">
                <a href="/beijing_customs/434756/434804/2941702/3412592/3892348/index.html" target="_parent">行政执法公示</a></li> 
                              <li class="class_B" parentid="parent_2941702" num="14" id="self_434810">
                <a href="/beijing_customs/434756/434804/2941702/434810/index.html" target="_parent">应急管理</a></li> 
                              <li class="class_B" parentid="parent_2941702" num="15" id="self_4019036">
                <a href="/beijing_customs/434756/434804/2941702/4019036/index.html" target="_parent">动植物检疫企业名单</a></li> 
                              <li class="class_B" parentid="parent_2941702" num="16" id="self_4766566">
                <a href="/beijing_customs/434756/434804/2941702/4766566/index.html" target="_parent">信用类文书公告送达</a></li> 
                            </ul>
               <li class="class_A li_5" id="parent_434808">
          <div class="jiaClass" onclick="f(this)"></div><a href="/beijing_customs/434756/434804/434808/3663893/index.html" target="_parent">政府信息<br>公开年报</a></li>
           <ul style="display:none" class="erji_ul">
                            <li class="class_B" parentid="parent_434808" num="1" id="self_3663893">
                <a href="/beijing_customs/434756/434804/434808/3663893/index.html" target="_parent">关区信息公开年报</a></li> 
                              <li class="class_B" parentid="parent_434808" num="2" id="self_3663896">
                <a href="/beijing_customs/434756/434804/434808/3663896/index.html" target="_parent">本级机关信息公开年报</a></li> 
                              <li class="class_B" parentid="parent_434808" num="3" id="self_3663898">
                <a href="/beijing_customs/434756/434804/434808/3663898/index.html" target="_parent">隶属海关信息公开年报</a></li> 
                            </ul>
               <li class="class_A li_6" id="parent_3677575">
          <div class="jiaClass" onclick="f(this)" style="background:none;"></div><a href="/beijing_customs/434756/434804/zfxxgkml6938/index.html" target="_parent">政府信息<br>公开目录</a></li>
           <ul style="display:none" class="erji_ul">
                          </ul>
               <li class="class_A li_7" id="parent_434809">
          <div class="jiaClass" onclick="f(this)" style="background:none;"></div><a href="/beijing_customs/434756/434804/434809/index.html" target="_parent">政府信息<br>公开申请</a></li>
           <ul style="display:none" class="erji_ul">
                          </ul>
               <li class="class_A li_8" id="parent_3680477">
          <div class="jiaClass" onclick="f(this)" style="background:none;"></div><a href="/beijing_customs/434756/434804/3680477/index.html" target="_parent">政务公开<br>工作要点</a></li>
           <ul style="display:none" class="erji_ul">
                          </ul>
       </ul>
</div>
</div>
<script type="text/javascript">
function f(div){
  var $ul=$(div).parent().next();
    if($ul.is(":hidden")){
      $ul.show();
  }else{
    $ul.hide();
  }
  if($(div).attr("class")=="jiaClass"){
    $(div).attr("class","jianClass");
  }else{
    $(div).attr("class","jiaClass");
  }
}



</script>


<script type="text/javascript">
$('.listcontent .portlet').eq(0).css({paddingTop: 0,borderTop:0})

$(".listCon_Nav li:last").find('a').css('border','0');

$('.dl_nav01 h3').eq(0).css('margin-top','0')
  $('.class_A a').each(function(){     
     var str=$(this).text();
     var strNum=str.length
     if(strNum >= 8){ //调整导航的高度
         $(this).html(str.substr(0,4)+'<br>'+str.substr(4,10))
     }else{
        $(this).css('line-height','60px')
     }
  })
</script>

<!--重写左侧导航样式 -->
<style type="text/css">
.listCon_L {
text-indent:0px;

}</style>
 
 </div> 
</div>       </div> 
     <!-- 左侧导航 end --> 
     <!-- 右侧内容区域 start --> 
     <div class="listCon_R column" name="二级内容区域" runat="server" id="conRight" role="region" aria-label="统计数据">
         <div class="portlet" id="275eca2e2a3044fa8dcc4fa5a2aa7cbd" pagemoduleid="0ae13c9fa61d48ec96f02251d1787dac">
 <div align="left" class="portlet-header" style="display: none;"> 
  <span id="menu">
        </span> 
  <div id="submenu275eca2e2a3044fa8dcc4fa5a2aa7cbd" class="shadow dn"> 
   <ul class="float_list_ul">
        </ul> 
  </div> 
 </div> 
 <div>
   <style type="text/css">
.nav_sj { overflow: hidden; /*width: 106%;*/width: 100%; }
/*.nav_sj li { width: 250px; text-align: center; height: 35px; line-height: 35px; background: #eaeaea; float: left; margin-right: 46px; margin-bottom: 30px; }*/
.nav_sj li { width: 23.5% !important; text-align: center; height: 35px; line-height: 35px; background: #eaeaea; float: left; margin-right: 2% !important; margin-bottom: 30px; }
.nav_sj li:nth-child(4n+4){margin-right:0  !important;}
.nav_sj li a { font-size: 16px; color: #005684; }
.nav_sj li.on { background: #005684; box-shadow: 0 0 21px rgba(0, 0, 0, 0.25); }
.nav_sj li.on a { color: #fff; }

@media screen and (max-width:1199px) {
.nav_sj { width: 100%; display: flex; flex-wrap: wrap; }
}


@media screen and (max-width:800px) {
.nav_sj li { width: 100%; }
}
@media screen and (max-width:450px) {
.nav_sj li{margin-bottom: 10px;width: 100%!important;}
}
</style>
<ul class="nav_sj">
					<li class="on"><a href="/beijing_customs/434756/434804/2941702/434773/434774/index.html" target="_parent">统计数据</a></li>
							<li><a href="/beijing_customs/434756/434804/2941702/434773/434775/index.html" target="_parent">统计分析</a></li>
							<li><a href="/beijing_customs/434756/434804/2941702/434773/3892365/index.html" target="_parent">统计图表</a></li>
			</ul>
<script type="text/javascript">
$(".nav_sj li").each(function(index, el) {
	if ($(this).text().length>=7 && $(this).text().length<=14 ) {
		//$(this).css("width","250px");
		$(this).css("margin-right","0");
	}else if($(this).text().length>14 ) {
		//$(this).css("width","380px");
	}
});
</script>
 
 </div> 
</div>  <link rel="stylesheet" type="text/css" href="/eportal/fileDir/beijing_customs/template/module/list_mk/container.css?timestamp=1603251587357"> 
<div id="1667380986bc42c583c65be8d74da7d1" class=" portlet" style="$portletStyle" pagemoduleid="16a69ea254ed4e6cbb8a2d40c224f61b"> 
 <h1><span>统计数据<i></i></span> 
  <div align="left" class="portlet-header" style="display: none;"> 
   <span id="menu"></span> 
   <div id="submenu1667380986bc42c583c65be8d74da7d1" class="shadow dn"> 
    <ul class="float_list_ul">
          </ul> 
   </div> 
  </div></h1> 


<p class="tipsCon" style="color: #054a83;font-weight: bold;" opentype="page">提示：统计原始资料在数据公布后发生变化的，已公布数据与最新公布数据之间可能出现不一致，以最新公布数据为准。</p> 
<ul class="conList_ul" role="region" aria-label="统计数据"> 
 <li><a href="/beijing_customs/434756/434804/2941702/434773/434774/5838290/index.html" onclick="void(0)" target="_blank" title="（3）北京地区进出口商品构成表（2024年1-3月）" istitle="true">（3）北京地区进出口商品构成表（2024年1-3月）</a><span>2024-04-25</span></li> 
 <li><a href="/beijing_customs/434756/434804/2941702/434773/434774/5838287/index.html" onclick="void(0)" target="_blank" title="（2）北京地区进出口商品国别（地区）总值表（2024年1-3月）" istitle="true">（2）北京地区进出口商品国别（地区）总值表（2024年1-3月）</a><span>2024-04-25</span></li> 
 <li><a href="/beijing_customs/434756/434804/2941702/434773/434774/5838243/index.html" onclick="void(0)" target="_blank" title="（1）北京地区进出口商品总值表B：月度表（2024年1-3月）" istitle="true">（1）北京地区进出口商品总值表B：月度表（2024年1-3月）</a><span>2024-04-25</span></li> 
 <li><a href="/beijing_customs/434756/434804/2941702/434773/434774/5838240/index.html" onclick="void(0)" target="_blank" title="（1）北京地区进出口商品总值表A：年度表（2008-2023）" istitle="true">（1）北京地区进出口商品总值表A：年度表（2008-2023）</a><span>2024-04-25</span></li> 
 <li><a href="/beijing_customs/434756/434804/2941702/434773/434774/5773602/index.html" onclick="void(0)" target="_blank" title="（13）北京地区部分进口商品主要贸易方式量值表（2024年1-2月）" istitle="true">（13）北京地区部分进口商品主要贸易方式量值表（2024年1-2月）</a><span>2024-03-22</span></li> 
 <li><a href="/beijing_customs/434756/434804/2941702/434773/434774/5773599/index.html" onclick="void(0)" target="_blank" title="（12）北京地区部分出口商品主要贸易方式量值表（2024年1-2月）" istitle="true">（12）北京地区部分出口商品主要贸易方式量值表（2024年1-2月）</a><span>2024-03-22</span></li> 
 <li><a href="/beijing_customs/434756/434804/2941702/434773/434774/5773593/index.html" onclick="void(0)" target="_blank" title="（11）北京地区对部分国家（地区）进口商品类章金额表（2024年1-2月）" istitle="true">（11）北京地区对部分国家（地区）进口商品类章金额表（2024年1-2月）</a><span>2024-03-22</span></li> 
 <li><a href="/beijing_customs/434756/434804/2941702/434773/434774/5773590/index.html" onclick="void(0)" target="_blank" title="（10）北京地区对部分国家（地区）出口商品类章金额表（2024年1-2月）" istitle="true">（10）北京地区对部分国家（地区）出口商品类章金额表（2024年1-2月）</a><span>2024-03-22</span></li> 
 <li><a href="/beijing_customs/434756/434804/2941702/434773/434774/5773587/index.html" onclick="void(0)" target="_blank" title="（9）北京地区进口主要商品量值表（2024年1-2月）" istitle="true">（9）北京地区进口主要商品量值表（2024年1-2月）</a><span>2024-03-22</span></li> 
 <li><a href="/beijing_customs/434756/434804/2941702/434773/434774/5773542/index.html" onclick="void(0)" target="_blank" title="（8）北京地区出口主要商品量值表（2024年1-2月）" istitle="true">（8）北京地区出口主要商品量值表（2024年1-2月）</a><span>2024-03-22</span></li> 
</ul> 
<div class="gg_page"> 
 <div class="easysite-page-wrap"> 
  <a style="cursor:pointer" onclick="queryArticleByCondition(this,'/beijing_customs/434756/434804/2941702/434773/434774/16673809-1.html')" tagname="/beijing_customs/434756/434804/2941702/434773/434774/16673809-1.html" title="首页">首页</a> 
  <a style="cursor:pointer" onclick="queryArticleByCondition(this,'/eportal/ui?pageId=434774&amp;currentPage=22&amp;moduleId=1667380986bc42c583c65be8d74da7d1&amp;staticRequest=yes')" tagname="/eportal/ui?pageId=434774&amp;currentPage=22&amp;moduleId=1667380986bc42c583c65be8d74da7d1&amp;staticRequest=yes" title="上一页">上一页</a> 
  <a style="cursor:pointer" title="下一页" onclick="queryArticleByCondition(this,'/eportal/ui?pageId=434774&amp;currentPage=24&amp;moduleId=1667380986bc42c583c65be8d74da7d1&amp;staticRequest=yes')" tagname="/eportal/ui?pageId=434774&amp;currentPage=24&amp;moduleId=1667380986bc42c583c65be8d74da7d1&amp;staticRequest=yes">下一页</a> 
  <a style="cursor:pointer" title="尾页" onclick="queryArticleByCondition(this,'/eportal/ui?pageId=434774&amp;currentPage=189&amp;moduleId=1667380986bc42c583c65be8d74da7d1&amp;staticRequest=yes')" tagname="/eportal/ui?pageId=434774&amp;currentPage=189&amp;moduleId=1667380986bc42c583c65be8d74da7d1&amp;staticRequest=yes">尾页</a> 
 </div> 
 <div class="easysite-total-page"> 
  <span>当前页:<font color="red"><b>23</b></font>/<b>189</b></span> 
 </div> 
 <div class="easysite-jump-page"> 
  <span>跳转至</span> 
  <input value="" size="5" class="easysite-page-text" onkeydown="jumpToPage(event,this,'189','23','/eportal/ui?pageId=434774&amp;currentPage=%1&amp;moduleId=1667380986bc42c583c65be8d74da7d1||/beijing_customs/434756/434804/2941702/434773/434774/16673809-%1.html')" type="text">页 
  <input class="easysite-jump-btn" value="跳转" onclick="jumpTo(this,'189','23','/eportal/ui?pageId=434774&amp;currentPage=%1&amp;moduleId=1667380986bc42c583c65be8d74da7d1||/beijing_customs/434756/434804/2941702/434773/434774/16673809-%1.html')" type="button"> 
 </div> 
</div> 
<input type="hidden" name="article_paging_list_hidden" moduleid="1667380986bc42c583c65be8d74da7d1" modulekey="1667380986bc42c583c65be8d74da7d1" totalpage="189">
 
</div>       </div> 
     <!-- 右侧内容区域 end --> 
     <div class="clear"></div> 
    </div> 
   </div> 
   <!-- 内容区域 end --> 
   <!-- 尾部 start --> <!-- 底部 start -->
<div class="customs-foot clearfix">
	<div class="customs-foot-link pubCon" "="" role="region" aria-label="相关链接">
		<span>相关链接</span>
		 <select name="">
            <option value="">中国政府网</option>
            <option value="http://www.gov.cn">中国政府网</option>
            <option value="http://zygjjg.12388.gov.cn">中央国家机关举报网站</option>
            <option value="http://www.locpg.gov.cn">中央人民政府驻香港特别行政区联络办公室</option>
        </select>
        <select name="">
            <option value="">国务院部门</option>
            <option value="http://www.fmprc.gov.cn/web/">外交部</option>
            <option value="http://www.mod.gov.cn/">国防部</option>
            <option value="http://www.ndrc.gov.cn/">国家发展和改革委员会</option>
            <option value="http://www.moe.gov.cn/">教育部</option>
            <option value="http://www.most.gov.cn/">科学技术部</option>
            <option value="http://www.miit.gov.cn/">工业和信息化部</option>
            <option value="http://www.seac.gov.cn/">国家民族事务委员会</option>
            <option value="http://www.mps.gov.cn/">公安部</option>
            <option value="" disabled="disabled">国家安全部</option>
            <option value="http://www.mca.gov.cn/">民政部</option>
            <option value="http://www.moj.gov.cn/">司法部</option>
            <option value="http://www.mof.gov.cn/index.htm">财政部</option>
            <option value="http://www.mohrss.gov.cn/">人力资源和社会保障部</option>
            <option value="http://www.mnr.gov.cn/">自然资源部</option>
            <option value="http://www.mee.gov.cn/">生态环境部</option>
            <option value="http://www.mohurd.gov.cn/">住房和城乡建设部</option>
            <option value="http://www.mot.gov.cn/">交通运输部</option>
            <option value="http://www.mwr.gov.cn/">水利部</option>
            <option value="http://www.moa.gov.cn/">农业农村部</option>
            <option value="http://www.mofcom.gov.cn/">商务部</option>
            <option value="http://www.mct.gov.cn/">文化和旅游部</option>
            <option value="http://www.nhc.gov.cn/">国家卫生健康委员会</option>
            <option value="http://www.mva.gov.cn/">退役军人事务部</option>
            <option value="http://www.mem.gov.cn">应急管理部</option>
            <option value="http://www.pbc.gov.cn/">人民银行</option>
            <option value="http://www.audit.gov.cn/">审计署</option>
            <option value="http://www.moe.gov.cn/jyb_sy/China_Language/">国家语言文字工作委员会</option>
            <option value="" disabled="disabled">国家外国专家局</option>
            <option value="http://www.cnsa.gov.cn/">国家航天局</option>
            <option value="http://www.caea.gov.cn/">国家原子能机构</option>
            <option value="" disabled="disabled">国家海洋局</option>
            <option value="http://nnsa.mee.gov.cn/">国家核安全局</option>
            <option value="http://www.sasac.gov.cn/">国务院国有资产监督管理委员会</option>
            <option value="http://www.customs.gov.cn/">海关总署</option>
            <option value="http://www.chinatax.gov.cn/">国家税务总局</option>
            <option value="https://www.samr.gov.cn/">国家市场监督管理总局</option>
            <option value="http://www.nrta.gov.cn/">国家广播电视总局</option>
            <option value="http://www.sport.gov.cn/">国家体育总局</option>
            <option value="http://www.stats.gov.cn/">国家统计局</option>
            <option value="http://www.cidca.gov.cn/">国家国际发展合作署</option>
            <option value="http://www.nhsa.gov.cn/">国家医疗保障局</option>
            <option value="http://www.counsellor.gov.cn/">国务院参事室</option>
            <option value="http://www.ggj.gov.cn/">国家机关事务管理局</option>
            <option value="http://www.cnca.gov.cn/">国家认证认可监督管理委员会</option>
            <option value="http://www.sac.gov.cn/">国家标准化管理委员会</option>
            <option value="http://www.ncac.gov.cn/">国家新闻出版署（国家版权局）</option>
            <option value="" disabled="disabled">国家宗教事务局</option>
            <option value="http://www.hmo.gov.cn/">国务院港澳事务办公室</option>
            <option value="http://www.gov.cn/guoqing/2018-06/22/content_5300522.htm">国务院研究室</option>
            <option value="http://www.gqb.gov.cn/">国务院侨务办公室</option>
            <option value="http://www.gwytb.gov.cn/">国务院台湾事务办公室</option>
            <option value="http://www.cac.gov.cn/">国家互联网信息办公室</option>
            <option value="http://www.scio.gov.cn/index.htm">国务院新闻办公室</option>
            <option value="http://203.192.6.89/xhs/">新华通讯社</option>
            <option value="http://www.cas.ac.cn/">中国科学院</option>
            <option value="http://www.cass.cn/">中国社会科学院</option>              
            <option value="http://www.cae.cn/">中国工程院</option>
            <option value="http://www.drc.gov.cn/">国务院发展研究中心</option>
            <option value="" disabled="disabled">中央广播电视总台</option>
            <option value="http://www.cma.gov.cn/">中国气象局</option>
            <option value="http://www.cbrc.gov.cn/">中国银行保险监督管理委员会</option>
            <option value="http://www.csrc.gov.cn/pub/newsite/">中国证券监督管理委员会</option>
            <option value="http://www.ccps.gov.cn/">国家行政学院</option>
            <option value="http://www.gjxfj.gov.cn/">国家信访局</option>
            <option value="http://www.lswz.gov.cn/">国家粮食和物资储备局</option>
            <option value="http://www.nea.gov.cn/">国家能源局</option>
            <option value="http://www.sastind.gov.cn/">国家国防科技工业局</option>
            <option value="http://www.tobacco.gov.cn/html/">国家烟草专卖局</option>
            <option value="https://www.nia.gov.cn/">国家移民管理局</option>
            <option value="http://www.forestry.gov.cn/">国家林业和草原局</option>
            <option value="http://www.nra.gov.cn/">国家铁路局</option>
            <option value="http://www.caac.gov.cn/index.html">中国民用航空局</option>
            <option value="http://www.spb.gov.cn/">国家邮政局</option>
            <option value="http://www.ncha.gov.cn/">国家文物局</option>
            <option value="http://www.natcm.gov.cn/">国家中医药管理局</option>
            <option value="http://www.chinamine-safety.gov.cn/">国家矿山安全监察局</option>
            <option value="https://www.safe.gov.cn/">国家外汇管理局</option>
            <option value="https://www.nmpa.gov.cn/">国家药品监督管理局</option>
            <option value="http://www.cnipa.gov.cn">国家知识产权局</option>
            <option value="" disabled="disabled">出入境管理局</option>
            <option value="http://www.forestry.gov.cn/">国家公园管理局</option>
            <option value="http://www.scs.gov.cn/">国家公务员局</option>
            <option value="http://www.saac.gov.cn/">国家档案局</option>
            <option value="http://www.gjbmj.gov.cn">国家保密局</option>
            <option value="http://www.oscca.gov.cn/">国家密码管理局</option>   
        </select>
        <select name="">
            <option value="">地方政府</option>
            <option value="http://www.beijing.gov.cn">北京市</option>
            <option value="http://www.shanghai.gov.cn">上海市</option>
            <option value="http://www.tj.gov.cn">天津市</option>
            <option value="http://www.cq.gov.cn">重庆市</option>
            <option value="http://www.hlj.gov.cn">黑龙江</option>
            <option value="http://www.jl.gov.cn">吉林省</option>
            <option value="http://www.ln.gov.cn">辽宁省</option>
            <option value="http://www.hebei.gov.cn">河北省</option>
            <option value="http://www.shanxi.gov.cn">山西省</option>
            <option value="http://www.jiangsu.gov.cn">江苏省</option>
            <option value="http://www.zhejiang.gov.cn">浙江省</option>
            <option value="http://www.ah.gov.cn">安徽省</option>
            <option value="http://www.fujian.gov.cn">福建省</option>
            <option value="http://www.jiangxi.gov.cn">江西省</option>
            <option value="http://www.sd.gov.cn">山东省</option>
            <option value="http://www.henan.gov.cn">河南省</option>
            <option value="http://www.hubei.gov.cn">湖北省</option>
            <option value="http://www.hunan.gov.cn">湖南省</option>
            <option value="http://www.gd.gov.cn">广东省</option>
            <option value="http://www.hainan.gov.cn">海南省</option>
            <option value="http://www.sc.gov.cn/">四川省</option>
            <option value="http://www.gzgov.gov.cn">贵州省</option>
            <option value="http://www.yn.gov.cn">云南省</option>
            <option value="http://www.shaanxi.gov.cn">陕西省</option>
            <option value="http://www.gansu.gov.cn">甘肃省</option>
            <option value="http://www.qinghai.gov.cn/">青海省</option>
            <option value="http://www.nmg.gov.cn">内蒙古</option>
            <option value="http://www.nx.gov.cn/">宁 夏</option>
            <option value="http://www.xj.gov.cn/">新 疆</option>
            <option value="http://www.gxzf.gov.cn">广 西</option>
            <option value="http://www.xizang.gov.cn/">西 藏</option>
            <option value="http://www.gov.hk/">香 港</option>
            <option value="http://www.gov.mo/">澳 门</option>
        </select>
        <select name="">
            <option value="">分署和司局子站</option>
            <!--<option value="http://bgtkab.customs.gov.cn">办公厅（国家口岸管理办公室）</option>-->
                        <option value="http://gdfs.customs.gov.cn">广东分署</option>
            <option value="http://bgt.customs.gov.cn">办公厅</option>
            <option value="http://gkb.customs.gov.cn">国家口岸管理办公室</option>
            <option value="http://zfs.customs.gov.cn">政策法规司</option>
            <option value="http://zhs.customs.gov.cn">综合业务司</option>
            <option value="http://zms.customs.gov.cn">自贸区和特殊区域发展司</option>
            <option value="http://fxs.customs.gov.cn">风险管理司</option>
            <option value="http://gszgs.customs.gov.cn">关税征管司</option>
            <option value="http://wss.customs.gov.cn">卫生检疫司</option>
            <option value="http://dzs.customs.gov.cn">动植物检疫司</option>
            <option value="http://jckspj.customs.gov.cn">进出口食品安全局</option>
            <option value="http://sjs.customs.gov.cn">商品检验司</option>
            <option value="http://jgs.customs.gov.cn">口岸监管司</option>
            <option value="http://tjs.customs.gov.cn">统计分析司</option>
            <option value="http://qgjcs.customs.gov.cn">企业管理和稽查司</option>
            <option value="http://jsj.customs.gov.cn">缉私局</option>
            <option value="http://gjs.customs.gov.cn">国际合作司</option>
            <option value="http://cws.customs.gov.cn">财务司</option>
            <option value="http://kjs.customs.gov.cn">科技发展司</option>
            <option value="http://dss.customs.gov.cn">督察内审司</option>
            <option value="http://rjs.customs.gov.cn">人事教育司</option>
            <option value="http://jgdw.customs.gov.cn">机关党委</option>
            <option value="http://ltj.customs.gov.cn">离退休干部局</option>
        </select>
        <select name="">
            <option value="">直属海关</option>
            <option value="http://beijing.customs.gov.cn">北京海关</option>
            <option value="http://tianjin.customs.gov.cn">天津海关</option>
            <option value="http://shijiazhuang.customs.gov.cn">石家庄海关</option>
            <option value="http://taiyuan.customs.gov.cn">太原海关</option>
            <option value="http://huhehaote.customs.gov.cn">呼和浩特海关</option>
            <option value="http://manzhouli.customs.gov.cn">满洲里海关</option>
            <option value="http://dalian.customs.gov.cn">大连海关</option>
            <option value="http://shenyang.customs.gov.cn">沈阳海关</option>
            <option value="http://changchun.customs.gov.cn">长春海关</option>
            <option value="http://harbin.customs.gov.cn ">哈尔滨海关</option>
            <option value="http://shanghai.customs.gov.cn">上海海关</option>
            <option value="http://nanjing.customs.gov.cn">南京海关</option>
            <option value="http://hangzhou.customs.gov.cn">杭州海关</option>
            <option value="http://ningbo.customs.gov.cn">宁波海关</option>
            <option value="http://hefei.customs.gov.cn">合肥海关</option>
            <option value="http://fuzhou.customs.gov.cn">福州海关</option>
            <option value="http://xiamen.customs.gov.cn">厦门海关</option>
            <option value="http://nanchang.customs.gov.cn">南昌海关</option>
            <option value="http://qingdao.customs.gov.cn">青岛海关</option>
            <option value="http://jinan.customs.gov.cn">济南海关</option>
            <option value="http://zhengzhou.customs.gov.cn">郑州海关</option>
            <option value="http://wuhan.customs.gov.cn">武汉海关</option>
            <option value="http://changsha.customs.gov.cn">长沙海关</option>
            <option value="http://guangzhou.customs.gov.cn">广州海关</option>
            <option value="http://shenzhen.customs.gov.cn">深圳海关</option>
            <option value="http://gongbei.customs.gov.cn">拱北海关</option>
            <option value="http://shantou.customs.gov.cn">汕头海关</option>
            <option value="http://huangpu.customs.gov.cn">黄埔海关</option>
            <option value="http://jiangmen.customs.gov.cn">江门海关</option>
            <option value="http://zhanjiang.customs.gov.cn">湛江海关</option>
            <option value="http://nanning.customs.gov.cn">南宁海关</option>
            <option value="http://haikou.customs.gov.cn">海口海关</option>
            <option value="http://chongqing.customs.gov.cn">重庆海关</option>
            <option value="http://chengdu.customs.gov.cn">成都海关</option>
            <option value="http://guiyang.customs.gov.cn">贵阳海关</option>
            <option value="http://kunming.customs.gov.cn">昆明海关</option>
            <option value="http://lasa.customs.gov.cn">拉萨海关</option>
            <option value="http://xian.customs.gov.cn">西安海关</option>
            <option value="http://lanzhou.customs.gov.cn">兰州海关</option>
            <option value="http://xining.customs.gov.cn">西宁海关</option>
            <option value="http://yinchuan.customs.gov.cn">银川海关</option>
            <option value="http://urumqi.customs.gov.cn">乌鲁木齐海关</option>
        </select>
        <select name="">
            <option value="">在京直属事业单位</option>
            <option value="http://www.chinaport.gov.cn">电子口岸数据中心</option>
            <option value="http://www.customsmuseum.cn">中国海关博物馆</option>
            <option value="http://www.hgcbs.com.cn/">海关出版社</option>
            <option value="http://www.chinacustoms-strc.cn">中国海关科学技术研究中心</option>
        </select>
        <select name="">
            <option value="">世界海关组织</option>
            <option value="http://www.wcoomd.org">世界海关组织</option>
            <option value="http://www.tbtsps.com">中国WTO/TBT-SPS通报咨询网 </option>
            <option value="http://www.tbtsps.cn">中国技术贸易措施网</option>
        </select>
        <select name="">
            <option value="">社会团体</option>
            <option value="http://www.caop.org.cn">口岸协会</option>
            <option value="http://chinacba.org/ccba/">报关协会</option>
            <option value="http://www.cfea.org.cn">保税区出口加工区协会</option>
        </select>
	</div>
	<div class="customs-foot-text pubCon" role="region" aria-label="网站信息">
		<div class="foot-text-left fl">
			<p><a href="/beijing_customs/sy2022/745118/index.html" target="_blank" class="app_yincang">关于我们　|　</a><a href="javascript:void(0);" onclick="SetHome(this,'http://beijing.customs.gov.cn');" title="设为首页">设为首页　|　</a><a href="javascript:void(0);" onclick="AddFavorite('中华人民共和国北京海关','http://beijing.customs.gov.cn')" title="加入收藏">加入收藏　|　</a><a href="/beijing_customs/sy2022/742830/index.html" class="app_yincang">举报电话　|　</a><a href="/beijing_customs/sy2022/742833/index.html" target="_blank">使用帮助</a><a href="/Assistants/index.html" class="phone_wza">　|　无障碍　</a></p>
			<p>版权所有：中华人民共和国海关总署　　主办单位：中华人民共和国北京海关</p>
			<p>电话：010-85736114　地址：北京市朝阳区光华路甲10号　邮编：100026 </p>
		</div>
		<div class="foot-text-center fl">
                        <p><a href="http://beian.miit.gov.cn/" target="blank">京ICP备17015317号</a></p>
			<p>京公网安备：<a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=11040102700078" target="blank">11040102700078</a></p>
			<p>网站标识码：bm28020001</p>
		</div>
		<div class="foot-text-right fr">
			<a target="_blank" href="http://bszs.conac.cn/sitename?method=show&amp;id=07288044E5580314E053022819ACAFCA"><img src="/eportal/fileDir/beijing_customs/template/common/footer_2020/foot_copy.png?timestamp=1607566204921"></a>
			<a target="_blank" href="https://zfwzzc.www.gov.cn/check_web/errorInfo/jcInfo/a8dce411-d550-4118-91c8-61ef4dfb34dd-3706800412"><img src="/eportal/uiFramework/commonResource/image/2017062602331959948.jpg"></a>
		</div>
	</div>
</div>
<!-- 底部 end -->

<!-- 外链 弹窗 -->
<style type="text/css">
/*外链弹窗*/
.alert-mengban{position: fixed;top: 0px;left: 0px;z-index: 1000;background: #000000;opacity: 0.8 !important;filter: alpha(opacity=80) !important;width: 100%;height: 100%;display: none;}
.alert-warning{position: fixed;left: 50%;top: 50%; margin-left:-350px; margin-top: -50%; width: 700px;height: 270px;background:#fff;z-index: 1001;display: none;}
.alert-delete{width: 100%;height: 38px;position: relative;}
.alert-delete span{position: absolute;top:10px;right: 10px; width: 19px;height: 19px;background: url(http://www.bjhr.gov.cn/main/resource/cms/2017/10/img_pc_site/2017103115460722820.png) center center no-repeat;cursor:pointer;}
.alert-wzsm{width: 620px;height: 100px;margin: 15px auto 0; line-height: 35px;font-size: 24px;color: #000;text-align: center;font-family:"Microsoft YaHei"; padding-bottom: 15px;border-bottom: 1px solid #d4d4d4;}
.alert-wzsm p{font-size:24px;font-family:"Microsoft YaHei";}
.alert-footer{width: 100%; height: 105px;font-size: 24px;color: #000;}
.alert-footer span{cursor: pointer;float: left;font-family:"Microsoft YaHei";}
.continue{width: 124px;height: 42px;background: url(/eportal/uiFramework/commonResource/image/2020101609332452907.png) center center no-repeat;}
.continue a { display:block; width: 124px;height: 42px; overflow:hidden; text-indent:-999px; }
.fangqi{line-height: 42px;font-size: 20px;color: #ab0d07;margin-left: 20px;}
.xuanze{width: 210px;height: 42px;margin: 25px auto 0;}
@media screen and (max-width:800px) {
	.alert-warning { width: 90%; margin-left: 0; left: 5%; }
	.alert-wzsm { width: 100%; font-size: 16px; line-height: 2em; }
	.alert-wzsm p { font-size: 16px; padding: 0 20px; }
}
</style>

<script type="text/javascript">
/*外链弹窗 start*/
$(function(){
	$("a[href*='http']").on("click",this,function(e){	
		var w_link = $(this).attr("href");
		var tpid = $("#eprotalCurrentPageId").val();
		if(w_link!=""&&w_link.toLowerCase().indexOf("javascript")==-1&&w_link.toLowerCase().indexOf("beijing.customs.gov.cn/")==-1 ){
			
			if (self.frameElement && self.frameElement.tagName == "IFRAME") {
				
				if($(window.parent.document).find("#w_warning").length < 1 ){
					$(window.parent.document).find("body").append(f_html(w_link));
				}else{
					$(window.parent.document).find("#continue a").attr("href",w_link);
				}
				window.parent.f_kg();
				
			}
			else {
				if($("#w_warning").length < 1 ){
					$("body").append(f_html(w_link));
				}else{
					$("#continue a").attr("href",w_link);
				}
				f_kg();
			}
			
			return false;
		}
	});
});

/*外链弹窗*/
function f_html(w_link){
	var w_html = ''; 
		w_html += '<div class="alert-warning" id="w_warning" >';
		w_html += '		<div class="alert-delete">';
		w_html += '			<span id="closets"></span>';
		w_html += '		</div>';
		w_html += '		<div class="alert-wzsm">';
		w_html += '			<p>您访问的链接即将离开"中华人民共和国北京海关"门户网站 是否继续？</p>';
		w_html += '		</div>';
		w_html += '		<div class="alert-footer">		';
		w_html += '			<div class="xuanze">';
		w_html += '				<span class="continue" id="continue"><a href="'+w_link+'" target="_blank">外链</a></span>';	
		w_html += '				<span class="fangqi" id="fangqi">放弃</span>	';
		w_html += '			</div>';
		w_html += '		</div>';
		w_html += '</div>';
		w_html += '<div class="alert-mengban" id="w_mengban" ></div>';
	return w_html;
}

/*外链打开关闭弹窗*/
function f_kg(){
	$("#w_mengban").fadeIn(200);
	$("#w_warning").delay(100).show().animate({'marginTop':"-135px"}, 300);
	$("#closets,#fangqi,#w_mengban").click(function() {
		$("#w_warning").animate({"marginTop":"-50%"}, 200).hide(200);
		$("#w_mengban").delay(100).fadeOut(200);
	});
	$("#continue").click(function(){			
		$("#w_warning").hide(200);
		$("#w_mengban").delay(100).fadeOut(200);
	})
}
</script>

<script type="text/javascript">
//设为首页 www.customs.gov.cn
function SetHome(obj,url){
  try{
    obj.style.behavior='url(#default#homepage)';
    obj.setHomePage(url);
  }catch(e){
    if(window.netscape){
     try{
       netscape.security.PrivilegeManager.enablePrivilege("UniversalXPConnect");
     }catch(e){
       alert("抱歉，此操作被浏览器拒绝！\n\n请在浏览器地址栏输入“about:config”并回车然后将[signed.applets.codebase_principal_support]设置为'true'");
     }
    }else{
    alert("抱歉，您所使用的浏览器无法完成此操作。\n\n您需要手动将【"+url+"】设置为首页。");
    }
 }
}
  
//收藏本站 www.customs.gov.cn
function AddFavorite(title, url) {
 try {
   window.external.addFavorite(url, title);
 }
catch (e) {
   try {
    window.sidebar.addPanel(title, url, "");
  }
   catch (e) {
     alert("抱歉，您所使用的浏览器无法完成此操作。\n\n加入收藏失败，请进入新网站后使用Ctrl+D进行添加");
   }
 }
}
</script>   <!-- 尾部 end --> 
   <script type="text/javascript">
	$(".listCon_Nav li:last").find('a').css('border','0');
$(function(){
    $(".listCon_L h3 img.phoneNavBut").click(function(){
        $(".yj_navList, .listCon_Nav").slideToggle();
    });
})
</script> 
   <script>
	$(function(){
		$('.this_nav').html($('.position').html());
	})
</script> 
   <style>
/*  ***政务公开（新）**** */
.content_img{ background: url(/eportal/uiFramework/commonResource/image/2021062210073444977.png ) no-repeat top center; padding-top:35px; }
.gkmlTit .logo img{height:58px; }
.gkmlTit h2{font-size:94px; color:#fff; position:relative;margin-bottom:100px; margin-top:49px; height:125px; text-align:center;}
.gkmlTit h2 i{display:inline-block; position:absolute; left:15%; bottom:0px; width:70%; height:2px; background:#fff; }
.this_nav{line-height:50px;}
.inwrap{background:#fff;margin-bottom: 50px;padding: 35px 38px;}
.this_nav a,.this_nav span{font-size:16px;}
.customs-body-bg{background:none;}
.containerbox{padding-top: 50px;}
.listCon{background:#FFF;padding: 35px 38px;}
</style> 
  </div><div style="display:none" easysite="easysiteHiddenDiv">
<input type="hidden" id="currentLoginUserLoginName">
<input type="hidden" id="currentLoginUserLoginId">
<input type="hidden" id="currentLoginUserIsSuperAdmin">
</div>
 
</body></html>