import pandas as pd

# 读取CSV文件
df = pd.read_csv('T_STATISTICAL_CUS_TOTAL_BEIJING_updated.csv', encoding='utf-8-sig')

# 检查是否有企业性质数据
enterprise_data = df[df['STAT_NAME'].str.contains('企业性质', na=False)]
print("企业性质数据条数:", len(enterprise_data))
print(enterprise_data.head())

# 检查是否有国别地区数据
country_data = df[df['STAT_NAME'].str.contains('国别地区', na=False)]
print("\n国别地区数据条数:", len(country_data))
print(country_data.head())

# 检查所有不同的STAT_NAME值
print("\n所有不同的STAT_NAME值:")
print(df['STAT_NAME'].unique())