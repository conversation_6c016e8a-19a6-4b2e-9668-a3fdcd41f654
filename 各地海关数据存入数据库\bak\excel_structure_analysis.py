"""
浙江省进出口数据Excel文件结构分析脚本
用于分析2025年1月浙江省进出口情况.xlsx与浙江单6月数据.xlsx的结构差异
"""

import pandas as pd
import numpy as np
import sys
import io
from pathlib import Path

# 设置输出编码
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

def analyze_excel_structure(file_path, file_name):
    """分析Excel文件结构"""
    print(f"\n{'='*60}")
    print(f"分析文件: {file_name}")
    print(f"{'='*60}")
    
    try:
        # 读取Excel文件
        excel_file = pd.ExcelFile(file_path)
        print(f"工作表数量: {len(excel_file.sheet_names)}")
        print(f"工作表名称: {excel_file.sheet_names}")
        
        analysis_results = {}
        
        # 分析每个工作表
        for sheet_name in excel_file.sheet_names:
            print(f"\n{'-'*40}")
            print(f"工作表: {sheet_name}")
            print(f"{'-'*40}")
            
            # 读取工作表
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            
            # 基本信息
            basic_info = {
                '行数': len(df),
                '列数': len(df.columns),
                '列名': list(df.columns),
                '数据类型': dict(df.dtypes),
                '缺失值统计': dict(df.isnull().sum()),
                '前5行数据': df.head().to_dict()
            }
            
            print(f"行数: {basic_info['行数']}")
            print(f"列数: {basic_info['列数']}")
            print(f"列名: {basic_info['列名']}")
            print(f"数据类型: {basic_info['数据类型']}")
            
            # 显示缺失值统计
            print(f"缺失值统计:")
            for col, missing_count in basic_info['缺失值统计'].items():
                if missing_count > 0:
                    print(f"  {col}: {missing_count}")
            
            analysis_results[sheet_name] = basic_info
        
        return analysis_results
        
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None

def compare_structures(file1_analysis, file2_analysis):
    """对比两个文件的结构"""
    print(f"\n{'='*60}")
    print("文件结构对比分析")
    print(f"{'='*60}")
    
    if not file1_analysis or not file2_analysis:
        print("无法对比，请确保两个文件都成功读取")
        return
    
    # 对比工作表
    sheets1 = set(file1_analysis.keys())
    sheets2 = set(file2_analysis.keys())
    
    print(f"\n工作表对比:")
    print(f"文件1工作表: {sorted(sheets1)}")
    print(f"文件2工作表: {sorted(sheets2)}")
    print(f"相同工作表: {sorted(sheets1 & sheets2)}")
    print(f"仅在文件1中的工作表: {sorted(sheets1 - sheets2)}")
    print(f"仅在文件2中的工作表: {sorted(sheets2 - sheets1)}")
    
    # 对比每个工作表的结构
    common_sheets = sheets1 & sheets2
    for sheet_name in sorted(common_sheets):
        print(f"\n{'-'*40}")
        print(f"工作表 '{sheet_name}' 对比:")
        print(f"{'-'*40}")
        
        info1 = file1_analysis[sheet_name]
        info2 = file2_analysis[sheet_name]
        
        # 行数对比
        print(f"行数: 文件1={info1['行数']}, 文件2={info2['行数']}")
        if info1['行数'] != info2['行数']:
            print(f"  ⚠️  行数不同，差异: {info1['行数'] - info2['行数']}")
        
        # 列数对比
        print(f"列数: 文件1={info1['列数']}, 文件2={info2['列数']}")
        if info1['列数'] != info2['列数']:
            print(f"  ⚠️  列数不同，差异: {info1['列数'] - info2['列数']}")
        
        # 列名对比
        cols1 = set(info1['列名'])
        cols2 = set(info2['列名'])
        
        print(f"列名对比:")
        print(f"  相同列名: {sorted(cols1 & cols2)}")
        if cols1 - cols2:
            print(f"  仅在文件1中的列: {sorted(cols1 - cols2)}")
        if cols2 - cols1:
            print(f"  仅在文件2中的列: {sorted(cols2 - cols1)}")
        
        # 数据类型对比
        print(f"数据类型对比:")
        common_cols = cols1 & cols2
        for col in sorted(common_cols):
            type1 = info1['数据类型'][col]
            type2 = info2['数据类型'][col]
            print(f"  {col}: 文件1={type1}, 文件2={type2}")
            if type1 != type2:
                print(f"    ⚠️  数据类型不同")

def clean_and_analyze_data(file_path, file_name):
    """清理和分析数据"""
    print(f"\n{'='*60}")
    print(f"清理和分析文件: {file_name}")
    print(f"{'='*60}")
    
    try:
        excel_file = pd.ExcelFile(file_path)
        cleaned_data = {}
        
        for sheet_name in excel_file.sheet_names:
            print(f"\n清理工作表: {sheet_name}")
            
            # 读取原始数据
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            print(f"原始数据形状: {df.shape}")
            
            # 清理步骤
            # 1. 删除Unnamed列
            df_cleaned = df.loc[:, ~df.columns.str.contains('^Unnamed')]
            print(f"删除Unnamed列后形状: {df_cleaned.shape}")
            
            # 2. 清理列名中的特殊字符
            df_cleaned.columns = df_cleaned.columns.str.replace('\xa0', ' ').str.strip()
            
            # 3. 转换数值列
            numeric_cols = ['当期进出口', '当期进口', '当期出口', '进出口同比', '进口同比', '出口同比']
            for col in numeric_cols:
                if col in df_cleaned.columns:
                    # 处理特殊字符
                    df_cleaned[col] = df_cleaned[col].astype(str).str.replace('*', '').str.strip()
                    df_cleaned[col] = pd.to_numeric(df_cleaned[col], errors='coerce')
                    print(f"  {col}: 转换后数据类型: {df_cleaned[col].dtype}")
            
            # 4. 处理主出商品和主进商品的特殊情况
            if sheet_name in ['主出商品', '主进商品']:
                df_cleaned['当期'] = pd.to_numeric(df_cleaned['当期'], errors='coerce')
                df_cleaned['同比'] = pd.to_numeric(df_cleaned['同比'], errors='coerce')
                print(f"  当期: 转换后数据类型: {df_cleaned['当期'].dtype}")
                print(f"  同比: 转换后数据类型: {df_cleaned['同比'].dtype}")
            
            # 显示清理后的数据类型
            print(f"清理后数据类型:")
            for col, dtype in df_cleaned.dtypes.items():
                print(f"  {col}: {dtype}")
            
            cleaned_data[sheet_name] = df_cleaned
        
        return cleaned_data
        
    except Exception as e:
        print(f"清理数据时出错: {e}")
        return None

def main():
    """主函数"""
    print("浙江省进出口数据Excel文件结构分析")
    print("=" * 60)
    
    # 文件路径
    file1_path = r"C:\Users\<USER>\Desktop\海关数据\小超海关统计\全国进出口\各地海关数据存入数据库\浙江单6月数据.xlsx"
    file2_path = r"C:\Users\<USER>\Desktop\海关数据\小超海关统计\全国进出口\各地海关数据存入数据库\录入的\2025年1月浙江省进出口情况.xlsx"
    
    # 分析文件1
    file1_analysis = analyze_excel_structure(file1_path, "浙江单6月数据.xlsx")
    
    # 分析文件2
    file2_analysis = analyze_excel_structure(file2_path, "2025年1月浙江省进出口情况.xlsx")
    
    # 对比结构
    compare_structures(file1_analysis, file2_analysis)
    
    # 清理和分析数据
    print(f"\n{'='*60}")
    print("数据清理和分析")
    print(f"{'='*60}")
    
    file1_cleaned = clean_and_analyze_data(file1_path, "浙江单6月数据.xlsx")
    file2_cleaned = clean_and_analyze_data(file2_path, "2025年1月浙江省进出口情况.xlsx")
    
    print(f"\n{'='*60}")
    print("分析完成")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()