from DrissionPage import SessionPage, ChromiumPage
import pandas as pd
import os
import time
import re

# 导入提取函数
from 直接提取表格数据 import extract_table_from_url

# 创建存储目录
output_dir = "提取的表格数据"
if not os.path.exists(output_dir):
    os.makedirs(output_dir)
    print(f"创建输出目录: {output_dir}")

def extract_tables_from_links_file(link_file, target_years=None):
    """
    从链接文件读取URL并批量提取表格数据
    :param link_file: 包含链接的文本文件
    :param target_years: 目标年份列表，如果指定则只处理这些年份的链接
    """
    # 检查链接文件是否存在
    if not os.path.exists(link_file):
        print(f"链接文件不存在: {link_file}")
        return
    
    # 读取链接
    print(f"从文件中读取链接: {link_file}")
    all_links = []
    
    with open(link_file, 'r', encoding='utf-8') as f:
        for line in f:
            parts = line.strip().split(',', 2)  # 最多分成3部分（年份,文本,URL）
            if len(parts) == 3:
                year, text, url = parts
                
                # 如果指定了目标年份，则只处理这些年份
                if target_years and year not in target_years:
                    continue
                    
                link_info = {
                    "year": year,
                    "text": text,
                    "url": url
                }
                all_links.append(link_info)
    
    print(f"读取了 {len(all_links)} 个链接")
    
    # 遍历链接提取表格
    success_count = 0
    fail_count = 0
    
    for link_info in all_links:
        year = link_info['year']
        text = link_info['text']
        url = link_info['url']
        
        print(f"\n开始处理: {year} - {text}")
        
        # 构造文件名
        # 清理文本，移除不允许的字符
        clean_text = re.sub(r'[\\/*?:"<>|]', '', text)
        file_name = f"{year}_{clean_text}.xlsx"
        
        # 提取表格数据
        result = extract_table_from_url(url, file_name)
        
        if result:
            success_count += 1
        else:
            fail_count += 1
        
        # 防止请求过于频繁
        time.sleep(3)
    
    print(f"\n处理完成! 成功: {success_count}, 失败: {fail_count}")

if __name__ == "__main__":
    # 链接文件路径
    link_file = '进出口商品收发货人所在地总值表_所有链接.txt'
    
    # 用户选择处理方式
    print("请选择处理方式:")
    print("1. 处理所有链接")
    print("2. 按年份处理")
    print("3. 处理特定年份范围")
    
    choice = input("请输入选择 (1/2/3): ")
    
    if choice == '1':
        # 处理所有链接
        extract_tables_from_links_file(link_file)
    elif choice == '2':
        # 按特定年份处理
        years_input = input("请输入要处理的年份（用逗号分隔，例如: 2014,2015,2016）: ")
        target_years = [year.strip() for year in years_input.split(',')]
        print(f"将处理以下年份的链接: {', '.join(target_years)}")
        extract_tables_from_links_file(link_file, target_years)
    elif choice == '3':
        # 处理年份范围
        start_year = input("请输入起始年份: ")
        end_year = input("请输入结束年份: ")
        
        try:
            start = int(start_year)
            end = int(end_year)
            target_years = [str(year) for year in range(start, end+1)]
            print(f"将处理 {start} 到 {end} 年份的链接: {', '.join(target_years)}")
            extract_tables_from_links_file(link_file, target_years)
        except ValueError:
            print("年份输入格式错误，请输入有效的数字年份")
    else:
        print("无效的选择") 