from DrissionPage import ChromiumPage
import time
import os
import re
import shutil
from datetime import datetime

# 定义北京海关要抓取的表格种类配置
BEIJING_TARGET_TABLES = {
    "(5)": "北京地区进出口商品贸易方式总值表",
    "(6)": "北京地区出口商品贸易方式企业性质总值表",
    "(7)": "北京地区进口商品贸易方式企业性质总值表",
    "(8)": "北京地区出口主要商品量值表",
    "(9)": "北京地区进口主要商品量值表"
}

def is_target_table(text):
    """检查链接文本是否是我们需要的目标表格"""
    # 检查是否包含目标表格的关键特征（注意使用中文括号）
    if "北京地区" in text:
        if "（5）" in text and "贸易方式" in text and "总值表" in text and "企业性质" not in text:
            return True, "(5)"
        elif "（6）" in text and "出口" in text and "贸易方式" in text and "企业性质" in text:
            return True, "(6)"
        elif "（7）" in text and "进口" in text and "贸易方式" in text and "企业性质" in text:
            return True, "(7)"
        elif "（8）" in text and "出口" in text and "主要商品" in text and "量值表" in text:
            return True, "(8)"
        elif "（9）" in text and "进口" in text and "主要商品" in text and "量值表" in text:
            return True, "(9)"
    return False, None

def download_file_with_browser(page, file_url, save_path, temp_download_dir):
    """使用浏览器下载文件"""
    try:
        print(f"使用浏览器访问文件URL: {file_url}")

        # 清空临时下载目录
        for temp_file in os.listdir(temp_download_dir):
            temp_file_path = os.path.join(temp_download_dir, temp_file)
            if os.path.isfile(temp_file_path):
                os.remove(temp_file_path)

        # 直接访问文件URL，这会触发下载
        page.get(file_url)

        # 等待下载完成
        downloaded = False
        start_time = time.time()
        timeout = 60  # 设置超时时间为60秒

        while not downloaded and time.time() - start_time < timeout:
            time.sleep(2)  # 每2秒检查一次

            # 检查临时目录中是否有新文件
            temp_files = os.listdir(temp_download_dir)
            if temp_files:
                for temp_file in temp_files:
                    if temp_file.endswith(('.xls', '.xlsx')) and not temp_file.endswith('.crdownload'):
                        # 找到下载的文件
                        temp_file_path = os.path.join(temp_download_dir, temp_file)
                        print(f"文件已下载到临时位置: {temp_file_path}")

                        # 移动并重命名文件
                        shutil.copy2(temp_file_path, save_path)
                        print(f"文件已复制到最终位置: {save_path}")
                        downloaded = True
                        return True
                    elif temp_file.endswith('.crdownload'):
                        print(f"文件正在下载中: {temp_file}")

        if not downloaded:
            print("下载超时")
            return False

    except Exception as e:
        print(f"下载文件时出错: {e}")
        return False

def get_links_from_page(page, base_url="http://beijing.customs.gov.cn"):
    """从当前页面获取目标链接"""
    target_links = []

    # 查找所有链接
    all_links = page.eles('tag:a')

    for link in all_links:
        link_text = link.text.strip()
        href = link.attr('href')

        if not href or not link_text:
            continue

        # 检查是否包含目标年份
        if any(year in link_text for year in ['2024', '2025']):
            # 使用目标表格检查函数
            is_target, table_key = is_target_table(link_text)

            if is_target:
                # 构建完整URL
                if href.startswith('/'):
                    full_url = base_url + href
                elif not href.startswith('http'):
                    current_url = page.url
                    base_path = '/'.join(current_url.split('/')[:-1]) + '/'
                    full_url = base_path + href
                else:
                    full_url = href

                target_links.append({
                    'text': link_text,
                    'url': full_url,
                    'year': '2024' if '2024' in link_text else '2025',
                    'table_key': table_key,
                    'table_name': BEIJING_TARGET_TABLES[table_key]
                })

                print(f"找到目标链接: {table_key} - {link_text[:50]}...")

    return target_links

def main():
    """主函数：爬取北京海关数据（支持翻页）"""

    print("=== 北京海关数据爬虫（完整版 - 支持翻页）===")

    # 创建基础下载目录
    base_dir = "北京海关统计数据_完整版"
    if not os.path.exists(base_dir):
        os.makedirs(base_dir)
        print(f"创建基础下载目录: {base_dir}")

    # 创建临时下载目录
    temp_download_dir = "temp_download_beijing_full"
    if not os.path.exists(temp_download_dir):
        os.makedirs(temp_download_dir)
        print(f"创建临时下载目录: {temp_download_dir}")

    # 设置ChromiumPage
    page = ChromiumPage(timeout=30)
    page.set.download_path(os.path.abspath(temp_download_dir))
    print(f"设置下载目录: {temp_download_dir}")

    # 北京海关统计数据页面
    base_url = "http://beijing.customs.gov.cn"
    start_url = "http://beijing.customs.gov.cn/beijing_customs/434756/434804/2941702/434773/434774/index.html"

    try:
        print(f"\n正在访问北京海关统计数据页面...")
        page.get(start_url)
        time.sleep(5)  # 等待页面完全加载

        print(f"页面标题: {page.title}")
        print(f"当前URL: {page.url}")

        # 收集所有目标链接
        all_target_links = []

        # 从第一页开始
        current_page = 1
        max_pages = 50  # 限制最大页数，避免无限循环

        while current_page <= max_pages:
            print(f"\n=== 处理第 {current_page} 页 ===")

            # 获取当前页面的目标链接
            page_links = get_links_from_page(page, base_url)

            if page_links:
                print(f"第 {current_page} 页找到 {len(page_links)} 个目标链接")
                all_target_links.extend(page_links)
            else:
                print(f"第 {current_page} 页没有找到目标链接")

            # 检查是否有下一页
            next_page_found = False

            # 方法1：查找"下一页"链接
            next_page_elements = page.eles("下一页")
            for next_elem in next_page_elements:
                if next_elem.tag == 'a':
                    onclick = next_elem.attr('onclick')
                    if onclick and 'queryArticleByCondition' in onclick:
                        print(f"找到下一页链接，准备访问第 {current_page + 1} 页")
                        try:
                            next_elem.click()
                            time.sleep(3)
                            next_page_found = True
                            break
                        except Exception as e:
                            print(f"点击下一页失败: {e}")

            # 方法2：如果方法1失败，尝试直接构建URL
            if not next_page_found and current_page < 10:  # 只尝试前10页
                next_page_url = f"http://beijing.customs.gov.cn/beijing_customs/434756/434804/2941702/434773/434774/16673809-{current_page + 1}.html"
                print(f"尝试直接访问第 {current_page + 1} 页: {next_page_url}")
                try:
                    page.get(next_page_url)
                    time.sleep(3)

                    # 检查页面是否有效（包含统计数据）
                    if page.eles("统计数据"):
                        next_page_found = True
                        print(f"成功访问第 {current_page + 1} 页")
                    else:
                        print(f"第 {current_page + 1} 页无效或无数据")
                except Exception as e:
                    print(f"直接访问第 {current_page + 1} 页失败: {e}")

            if not next_page_found:
                print(f"没有更多页面，停止翻页")
                break

            current_page += 1

            # 防止请求过快
            time.sleep(2)

        print(f"\n=== 翻页完成，总共处理了 {current_page} 页 ===")
        print(f"总共找到 {len(all_target_links)} 个目标链接")

        # 保存所有链接信息
        with open('北京海关数据链接_完整版.txt', 'w', encoding='utf-8') as f:
            f.write("年份\t表格编号\t表格名称\t链接文本\t完整URL\n")
            for link in all_target_links:
                f.write(f"{link['year']}\t{link['table_key']}\t{link['table_name']}\t{link['text']}\t{link['url']}\n")

        print("链接信息已保存到: 北京海关数据链接_完整版.txt")

        # 开始下载文件
        if all_target_links:
            print(f"\n=== 开始下载文件 ===")
            download_count = 0
            error_count = 0

            for i, link_info in enumerate(all_target_links):
                try:
                    print(f"\n处理第 {i+1}/{len(all_target_links)} 个链接...")
                    print(f"表格: {link_info['table_key']} - {link_info['table_name']}")
                    print(f"年份: {link_info['year']}")
                    print(f"URL: {link_info['url']}")

                    # 访问详情页
                    page.get(link_info['url'])
                    time.sleep(3)

                    # 查找页面中的.xls或.xlsx文件链接
                    xls_links = page.eles('tag:a')
                    file_url = None

                    for xls_link in xls_links:
                        href = xls_link.attr('href')
                        if href and (href.endswith('.xls') or href.endswith('.xlsx')):
                            # 构建完整的文件URL
                            if href.startswith('/'):
                                file_url = base_url + href
                            elif not href.startswith('http'):
                                current_url = page.url
                                base_path = '/'.join(current_url.split('/')[:-1]) + '/'
                                file_url = base_path + href
                            else:
                                file_url = href
                            break

                    if not file_url:
                        # 尝试从meta标签中获取文件URL
                        meta_elements = page.eles('tag:meta')
                        for meta in meta_elements:
                            content = meta.attr('content')
                            if content and (content.endswith('.xls') or content.endswith('.xlsx')):
                                file_url = content
                                break

                    if file_url:
                        print(f"找到文件URL: {file_url}")

                        # 生成本地文件名
                        clean_text = re.sub(r'[\\/*?:"<>|]', '_', link_info['text'])
                        file_name = f"{link_info['year']}_{link_info['table_key']}_{clean_text}.xls"
                        file_path = os.path.join(base_dir, file_name)

                        # 检查文件是否已存在
                        if os.path.exists(file_path):
                            print(f"文件已存在，跳过: {file_name}")
                            continue

                        # 下载文件
                        print(f"开始下载: {file_name}")
                        if download_file_with_browser(page, file_url, file_path, temp_download_dir):
                            print(f"下载成功: {file_name}")
                            download_count += 1
                        else:
                            print(f"下载失败: {file_name}")
                            error_count += 1
                    else:
                        print("未找到文件下载链接")
                        error_count += 1

                except Exception as e:
                    print(f"处理链接时出错: {e}")
                    error_count += 1

                # 防止请求过快
                time.sleep(2)

            print(f"\n=== 下载完成 ===")
            print(f"成功下载: {download_count} 个文件")
            print(f"失败: {error_count} 个文件")
        else:
            print("未找到任何目标链接")

    except Exception as e:
        print(f"主程序执行出错: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # 清理临时目录
        try:
            if os.path.exists(temp_download_dir):
                shutil.rmtree(temp_download_dir)
                print(f"临时目录已清理: {temp_download_dir}")
        except Exception as e:
            print(f"清理临时目录出错: {e}")

        # 关闭浏览器
        try:
            page.quit()
            print("浏览器已关闭")
        except:
            pass

if __name__ == "__main__":
    main()