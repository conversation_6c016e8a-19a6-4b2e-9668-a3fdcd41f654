import pandas as pd
import cx_Oracle
import os
from datetime import datetime

def import_single_csv_to_oracle(csv_file_path, table_name, db_config):
    """
    导入单个CSV文件到Oracle数据库
    """
    try:
        print(f"=== 导入单个文件 ===")
        print(f"文件: {csv_file_path}")
        print(f"目标表: {table_name}")
        
        # 检查文件是否存在
        if not os.path.exists(csv_file_path):
            print(f"❌ 文件不存在: {csv_file_path}")
            return False, 0
        
        # 读取CSV文件
        print("读取CSV文件...")
        df = pd.read_csv(csv_file_path, encoding='utf-8-sig')
        print(f"读取到 {len(df)} 条记录")
        print(f"字段数量: {len(df.columns)}")
        
        if len(df) == 0:
            print("CSV文件为空，跳过")
            return True, 0
        
        # 显示前几条记录
        print(f"\n前3条记录预览:")
        for i in range(min(3, len(df))):
            print(f"记录 {i+1}:")
            row = df.iloc[i]
            for col in ['STAT_DATE', 'STAT_TYPE', 'STAT_CONTENT_CLEANSE', 'CREATE_TIME']:
                if col in df.columns:
                    print(f"  {col}: {row[col]}")
        
        # 连接Oracle数据库
        print(f"\n连接Oracle数据库...")
        conn = cx_Oracle.connect(**db_config)
        cursor = conn.cursor()
        print("数据库连接成功")
        
        # 修复的插入SQL（使用TO_DATE函数处理日期字段）
        insert_sql = f"""
        INSERT INTO {table_name} (
            STAT_DATE, STAT_TYPE, STAT_NAME, STAT_CODE, STAT_CONTENT_RAW, STAT_CONTENT_CLEANSE,
            ACC_A_CNY_AMOUNT, ACC_A_CNY_YOY, ACC_A_USD_AMOUNT, ACC_A_USD_YOY,
            MON_A_CNY_AMOUNT, MON_A_CNY_YOY, MON_A_USD_AMOUNT, MON_A_USD_YOY,
            ACC_E_CNY_AMOUNT, ACC_E_CNY_YOY, ACC_E_USD_AMOUNT, ACC_E_USD_YOY,
            MON_E_CNY_AMOUNT, MON_E_CNY_YOY, MON_E_USD_AMOUNT, MON_E_USD_YOY,
            ACC_I_CNY_AMOUNT, ACC_I_CNY_YOY, ACC_I_USD_AMOUNT, ACC_I_USD_YOY,
            MON_I_CNY_AMOUNT, MON_I_CNY_YOY, MON_I_USD_AMOUNT, MON_I_USD_YOY,
            ACC_E_AMOUNT, ACC_E_AMOUNT_UNIT, ACC_E_AMOUNT_YOY,
            MON_E_AMOUNT, MON_E_AMOUNT_UNIT, MON_E_AMOUNT_YOY,
            ACC_I_AMOUNT, ACC_I_AMOUNT_UNIT, ACC_I_AMOUNT_YOY,
            MON_I_AMOUNT, MON_I_AMOUNT_UNIT, MON_I_AMOUNT_YOY,
            RANK_MARKERS, DATA_SOURCE, EMPHASIS_OR_EMERGING_MARK, CREATE_TIME
        ) VALUES (
            :1, :2, :3, :4, :5, :6,
            :7, :8, :9, :10,
            :11, :12, :13, :14,
            :15, :16, :17, :18,
            :19, :20, :21, :22,
            :23, :24, :25, :26,
            :27, :28, :29, :30,
            :31, :32, :33,
            :34, :35, :36,
            :37, :38, :39,
            :40, :41, :42,
            :43, :44, :45, :46
        )
        """
        
        print(f"\nSQL语句准备完成")
        
        # 数据处理函数
        def get_numeric_value(value):
            if pd.isna(value) or value == '' or str(value).lower() == 'nan':
                return None
            try:
                return float(value)
            except (ValueError, TypeError):
                return None
        
        def get_string_value(value):
            if pd.isna(value) or value == '' or str(value).lower() == 'nan':
                return None
            return str(value).strip()
        
        def get_date_value(value):
            if pd.isna(value) or value == '' or str(value).lower() == 'nan':
                return None
            try:
                date_str = str(value).strip()
                if '/' in date_str and len(date_str.split('/')) == 3:
                    parts = date_str.split('/')
                    year, month, day = parts[0], parts[1].zfill(2), parts[2].zfill(2)
                    # 返回Python datetime对象，让cx_Oracle自动转换
                    from datetime import datetime
                    return datetime.strptime(f"{year}-{month}-{day}", "%Y-%m-%d").date()
                elif '-' in date_str:
                    from datetime import datetime
                    return datetime.strptime(date_str, "%Y-%m-%d").date()
                return None
            except:
                return None
        
        def get_datetime_value(value):
            if pd.isna(value) or value == '' or str(value).lower() == 'nan':
                return None
            try:
                datetime_str = str(value).strip()
                from datetime import datetime
                if '/' in datetime_str:
                    if ' ' in datetime_str:
                        date_part, time_part = datetime_str.split(' ', 1)
                        parts = date_part.split('/')
                        if len(parts) == 3:
                            year, month, day = parts[0], parts[1].zfill(2), parts[2].zfill(2)
                            full_datetime = f"{year}-{month}-{day} {time_part}"
                            return datetime.strptime(full_datetime, "%Y-%m-%d %H:%M:%S")
                    else:
                        parts = datetime_str.split('/')
                        if len(parts) == 3:
                            year, month, day = parts[0], parts[1].zfill(2), parts[2].zfill(2)
                            return datetime.strptime(f"{year}-{month}-{day}", "%Y-%m-%d")
                elif '-' in datetime_str:
                    if ' ' not in datetime_str:
                        return datetime.strptime(datetime_str, "%Y-%m-%d")
                    return datetime.strptime(datetime_str, "%Y-%m-%d %H:%M:%S")
                return None
            except:
                return None
        
        # 逐条插入数据
        inserted_rows = 0
        
        print(f"\n开始插入数据...")
        
        for index, row in df.iterrows():
            try:
                # 构建数据行（46个参数）
                data_row = [
                    get_date_value(row['STAT_DATE']),
                    get_string_value(row['STAT_TYPE']),
                    get_string_value(row['STAT_NAME']),
                    get_string_value(row['STAT_CODE']),
                    get_string_value(row['STAT_CONTENT_RAW']),
                    get_string_value(row['STAT_CONTENT_CLEANSE']),
                    get_numeric_value(row['ACC_A_CNY_AMOUNT']),
                    get_numeric_value(row['ACC_A_CNY_YOY']),
                    get_numeric_value(row['ACC_A_USD_AMOUNT']),
                    get_numeric_value(row['ACC_A_USD_YOY']),
                    get_numeric_value(row['MON_A_CNY_AMOUNT']),
                    get_numeric_value(row['MON_A_CNY_YOY']),
                    get_numeric_value(row['MON_A_USD_AMOUNT']),
                    get_numeric_value(row['MON_A_USD_YOY']),
                    get_numeric_value(row['ACC_E_CNY_AMOUNT']),
                    get_numeric_value(row['ACC_E_CNY_YOY']),
                    get_numeric_value(row['ACC_E_USD_AMOUNT']),
                    get_numeric_value(row['ACC_E_USD_YOY']),
                    get_numeric_value(row['MON_E_CNY_AMOUNT']),
                    get_numeric_value(row['MON_E_CNY_YOY']),
                    get_numeric_value(row['MON_E_USD_AMOUNT']),
                    get_numeric_value(row['MON_E_USD_YOY']),
                    get_numeric_value(row['ACC_I_CNY_AMOUNT']),
                    get_numeric_value(row['ACC_I_CNY_YOY']),
                    get_numeric_value(row['ACC_I_USD_AMOUNT']),
                    get_numeric_value(row['ACC_I_USD_YOY']),
                    get_numeric_value(row['MON_I_CNY_AMOUNT']),
                    get_numeric_value(row['MON_I_CNY_YOY']),
                    get_numeric_value(row['MON_I_USD_AMOUNT']),
                    get_numeric_value(row['MON_I_USD_YOY']),
                    get_numeric_value(row['ACC_E_AMOUNT']),
                    get_string_value(row['ACC_E_AMOUNT_UNIT']),
                    get_numeric_value(row['ACC_E_AMOUNT_YOY']),
                    get_numeric_value(row['MON_E_AMOUNT']),
                    get_string_value(row['MON_E_AMOUNT_UNIT']),
                    get_numeric_value(row['MON_E_AMOUNT_YOY']),
                    get_numeric_value(row['ACC_I_AMOUNT']),
                    get_string_value(row['ACC_I_AMOUNT_UNIT']),
                    get_numeric_value(row['ACC_I_AMOUNT_YOY']),
                    get_numeric_value(row['MON_I_AMOUNT']),
                    get_string_value(row['MON_I_AMOUNT_UNIT']),
                    get_numeric_value(row['MON_I_AMOUNT_YOY']),
                    get_string_value(row['RANK_MARKERS']),
                    get_string_value(row['DATA_SOURCE']),
                    get_string_value(row['EMPHASIS_OR_EMERGING_MARK']),
                    get_datetime_value(row['CREATE_TIME'])
                ]
                
                # 调试：显示第一条记录的参数
                if index == 0:
                    print(f"\n第一条记录参数详情:")
                    print(f"参数数量: {len(data_row)}")
                    for i, value in enumerate(data_row[:10]):
                        print(f"  参数{i+1}: {value} ({type(value).__name__})")
                    print(f"  ... (共{len(data_row)}个参数)")
                
                # 执行插入
                cursor.execute(insert_sql, data_row)
                conn.commit()
                
                inserted_rows += 1
                
                if inserted_rows % 5 == 0 or inserted_rows == len(df):
                    progress = (inserted_rows / len(df)) * 100
                    print(f"  已插入 {inserted_rows}/{len(df)} 条记录 ({progress:.1f}%)")
                
            except Exception as e:
                print(f"插入第 {index+1} 条记录时出错: {e}")
                print(f"记录内容: {row['STAT_CONTENT_CLEANSE'] if 'STAT_CONTENT_CLEANSE' in row else 'N/A'}")
                # 继续处理下一条记录
                continue
        
        print(f"\n✅ 数据插入完成！共插入 {inserted_rows} 条记录")
        
        # 关闭连接
        cursor.close()
        conn.close()
        
        return True, inserted_rows
        
    except cx_Oracle.DatabaseError as e:
        print(f"❌ 数据库错误: {e}")
        return False, 0
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")
        return False, 0
    finally:
        try:
            if 'conn' in locals():
                conn.close()
        except:
            pass

if __name__ == "__main__":
    # 数据库配置（TEST环境）
    db_config = {
        'user': 'manifest_dcb',
        'password': 'manifest_dcb',
        'dsn': '192.168.1.151/TEST'
    }
    
    table_name = 'T_STATISTICAL_CUS_TOTAL_CS'
    
    # 测试单个文件
    test_file = '北京海关数据_完整转换结果/T_STATISTICAL_CUS_TOTAL_BEIJING_2024_(5)_（5）北京地区进出口商品贸易方式总值表（2024年1-10月）.csv'
    
    print("=== 单文件导入测试 ===")
    
    if os.path.exists(test_file):
        success, count = import_single_csv_to_oracle(test_file, table_name, db_config)
        if success:
            print(f"🎉 导入成功！插入了 {count} 条记录")
        else:
            print(f"💥 导入失败")
    else:
        print(f"❌ 测试文件不存在: {test_file}")
        
        # 列出可用的文件
        csv_dir = '北京海关数据_完整转换结果'
        if os.path.exists(csv_dir):
            csv_files = [f for f in os.listdir(csv_dir) if f.endswith('.csv')]
            print(f"\n可用的CSV文件 (前5个):")
            for i, file in enumerate(csv_files[:5]):
                print(f"  {i+1}. {file}")
            
            if csv_files:
                first_file = os.path.join(csv_dir, csv_files[0])
                print(f"\n尝试导入第一个文件: {csv_files[0]}")
                success, count = import_single_csv_to_oracle(first_file, table_name, db_config)
                if success:
                    print(f"🎉 导入成功！插入了 {count} 条记录")
                else:
                    print(f"💥 导入失败")
