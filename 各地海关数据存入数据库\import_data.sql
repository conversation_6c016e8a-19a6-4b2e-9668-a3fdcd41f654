-- 数据导入脚本
-- 从CSV文件导入数据到T_STATISTICAL_CUS_TOTAL表

-- 方法1：使用SQL*Loader导入（推荐）
-- 首先创建控制文件

-- 创建SQL*Loader控制文件内容
/*
LOAD DATA
INFILE 'T_STATISTICAL_CUS_TOTAL_ZHEJIANG.csv'
BADFILE 'T_STATISTICAL_CUS_TOTAL_ZHEJIANG.bad'
DISCARDFILE 'T_STATISTICAL_CUS_TOTAL_ZHEJIANG.dsc'
APPEND INTO TABLE T_STATISTICAL_CUS_TOTAL
FIELDS TERMINATED BY ',' OPTIONALLY ENCLOSED BY '"'
TRAILING NULLCOLS
(
    STAT_DATE CHAR,
    STAT_TYPE CHAR,
    STAT_NAME CHAR,
    STAT_CODE CHAR,
    STAT_CONTENT_RAW CHAR,
    STAT_CONTENT_CLEANSE CHAR,
    ACC_A_CNY_AMOUNT "DECODE(:ACC_A_CNY_AMOUNT, '', NULL, TO_NUMBER(:ACC_A_CNY_AMOUNT))",
    ACC_A_CNY_YOY "DECODE(:ACC_A_CNY_YOY, '', NULL, TO_NUMBER(:ACC_A_CNY_YOY))",
    ACC_A_USD_AMOUNT "DECODE(:ACC_A_USD_AMOUNT, '', NULL, TO_NUMBER(:ACC_A_USD_AMOUNT))",
    ACC_A_USD_YOY "DECODE(:ACC_A_USD_YOY, '', NULL, TO_NUMBER(:ACC_A_USD_YOY))",
    MON_A_CNY_AMOUNT "DECODE(:MON_A_CNY_AMOUNT, '', NULL, TO_NUMBER(:MON_A_CNY_AMOUNT))",
    MON_A_CNY_YOY "DECODE(:MON_A_CNY_YOY, '', NULL, TO_NUMBER(:MON_A_CNY_YOY))",
    MON_A_USD_AMOUNT "DECODE(:MON_A_USD_AMOUNT, '', NULL, TO_NUMBER(:MON_A_USD_AMOUNT))",
    MON_A_USD_YOY "DECODE(:MON_A_USD_YOY, '', NULL, TO_NUMBER(:MON_A_USD_YOY))",
    ACC_E_CNY_AMOUNT "DECODE(:ACC_E_CNY_AMOUNT, '', NULL, TO_NUMBER(:ACC_E_CNY_AMOUNT))",
    ACC_E_CNY_YOY "DECODE(:ACC_E_CNY_YOY, '', NULL, TO_NUMBER(:ACC_E_CNY_YOY))",
    ACC_E_USD_AMOUNT "DECODE(:ACC_E_USD_AMOUNT, '', NULL, TO_NUMBER(:ACC_E_USD_AMOUNT))",
    ACC_E_USD_YOY "DECODE(:ACC_E_USD_YOY, '', NULL, TO_NUMBER(:ACC_E_USD_YOY))",
    MON_E_CNY_AMOUNT "DECODE(:MON_E_CNY_AMOUNT, '', NULL, TO_NUMBER(:MON_E_CNY_AMOUNT))",
    MON_E_CNY_YOY "DECODE(:MON_E_CNY_YOY, '', NULL, TO_NUMBER(:MON_E_CNY_YOY))",
    MON_E_USD_AMOUNT "DECODE(:MON_E_USD_AMOUNT, '', NULL, TO_NUMBER(:MON_E_USD_AMOUNT))",
    MON_E_USD_YOY "DECODE(:MON_E_USD_YOY, '', NULL, TO_NUMBER(:MON_E_USD_YOY))",
    ACC_I_CNY_AMOUNT "DECODE(:ACC_I_CNY_AMOUNT, '', NULL, TO_NUMBER(:ACC_I_CNY_AMOUNT))",
    ACC_I_CNY_YOY "DECODE(:ACC_I_CNY_YOY, '', NULL, TO_NUMBER(:ACC_I_CNY_YOY))",
    ACC_I_USD_AMOUNT "DECODE(:ACC_I_USD_AMOUNT, '', NULL, TO_NUMBER(:ACC_I_USD_AMOUNT))",
    ACC_I_USD_YOY "DECODE(:ACC_I_USD_YOY, '', NULL, TO_NUMBER(:ACC_I_USD_YOY))",
    MON_I_CNY_AMOUNT "DECODE(:MON_I_CNY_AMOUNT, '', NULL, TO_NUMBER(:MON_I_CNY_AMOUNT))",
    MON_I_CNY_YOY "DECODE(:MON_I_CNY_YOY, '', NULL, TO_NUMBER(:MON_I_CNY_YOY))",
    MON_I_USD_AMOUNT "DECODE(:MON_I_USD_AMOUNT, '', NULL, TO_NUMBER(:MON_I_USD_AMOUNT))",
    MON_I_USD_YOY "DECODE(:MON_I_USD_YOY, '', NULL, TO_NUMBER(:MON_I_USD_YOY))",
    ACC_E_AMOUNT "DECODE(:ACC_E_AMOUNT, '', NULL, TO_NUMBER(:ACC_E_AMOUNT))",
    ACC_E_AMOUNT_UNIT CHAR,
    ACC_E_AMOUNT_YOY "DECODE(:ACC_E_AMOUNT_YOY, '', NULL, TO_NUMBER(:ACC_E_AMOUNT_YOY))",
    MON_E_AMOUNT "DECODE(:MON_E_AMOUNT, '', NULL, TO_NUMBER(:MON_E_AMOUNT))",
    MON_E_AMOUNT_UNIT CHAR,
    MON_E_AMOUNT_YOY "DECODE(:MON_E_AMOUNT_YOY, '', NULL, TO_NUMBER(:MON_E_AMOUNT_YOY))",
    ACC_I_AMOUNT "DECODE(:ACC_I_AMOUNT, '', NULL, TO_NUMBER(:ACC_I_AMOUNT))",
    ACC_I_AMOUNT_UNIT CHAR,
    ACC_I_AMOUNT_YOY "DECODE(:ACC_I_AMOUNT_YOY, '', NULL, TO_NUMBER(:ACC_I_AMOUNT_YOY))",
    MON_I_AMOUNT "DECODE(:MON_I_AMOUNT, '', NULL, TO_NUMBER(:MON_I_AMOUNT))",
    MON_I_AMOUNT_UNIT CHAR,
    MON_I_AMOUNT_YOY "DECODE(:MON_I_AMOUNT_YOY, '', NULL, TO_NUMBER(:MON_I_AMOUNT_YOY))",
    RANK_MARKERS CHAR,
    DATA_SOURCE CHAR,
    EMPHASIS_OR_EMERGING_MARK CHAR,
    CREATE_TIME CHAR
)
*/

-- 方法2：使用外部表（需要先创建目录对象）
/*
CREATE OR REPLACE DIRECTORY DATA_DIR AS '/path/to/your/data/directory';

CREATE TABLE T_STATISTICAL_CUS_TOTAL_EXT (
    STAT_DATE VARCHAR2(20),
    STAT_TYPE VARCHAR2(10),
    STAT_NAME VARCHAR2(50),
    STAT_CODE VARCHAR2(50),
    STAT_CONTENT_RAW VARCHAR2(200),
    STAT_CONTENT_CLEANSE VARCHAR2(200),
    ACC_A_CNY_AMOUNT VARCHAR2(50),
    ACC_A_CNY_YOY VARCHAR2(20),
    ACC_A_USD_AMOUNT VARCHAR2(50),
    ACC_A_USD_YOY VARCHAR2(20),
    MON_A_CNY_AMOUNT VARCHAR2(50),
    MON_A_CNY_YOY VARCHAR2(20),
    MON_A_USD_AMOUNT VARCHAR2(50),
    MON_A_USD_YOY VARCHAR2(20),
    ACC_E_CNY_AMOUNT VARCHAR2(50),
    ACC_E_CNY_YOY VARCHAR2(20),
    ACC_E_USD_AMOUNT VARCHAR2(50),
    ACC_E_USD_YOY VARCHAR2(20),
    MON_E_CNY_AMOUNT VARCHAR2(50),
    MON_E_CNY_YOY VARCHAR2(20),
    MON_E_USD_AMOUNT VARCHAR2(50),
    MON_E_USD_YOY VARCHAR2(20),
    ACC_I_CNY_AMOUNT VARCHAR2(50),
    ACC_I_CNY_YOY VARCHAR2(20),
    ACC_I_USD_AMOUNT VARCHAR2(50),
    ACC_I_USD_YOY VARCHAR2(20),
    MON_I_CNY_AMOUNT VARCHAR2(50),
    MON_I_CNY_YOY VARCHAR2(20),
    MON_I_USD_AMOUNT VARCHAR2(50),
    MON_I_USD_YOY VARCHAR2(20),
    ACC_E_AMOUNT VARCHAR2(50),
    ACC_E_AMOUNT_UNIT VARCHAR2(50),
    ACC_E_AMOUNT_YOY VARCHAR2(20),
    MON_E_AMOUNT VARCHAR2(50),
    MON_E_AMOUNT_UNIT VARCHAR2(50),
    MON_E_AMOUNT_YOY VARCHAR2(20),
    ACC_I_AMOUNT VARCHAR2(50),
    ACC_I_AMOUNT_UNIT VARCHAR2(50),
    ACC_I_AMOUNT_YOY VARCHAR2(20),
    MON_I_AMOUNT VARCHAR2(50),
    MON_I_AMOUNT_UNIT VARCHAR2(50),
    MON_I_AMOUNT_YOY VARCHAR2(20),
    RANK_MARKERS VARCHAR2(10),
    DATA_SOURCE VARCHAR2(10),
    EMPHASIS_OR_EMERGING_MARK VARCHAR2(10),
    CREATE_TIME VARCHAR2(20)
)
ORGANIZATION EXTERNAL (
    TYPE ORACLE_LOADER
    DEFAULT DIRECTORY DATA_DIR
    ACCESS PARAMETERS (
        RECORDS DELIMITED BY NEWLINE
        FIELDS TERMINATED BY ',' OPTIONALLY ENCLOSED BY '"'
        MISSING FIELD VALUES ARE NULL
    )
    LOCATION ('T_STATISTICAL_CUS_TOTAL_ZHEJIANG.csv')
)
REJECT LIMIT UNLIMITED;

-- 从外部表导入数据
INSERT INTO T_STATISTICAL_CUS_TOTAL (
    STAT_DATE, STAT_TYPE, STAT_NAME, STAT_CODE, STAT_CONTENT_RAW, STAT_CONTENT_CLEANSE,
    ACC_A_CNY_AMOUNT, ACC_A_CNY_YOY, ACC_A_USD_AMOUNT, ACC_A_USD_YOY,
    MON_A_CNY_AMOUNT, MON_A_CNY_YOY, MON_A_USD_AMOUNT, MON_A_USD_YOY,
    ACC_E_CNY_AMOUNT, ACC_E_CNY_YOY, ACC_E_USD_AMOUNT, ACC_E_USD_YOY,
    MON_E_CNY_AMOUNT, MON_E_CNY_YOY, MON_E_USD_AMOUNT, MON_E_USD_YOY,
    ACC_I_CNY_AMOUNT, ACC_I_CNY_YOY, ACC_I_USD_AMOUNT, ACC_I_USD_YOY,
    MON_I_CNY_AMOUNT, MON_I_CNY_YOY, MON_I_USD_AMOUNT, MON_I_USD_YOY,
    ACC_E_AMOUNT, ACC_E_AMOUNT_UNIT, ACC_E_AMOUNT_YOY,
    MON_E_AMOUNT, MON_E_AMOUNT_UNIT, MON_E_AMOUNT_YOY,
    ACC_I_AMOUNT, ACC_I_AMOUNT_UNIT, ACC_I_AMOUNT_YOY,
    MON_I_AMOUNT, MON_I_AMOUNT_UNIT, MON_I_AMOUNT_YOY,
    RANK_MARKERS, DATA_SOURCE, EMPHASIS_OR_EMERGING_MARK, CREATE_TIME
)
SELECT 
    STAT_DATE, STAT_TYPE, STAT_NAME, STAT_CODE, STAT_CONTENT_RAW, STAT_CONTENT_CLEANSE,
    DECODE(TRIM(ACC_A_CNY_AMOUNT), '', NULL, TO_NUMBER(TRIM(ACC_A_CNY_AMOUNT))) ACC_A_CNY_AMOUNT,
    DECODE(TRIM(ACC_A_CNY_YOY), '', NULL, TO_NUMBER(TRIM(ACC_A_CNY_YOY))) ACC_A_CNY_YOY,
    DECODE(TRIM(ACC_A_USD_AMOUNT), '', NULL, TO_NUMBER(TRIM(ACC_A_USD_AMOUNT))) ACC_A_USD_AMOUNT,
    DECODE(TRIM(ACC_A_USD_YOY), '', NULL, TO_NUMBER(TRIM(ACC_A_USD_YOY))) ACC_A_USD_YOY,
    DECODE(TRIM(MON_A_CNY_AMOUNT), '', NULL, TO_NUMBER(TRIM(MON_A_CNY_AMOUNT))) MON_A_CNY_AMOUNT,
    DECODE(TRIM(MON_A_CNY_YOY), '', NULL, TO_NUMBER(TRIM(MON_A_CNY_YOY))) MON_A_CNY_YOY,
    DECODE(TRIM(MON_A_USD_AMOUNT), '', NULL, TO_NUMBER(TRIM(MON_A_USD_AMOUNT))) MON_A_USD_AMOUNT,
    DECODE(TRIM(MON_A_USD_YOY), '', NULL, TO_NUMBER(TRIM(MON_A_USD_YOY))) MON_A_USD_YOY,
    DECODE(TRIM(ACC_E_CNY_AMOUNT), '', NULL, TO_NUMBER(TRIM(ACC_E_CNY_AMOUNT))) ACC_E_CNY_AMOUNT,
    DECODE(TRIM(ACC_E_CNY_YOY), '', NULL, TO_NUMBER(TRIM(ACC_E_CNY_YOY))) ACC_E_CNY_YOY,
    DECODE(TRIM(ACC_E_USD_AMOUNT), '', NULL, TO_NUMBER(TRIM(ACC_E_USD_AMOUNT))) ACC_E_USD_AMOUNT,
    DECODE(TRIM(ACC_E_USD_YOY), '', NULL, TO_NUMBER(TRIM(ACC_E_USD_YOY))) ACC_E_USD_YOY,
    DECODE(TRIM(MON_E_CNY_AMOUNT), '', NULL, TO_NUMBER(TRIM(MON_E_CNY_AMOUNT))) MON_E_CNY_AMOUNT,
    DECODE(TRIM(MON_E_CNY_YOY), '', NULL, TO_NUMBER(TRIM(MON_E_CNY_YOY))) MON_E_CNY_YOY,
    DECODE(TRIM(MON_E_USD_AMOUNT), '', NULL, TO_NUMBER(TRIM(MON_E_USD_AMOUNT))) MON_E_USD_AMOUNT,
    DECODE(TRIM(MON_E_USD_YOY), '', NULL, TO_NUMBER(TRIM(MON_E_USD_YOY))) MON_E_USD_YOY,
    DECODE(TRIM(ACC_I_CNY_AMOUNT), '', NULL, TO_NUMBER(TRIM(ACC_I_CNY_AMOUNT))) ACC_I_CNY_AMOUNT,
    DECODE(TRIM(ACC_I_CNY_YOY), '', NULL, TO_NUMBER(TRIM(ACC_I_CNY_YOY))) ACC_I_CNY_YOY,
    DECODE(TRIM(ACC_I_USD_AMOUNT), '', NULL, TO_NUMBER(TRIM(ACC_I_USD_AMOUNT))) ACC_I_USD_AMOUNT,
    DECODE(TRIM(ACC_I_USD_YOY), '', NULL, TO_NUMBER(TRIM(ACC_I_USD_YOY))) ACC_I_USD_YOY,
    DECODE(TRIM(MON_I_CNY_AMOUNT), '', NULL, TO_NUMBER(TRIM(MON_I_CNY_AMOUNT))) MON_I_CNY_AMOUNT,
    DECODE(TRIM(MON_I_CNY_YOY), '', NULL, TO_NUMBER(TRIM(MON_I_CNY_YOY))) MON_I_CNY_YOY,
    DECODE(TRIM(MON_I_USD_AMOUNT), '', NULL, TO_NUMBER(TRIM(MON_I_USD_AMOUNT))) MON_I_USD_AMOUNT,
    DECODE(TRIM(MON_I_USD_YOY), '', NULL, TO_NUMBER(TRIM(MON_I_USD_YOY))) MON_I_USD_YOY,
    DECODE(TRIM(ACC_E_AMOUNT), '', NULL, TO_NUMBER(TRIM(ACC_E_AMOUNT))) ACC_E_AMOUNT,
    ACC_E_AMOUNT_UNIT,
    DECODE(TRIM(ACC_E_AMOUNT_YOY), '', NULL, TO_NUMBER(TRIM(ACC_E_AMOUNT_YOY))) ACC_E_AMOUNT_YOY,
    DECODE(TRIM(MON_E_AMOUNT), '', NULL, TO_NUMBER(TRIM(MON_E_AMOUNT))) MON_E_AMOUNT,
    MON_E_AMOUNT_UNIT,
    DECODE(TRIM(MON_E_AMOUNT_YOY), '', NULL, TO_NUMBER(TRIM(MON_E_AMOUNT_YOY))) MON_E_AMOUNT_YOY,
    DECODE(TRIM(ACC_I_AMOUNT), '', NULL, TO_NUMBER(TRIM(ACC_I_AMOUNT))) ACC_I_AMOUNT,
    ACC_I_AMOUNT_UNIT,
    DECODE(TRIM(ACC_I_AMOUNT_YOY), '', NULL, TO_NUMBER(TRIM(ACC_I_AMOUNT_YOY))) ACC_I_AMOUNT_YOY,
    DECODE(TRIM(MON_I_AMOUNT), '', NULL, TO_NUMBER(TRIM(MON_I_AMOUNT))) MON_I_AMOUNT,
    MON_I_AMOUNT_UNIT,
    DECODE(TRIM(MON_I_AMOUNT_YOY), '', NULL, TO_NUMBER(TRIM(MON_I_AMOUNT_YOY))) MON_I_AMOUNT_YOY,
    RANK_MARKERS, DATA_SOURCE, EMPHASIS_OR_EMERGING_MARK, CREATE_TIME
FROM T_STATISTICAL_CUS_TOTAL_EXT;
*/

-- 方法3：使用PL/SQL逐行插入（适合小数据量）
/*
DECLARE
    v_file UTL_FILE.FILE_TYPE;
    v_line VARCHAR2(4000);
    v_csv_data CLOB;
BEGIN
    -- 清空表
    DELETE FROM T_STATISTICAL_CUS_TOTAL;
    COMMIT;
    
    -- 打开CSV文件
    v_file := UTL_FILE.FOPEN('DATA_DIR', 'T_STATISTICAL_CUS_TOTAL_ZHEJIANG.csv', 'R');
    
    -- 读取文件内容
    BEGIN
        LOOP
            UTL_FILE.GET_LINE(v_file, v_line);
            v_csv_data := v_csv_data || v_line || CHR(10);
        END LOOP;
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            NULL;
    END;
    
    UTL_FILE.FCLOSE(v_file);
    
    -- 这里可以添加解析CSV并插入数据的逻辑
    -- 由于CSV数据量较大，建议使用前两种方法
    
    DBMS_OUTPUT.PUT_LINE('数据导入完成');
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('错误: ' || SQLERRM);
        IF UTL_FILE.IS_OPEN(v_file) THEN
            UTL_FILE.FCLOSE(v_file);
        END IF;
END;
/
*/

-- 验证数据导入结果
SELECT 
    COUNT(*) AS 总记录数,
    COUNT(DISTINCT STAT_TYPE) AS 统计类型数量,
    COUNT(DISTINCT STAT_NAME) AS 统计名称数量,
    COUNT(DISTINCT DATA_SOURCE) AS 数据源数量,
    MIN(STAT_DATE) AS 最早统计日期,
    MAX(STAT_DATE) AS 最晚统计日期
FROM T_STATISTICAL_CUS_TOTAL;

-- 按统计类型分组统计
SELECT 
    STAT_TYPE,
    STAT_NAME,
    COUNT(*) AS 记录数,
    SUM(MON_A_CNY_AMOUNT) AS 当月进出口总额,
    SUM(MON_E_CNY_AMOUNT) AS 当月出口总额,
    SUM(MON_I_CNY_AMOUNT) AS 当月进口总额
FROM T_STATISTICAL_CUS_TOTAL
GROUP BY STAT_TYPE, STAT_NAME
ORDER BY STAT_TYPE;

-- 检查数据质量
SELECT 
    COUNT(*) AS 总记录数,
    COUNT(CASE WHEN STAT_DATE IS NULL THEN 1 END) AS 日期空值数,
    COUNT(CASE WHEN STAT_TYPE IS NULL THEN 1 END) AS 类型空值数,
    COUNT(CASE WHEN STAT_CONTENT_RAW IS NULL THEN 1 END) AS 内容空值数,
    COUNT(CASE WHEN DATA_SOURCE IS NULL THEN 1 END) AS 数据源空值数
FROM T_STATISTICAL_CUS_TOTAL;

PRINT '数据导入验证完成！';