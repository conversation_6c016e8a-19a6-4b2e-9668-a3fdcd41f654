import pandas as pd
import numpy as np

# 读取原始数据文件
try:
    # --- 1. 读取原始数据 ---
    raw_data_file = '（13）2025年5月出口主要商品量值表（人民币值）.xls'
    # 使用多级表头读取Excel，跳过文件顶部无关行
    df = pd.read_excel(raw_data_file, header=[2, 3], skiprows=1)

    # --- 2. 清洗和重命名列 ---
    # 更健壮的列名清理逻辑
    new_cols = []
    for col in df.columns.values:
        part1 = str(col[0]).strip()
        part2 = str(col[1]).strip()
        if 'Unnamed' in part1 and 'Unnamed' in part2:
            new_cols.append('drop_me')
        elif 'Unnamed' in part2:
            new_cols.append(part1)
        else:
            new_cols.append(f"{part1}_{part2}")
    df.columns = new_cols
    
    if 'drop_me' in df.columns:
        df = df.drop(columns=['drop_me'])

    # 定义列名映射
    column_mapping = {
        '商品名称': 'STAT_CONTENT_RAW',
        '计量单位': 'UNIT',
        '5月_数量': 'MON_E_AMOUNT',
        '5月_金额': 'MON_E_CNY_AMOUNT',
        '1至5月累计_数量': 'ACC_E_AMOUNT',
        '1至5月累计_金额': 'ACC_E_CNY_AMOUNT',
        '当月比去年同期±%_数量': 'MON_E_AMOUNT_YOY',
        '当月比去年同期±%_金额': 'MON_E_CNY_YOY',
        '累计比去年同期±%_数量': 'ACC_E_AMOUNT_YOY',
        '累计比去年同期±%_金额': 'ACC_E_CNY_YOY',
    }
    df = df.rename(columns=column_mapping)
    
    # --- 3. 数据转换和类型清理 ---
    # 转换金额 (万元 -> 元)
    df['MON_E_CNY_AMOUNT'] = pd.to_numeric(df['MON_E_CNY_AMOUNT'], errors='coerce') * 10000
    df['ACC_E_CNY_AMOUNT'] = pd.to_numeric(df['ACC_E_CNY_AMOUNT'], errors='coerce') * 10000

    # 转换数值类型，无法转换的设为NaN
    numeric_cols = [
        'MON_E_AMOUNT', 'ACC_E_AMOUNT', 'MON_E_AMOUNT_YOY', 'MON_E_CNY_YOY',
        'ACC_E_AMOUNT_YOY', 'ACC_E_CNY_YOY'
    ]
    for col in numeric_cols:
        df[col] = pd.to_numeric(df[col], errors='coerce')

    df.dropna(subset=['STAT_CONTENT_RAW'], inplace=True)
    df = df[df['STAT_CONTENT_RAW'] != '其中：'].copy()
    
    # --- 4. 添加新列 ---
    df['STAT_DATE'] = '2025-05-01'
    df['STAT_TYPE'] = '04'
    df['STAT_NAME'] = '主出商品'
    df['DATA_SOURCE'] = '01'
    df['CREATE_TIME'] = pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')
    df['STAT_CONTENT_CLEANSE'] = df['STAT_CONTENT_RAW'].str.strip()
    df['MON_E_AMOUNT_UNIT'] = df['UNIT']
    df['ACC_E_AMOUNT_UNIT'] = df['UNIT']
    df['RANK_MARKERS'] = '01' # 默认参与排名
    df['EMPHASIS_OR_EMERGING_MARK'] = '' # 默认为空

    # 定义最终数据库表的所有列顺序
    final_columns_order = [
        'STAT_DATE', 'STAT_TYPE', 'STAT_NAME', 'STAT_CODE', 'STAT_CONTENT_RAW', 
        'STAT_CONTENT_CLEANSE', 'ACC_A_CNY_AMOUNT', 'ACC_A_CNY_YOY', 'ACC_A_USD_AMOUNT', 
        'ACC_A_USD_YOY', 'MON_A_CNY_AMOUNT', 'MON_A_CNY_YOY', 'MON_A_USD_AMOUNT', 
        'MON_A_USD_YOY', 'ACC_E_CNY_AMOUNT', 'ACC_E_CNY_YOY', 'ACC_E_USD_AMOUNT', 
        'ACC_E_USD_YOY', 'MON_E_CNY_AMOUNT', 'MON_E_CNY_YOY', 'MON_E_USD_AMOUNT', 
        'MON_E_USD_YOY', 'ACC_I_CNY_AMOUNT', 'ACC_I_CNY_YOY', 'ACC_I_USD_AMOUNT', 
        'ACC_I_USD_YOY', 'MON_I_CNY_AMOUNT', 'MON_I_CNY_YOY', 'MON_I_USD_AMOUNT', 
        'MON_I_USD_YOY', 'ACC_E_AMOUNT', 'ACC_E_AMOUNT_UNIT', 'ACC_E_AMOUNT_YOY', 
        'MON_E_AMOUNT', 'MON_E_AMOUNT_UNIT', 'MON_E_AMOUNT_YOY', 'ACC_I_AMOUNT', 
        'ACC_I_AMOUNT_UNIT', 'ACC_I_AMOUNT_YOY', 'MON_I_AMOUNT', 'MON_I_AMOUNT_UNIT', 
        'MON_I_AMOUNT_YOY', 'RANK_MARKERS', 'DATA_SOURCE', 'EMPHASIS_OR_EMERGING_MARK', 
        'CREATE_TIME'
    ]

    # 创建一个空的DataFrame，其列与最终表结构一致
    final_df = pd.DataFrame(columns=final_columns_order)

    # 将我们处理好的数据合并进去
    for col in final_df.columns:
        if col in df.columns:
            final_df[col] = df[col]
        else:
            # 对于不存在的列，用NaN填充
            final_df[col] = np.nan
            
    # 特殊处理STAT_DATE的格式
    final_df['STAT_DATE'] = pd.to_datetime(final_df['STAT_DATE']).dt.strftime('%Y/%m/%d')
    final_df['EMPHASIS_OR_EMERGING_MARK'] = np.nan

    # --- 5. 保存到Excel ---
    # 按要求，将清洗好的数据保存到目标Excel的新sheet页
    # 为避免破坏原始文件，我们先写入一个新文件
    output_filename = 'T_STATISTICAL_CUS_TOTAL_PROCESSED.xlsx'
    with pd.ExcelWriter(output_filename, engine='openpyxl') as writer:
        final_df.to_excel(writer, sheet_name='主出商品', index=False)
    
    print("--- 脚本执行成功 ---")
    print(f"处理完成的数据已保存到文件: '{output_filename}' 的 '主出商品' sheet页。")
    print("\n最终数据预览 (前5行):")
    print(final_df.head())


except FileNotFoundError as e:
    print(f"错误：文件未找到。 {e}")
except Exception as e:
    print(f"处理数据时出错：{e}") 