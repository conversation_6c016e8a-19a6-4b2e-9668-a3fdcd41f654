import pandas as pd
import numpy as np
import datetime

def transform_zhejiang_data(input_excel_path, output_csv_path):
    """
    读取浙江海关数据Excel文件，将其转换为符合Oracle表结构的CSV文件。
    完全匹配T_STATISTICAL_CUS_TOTAL表的43个字段。
    """
    try:
        xls = pd.ExcelFile(input_excel_path, engine='openpyxl')
    except FileNotFoundError:
        print(f"错误：找不到文件 {input_excel_path}")
        return

    output_dfs = []

    # 目标CSV文件的列名（完全匹配Oracle表结构）
    output_columns = [
        "STAT_DATE", "STAT_TYPE", "STAT_NAME", "STAT_CODE", "STAT_CONTENT_RAW",
        "STAT_CONTENT_CLEANSE", "ACC_A_CNY_AMOUNT", "ACC_A_CNY_YOY", "ACC_A_USD_AMOUNT",
        "ACC_A_USD_YOY", "MON_A_CNY_AMOUNT", "MON_A_CNY_YOY", "MON_A_USD_AMOUNT",
        "MON_A_USD_YOY", "ACC_E_CNY_AMOUNT", "ACC_E_CNY_YOY", "ACC_E_USD_AMOUNT",
        "ACC_E_USD_YOY", "MON_E_CNY_AMOUNT", "MON_E_CNY_YOY", "MON_E_USD_AMOUNT",
        "MON_E_USD_YOY", "ACC_I_CNY_AMOUNT", "ACC_I_CNY_YOY", "ACC_I_USD_AMOUNT",
        "ACC_I_USD_YOY", "MON_I_CNY_AMOUNT", "MON_I_CNY_YOY", "MON_I_USD_AMOUNT",
        "MON_I_USD_YOY", "ACC_E_AMOUNT", "ACC_E_AMOUNT_UNIT", "ACC_E_AMOUNT_YOY",
        "MON_E_AMOUNT", "MON_E_AMOUNT_UNIT", "MON_E_AMOUNT_YOY", "ACC_I_AMOUNT",
        "ACC_I_AMOUNT_UNIT", "ACC_I_AMOUNT_YOY", "MON_I_AMOUNT", "MON_I_AMOUNT_UNIT",
        "MON_I_AMOUNT_YOY", "RANK_MARKERS", "DATA_SOURCE", "EMPHASIS_OR_EMERGING_MARK",
        "CREATE_TIME"
    ]

    # 工作表名称到 STAT_TYPE 的映射
    sheet_to_stat_type = {
        '贸易方式': ('01', '贸易方式'),
        '企业性质': ('02', '企业性质'),
        '主要国别（地区）': ('03', '国别地区'),
        '主出商品': ('04', '主出商品'),
        '主进商品': ('05', '主进商品'),
        '十一地市': ('03', '国别地区'),
    }

    for sheet_name, (stat_type, stat_name) in sheet_to_stat_type.items():
        try:
            df = xls.parse(sheet_name)
            print(f"正在处理工作表: {sheet_name}")
            
            # 数据清洗：过滤掉空行和单位行
            # 查找项目列名
            project_col = None
            for col in df.columns:
                if '项目' in str(col) or 'Unnamed' in str(col):
                    project_col = col
                    break
            
            if project_col is None:
                print(f"警告：工作表 {sheet_name} 中未找到项目列")
                continue
                
            # 过滤掉空值、单位行、总计行等
            clean_df = df[df[project_col].notna()]
            clean_df = clean_df[~clean_df[project_col].astype(str).str.contains('单位|合计|总计|nan', case=False, na=False)]
            clean_df = clean_df[clean_df[project_col].astype(str).str.strip() != '']
            
            print(f"清洗后数据行数: {len(clean_df)} (原始: {len(df)})")
            
            # 创建一个临时的DataFrame来存储当前工作表的数据
            temp_df = pd.DataFrame(columns=output_columns)
            
            # 基础字段
            temp_df['STAT_CONTENT_RAW'] = clean_df[project_col]
            # 数据清洗：去除多余空格
            temp_df['STAT_CONTENT_CLEANSE'] = clean_df[project_col].astype(str).str.strip()
            temp_df['STAT_TYPE'] = stat_type
            temp_df['STAT_NAME'] = stat_name
            temp_df['STAT_CODE'] = ''
            
            # 处理不同工作表的数据结构
            if sheet_name in ['贸易方式', '企业性质', '主要国别（地区）', '十一地市']:
                # 这些表有标准的进出口结构
                # 注意：Excel中的单位是万元，需要转换为亿元（除以10000）
                if '当期进出口' in clean_df.columns:
                    temp_df['MON_A_CNY_AMOUNT'] = pd.to_numeric(clean_df['当期进出口'], errors='coerce') / 10000
                if '当期进口' in clean_df.columns:
                    temp_df['MON_I_CNY_AMOUNT'] = pd.to_numeric(clean_df['当期进口'], errors='coerce') / 10000
                if '当期出口' in clean_df.columns:
                    temp_df['MON_E_CNY_AMOUNT'] = pd.to_numeric(clean_df['当期出口'], errors='coerce') / 10000
                
                # 同比数据
                if '进出口同比' in clean_df.columns:
                    temp_df['MON_A_CNY_YOY'] = pd.to_numeric(clean_df['进出口同比'], errors='coerce')
                if '进口同比' in clean_df.columns:
                    temp_df['MON_I_CNY_YOY'] = pd.to_numeric(clean_df['进口同比'], errors='coerce')
                if '出口同比' in clean_df.columns:
                    temp_df['MON_E_CNY_YOY'] = pd.to_numeric(clean_df['出口同比'], errors='coerce')
                    
            elif sheet_name in ['主出商品', '主进商品']:
                # 商品表有不同结构
                # 注意：Excel中的单位是万元，需要转换为亿元（除以10000）
                if '当期' in clean_df.columns:
                    if sheet_name == '主出商品':
                        temp_df['MON_E_CNY_AMOUNT'] = pd.to_numeric(clean_df['当期'], errors='coerce') / 10000
                        temp_df['MON_E_CNY_YOY'] = pd.to_numeric(clean_df['同比'], errors='coerce')
                    else:  # 主进商品
                        temp_df['MON_I_CNY_AMOUNT'] = pd.to_numeric(clean_df['当期'], errors='coerce') / 10000
                        temp_df['MON_I_CNY_YOY'] = pd.to_numeric(clean_df['同比'], errors='coerce')
            
            # 设置累计数据为空（浙江数据只有当月数据）
            acc_columns = [
                'ACC_A_CNY_AMOUNT', 'ACC_A_CNY_YOY', 'ACC_A_USD_AMOUNT', 'ACC_A_USD_YOY',
                'ACC_E_CNY_AMOUNT', 'ACC_E_CNY_YOY', 'ACC_E_USD_AMOUNT', 'ACC_E_USD_YOY',
                'ACC_I_CNY_AMOUNT', 'ACC_I_CNY_YOY', 'ACC_I_USD_AMOUNT', 'ACC_I_USD_YOY',
                'ACC_E_AMOUNT', 'ACC_E_AMOUNT_UNIT', 'ACC_E_AMOUNT_YOY',
                'ACC_I_AMOUNT', 'ACC_I_AMOUNT_UNIT', 'ACC_I_AMOUNT_YOY'
            ]
            for col in acc_columns:
                temp_df[col] = np.nan
            
            # 设置美元金额为空（浙江数据只有人民币金额）
            usd_columns = [
                'ACC_A_USD_AMOUNT', 'ACC_A_USD_YOY', 'MON_A_USD_AMOUNT', 'MON_A_USD_YOY',
                'ACC_E_USD_AMOUNT', 'ACC_E_USD_YOY', 'MON_E_USD_AMOUNT', 'MON_E_USD_YOY',
                'ACC_I_USD_AMOUNT', 'ACC_I_USD_YOY', 'MON_I_USD_AMOUNT', 'MON_I_USD_YOY'
            ]
            for col in usd_columns:
                temp_df[col] = np.nan
                
            # 设置数量单位为空
            amount_unit_columns = [
                'MON_E_AMOUNT_UNIT', 'MON_I_AMOUNT_UNIT', 'ACC_E_AMOUNT_UNIT', 'ACC_I_AMOUNT_UNIT'
            ]
            for col in amount_unit_columns:
                temp_df[col] = ''
            
            # 设置当月数量为空
            temp_df['MON_E_AMOUNT'] = np.nan
            temp_df['MON_I_AMOUNT'] = np.nan
            temp_df['MON_E_AMOUNT_YOY'] = np.nan
            temp_df['MON_I_AMOUNT_YOY'] = np.nan
            
            output_dfs.append(temp_df)
            
        except Exception as e:
            print(f"处理工作表 {sheet_name} 时出错: {e}")
            continue

    # 合并所有处理过的数据
    if output_dfs:
        final_df = pd.concat(output_dfs, ignore_index=True)
    else:
        print("没有成功处理任何数据")
        return

    # 填充公共字段
    final_df['STAT_DATE'] = '2025/6/1'
    final_df['DATA_SOURCE'] = '02'  # 浙江
    final_df['EMPHASIS_OR_EMERGING_MARK'] = ''
    final_df['RANK_MARKERS'] = ''
    final_df['CREATE_TIME'] = datetime.datetime.now().strftime('%Y/%m/%d %H:%M:%S')
    
    # 确保所有列都存在
    for col in output_columns:
        if col not in final_df.columns:
            final_df[col] = np.nan if 'AMOUNT' in col or 'YOY' in col else ''
    
    # 按照目标顺序排列列
    final_df = final_df[output_columns]
    
    # 数据清理：将空字符串替换为NaN，然后填充适当的默认值
    for col in final_df.columns:
        if 'AMOUNT' in col or 'YOY' in col:
            final_df[col] = pd.to_numeric(final_df[col], errors='coerce')
        elif 'UNIT' in col or 'CODE' in col or 'MARKERS' in col or 'MARK' in col:
            final_df[col] = final_df[col].fillna('').astype(str)
    
    # 将处理好的数据保存为CSV文件
    final_df.to_csv(output_csv_path, index=False, encoding='utf-8-sig', float_format='%.4f')
    print(f"数据已成功转换并保存到 {output_csv_path}")
    print(f"总记录数: {len(final_df)}")
    print(f"字段数: {len(final_df.columns)}")


if __name__ == '__main__':
    excel_file = '浙江单6月数据.xlsx'
    csv_file = 'T_STATISTICAL_CUS_TOTAL_ZHEJIANG.csv'
    transform_zhejiang_data(excel_file, csv_file)
    
    # 显示数据预览
    try:
        result_df = pd.read_csv(csv_file, encoding='utf-8-sig')
        print("\n数据预览:")
        print(result_df.head())
        print(f"\n数据形状: {result_df.shape}")
    except Exception as e:
        print(f"读取结果文件时出错: {e}") 