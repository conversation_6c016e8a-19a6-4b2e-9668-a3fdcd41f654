from DrissionPage import ChromiumPage
import pandas as pd
import os
import time
import re

# 创建存储目录
output_dir = "提取的表格数据_人民币版"
if not os.path.exists(output_dir):
    os.makedirs(output_dir)
    print(f"创建输出目录: {output_dir}")

# 初始化浏览器页面对象
page = ChromiumPage()

def extract_table_from_url(url, file_name):
    """从指定URL提取表格数据并保存为Excel"""
    try:
        print(f"访问URL: {url}")
        page.get(url)
        time.sleep(3)  # 等待页面完全加载，增加时间确保加载完成
        
        # 首先尝试通过 easysiteText ID 定位内容区域
        content_div = page.ele('#easysiteText')
        
        if content_div:
            print("已找到内容区域 #easysiteText")
            # 在内容区域中查找表格
            table_elements = content_div.eles('tag:table')
        else:
            print("未找到 #easysiteText 区域，尝试在整个页面查找表格")
            # 如果找不到内容区域，则在整个页面中查找表格
            table_elements = page.eles('tag:table')
        
        if not table_elements:
            print("未找到表格元素")
            return False
        
        print(f"找到 {len(table_elements)} 个表格元素")
        
        # 找到最有可能是目标表格的元素（通常是拥有最多行的表格）
        target_table = None
        max_rows = 0
        
        for i, table in enumerate(table_elements):
            rows = table.eles('tag:tr')
            if len(rows) > max_rows:
                max_rows = len(rows)
                target_table = table
            print(f"表格 {i+1}: {len(rows)} 行")
        
        if not target_table:
            print("未找到合适的表格")
            return False
        
        print(f"选择了具有 {max_rows} 行的表格")
        
        # 获取表格HTML
        html_content = target_table.html
        
        try:
            # 尝试使用不同表头配置解析表格
            try:
                # 先尝试双行表头
                dfs = pd.read_html(html_content, header=[0,1], flavor='bs4')
                df = dfs[0]
                # 合并多级表头
                df.columns = [' '.join(str(col) for col in cols if str(col) != 'nan').strip() for cols in df.columns.values]
            except Exception as e:
                print(f"尝试双行表头失败: {e}")
                try:
                    # 尝试单行表头
                    dfs = pd.read_html(html_content, header=0, flavor='bs4')
                    df = dfs[0]
                except Exception as e:
                    print(f"尝试单行表头失败: {e}")
                    # 无表头
                    dfs = pd.read_html(html_content, header=None, flavor='bs4')
                    df = dfs[0]
            
            # 保存为Excel
            file_path = os.path.join(output_dir, file_name)
            df.to_excel(file_path, index=False)
            print(f"表格数据已保存到: {file_path}")
            
            # 保存原始HTML，便于调试
            html_file_path = os.path.join(output_dir, f"{os.path.splitext(file_name)[0]}_原始表格.html")
            with open(html_file_path, 'w', encoding='utf-8') as html_file:
                html_file.write(f"<html><body>{html_content}</body></html>")
            print(f"原始HTML已保存到: {html_file_path}")
            
            return True
        except Exception as e:
            print(f"解析表格数据时出错: {e}")
            
            # 保存原始HTML到文件
            html_file_path = os.path.join(output_dir, f"{os.path.splitext(file_name)[0]}_原始表格.html")
            with open(html_file_path, 'w', encoding='utf-8') as html_file:
                html_file.write(f"<html><body>{html_content}</body></html>")
            print(f"无法解析表格，原始HTML已保存到: {html_file_path}")
            
            return False
    except Exception as e:
        print(f"处理URL时出错: {e}")
        return False

def batch_process_links(link_file, target_years=None, target_months=None):
    """批量处理链接文件中的URL"""
    if not os.path.exists(link_file):
        print(f"链接文件不存在: {link_file}")
        return
    
    all_links = []
    with open(link_file, 'r', encoding='utf-8') as f:
        for line in f:
            parts = line.strip().split(',', 2)
            if len(parts) == 3:
                year, month, url = parts
                # 过滤年份和月份
                if (not target_years or year in target_years) and (not target_months or month in target_months):
                    all_links.append((year, month, url))
    
    print(f"共加载 {len(all_links)} 个链接")
    
    success_count = 0
    fail_count = 0
    
    for i, (year, month, url) in enumerate(all_links, 1):
        print(f"\n[{i}/{len(all_links)}] 处理 {year}年{month} 的数据")
        
        # 构造文件名
        file_name = f"{year}年{month}_进出口商品收发货人所在地总值表_人民币.xlsx"
        
        # 提取表格
        result = extract_table_from_url(url, file_name)
        
        if result:
            success_count += 1
        else:
            fail_count += 1
        
        # 暂停一段时间，避免请求过于频繁
        time.sleep(3)
    
    print(f"\n处理完成! 成功: {success_count}, 失败: {fail_count}")
    return success_count, fail_count

if __name__ == "__main__":
    # 链接文件
    link_file = "进出口商品收发货人所在地总值表_所有链接 - 副本.txt"
    
    print("=== 提取人民币版表格数据 ===")
    print("请选择处理方式:")
    print("1. 处理所有链接")
    print("2. 按年份处理")
    print("3. 按月份处理") 
    print("4. 按年份和月份处理")
    
    choice = input("请输入选择 (1/2/3/4): ")
    
    target_years = None
    target_months = None
    
    if choice == '2' or choice == '4':
        years_input = input("请输入要处理的年份（用逗号分隔，例如: 2014,2015,2016）: ")
        target_years = [year.strip() for year in years_input.split(',')]
    
    if choice == '3' or choice == '4':
        months_input = input("请输入要处理的月份（用逗号分隔，例如: 1月,2月,3月）: ")
        target_months = [month.strip() for month in months_input.split(',')]
    
    # 执行批量处理
    batch_process_links(link_file, target_years, target_months)
    
    # 关闭浏览器
    page.quit() 