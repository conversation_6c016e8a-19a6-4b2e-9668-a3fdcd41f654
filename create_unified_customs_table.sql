-- =====================================================
-- 海关进出口统计数据统一大表 DDL
-- 表名: CUS_TRADE_UNIFIED_STATISTICS
-- 用途: 整合所有18张海关统计分表数据
-- 创建日期: 2025-01-05
-- =====================================================

-- 删除现有表（如果存在）
DROP TABLE CUS_TRADE_UNIFIED_STATISTICS CASCADE CONSTRAINTS;

-- 创建统一大表
CREATE TABLE CUS_TRADE_UNIFIED_STATISTICS (
    -- 主键ID
    ID                      NUMBER(20) GENERATED BY DEFAULT AS IDENTITY,
    
    -- 核心维度字段
    STAT_DATE               DATE NOT NULL,                    -- 统计日期
    STAT_TYPE               VARCHAR2(10) NOT NULL,            -- 统计类型编码
    STAT_TYPE_NAME          VARCHAR2(100),                    -- 统计类型名称
    CURRENCY_TYPE           VARCHAR2(10) NOT NULL,            -- 货币类型(人民币/美元)
    UNIT                    VARCHAR2(50),                     -- 计量单位
    
    -- 分类维度字段
    DIMENSION_1             VARCHAR2(500),                    -- 主要分类维度
    DIMENSION_2             VARCHAR2(500),                    -- 次要分类维度  
    DIMENSION_3             VARCHAR2(500),                    -- 第三分类维度
    TRADE_DIRECTION         VARCHAR2(10),                     -- 贸易方向(进口/出口/进出口)
    
    -- 当月金额指标
    MONTH_IE_AMOUNT         NUMBER(19,4),                     -- 当月进出口金额
    MONTH_EXP_AMOUNT        NUMBER(19,4),                     -- 当月出口金额
    MONTH_IMP_AMOUNT        NUMBER(19,4),                     -- 当月进口金额
    MONTH_TB_AMOUNT         NUMBER(19,4),                     -- 当月贸易差额
    
    -- 累计金额指标
    YTD_IE_AMOUNT           NUMBER(19,4),                     -- 累计进出口金额
    YTD_EXP_AMOUNT          NUMBER(19,4),                     -- 累计出口金额
    YTD_IMP_AMOUNT          NUMBER(19,4),                     -- 累计进口金额
    YTD_TB_AMOUNT           NUMBER(19,4),                     -- 累计贸易差额
    
    -- 当月同比指标(%)
    MONTH_IE_YOY            NUMBER(10,2),                     -- 当月进出口同比
    MONTH_EXP_YOY           NUMBER(10,2),                     -- 当月出口同比
    MONTH_IMP_YOY           NUMBER(10,2),                     -- 当月进口同比
    MONTH_TB_YOY            NUMBER(10,2),                     -- 当月贸易差额同比
    
    -- 累计同比指标(%)
    YTD_IE_YOY              NUMBER(10,2),                     -- 累计进出口同比
    YTD_EXP_YOY             NUMBER(10,2),                     -- 累计出口同比
    YTD_IMP_YOY             NUMBER(10,2),                     -- 累计进口同比
    YTD_TB_YOY              NUMBER(10,2),                     -- 累计贸易差额同比
    
    -- 环比指标(%)
    MONTH_IE_MOM            NUMBER(10,2),                     -- 当月进出口环比
    MONTH_EXP_MOM           NUMBER(10,2),                     -- 当月出口环比
    MONTH_IMP_MOM           NUMBER(10,2),                     -- 当月进口环比
    MONTH_TB_MOM            NUMBER(10,2),                     -- 当月贸易差额环比
    
    -- 数量相关指标
    MONTH_QUANTITY          NUMBER(19,4),                     -- 当月数量
    YTD_QUANTITY            NUMBER(19,4),                     -- 累计数量
    MONTH_QUANTITY_YOY      NUMBER(10,2),                     -- 当月数量同比(%)
    YTD_QUANTITY_YOY        NUMBER(10,2),                     -- 累计数量同比(%)
    QUANTITY_UNIT           VARCHAR2(50),                     -- 数量单位
    
    -- 重量相关指标(用于商品统计)
    MONTH_WEIGHT            NUMBER(19,4),                     -- 当月重量
    YTD_WEIGHT              NUMBER(19,4),                     -- 累计重量
    MONTH_WEIGHT_YOY        NUMBER(10,2),                     -- 当月重量同比(%)
    YTD_WEIGHT_YOY          NUMBER(10,2),                     -- 累计重量同比(%)
    WEIGHT_UNIT             VARCHAR2(50),                     -- 重量单位
    
    -- 扩展字段
    RANK_MARKER             VARCHAR2(10),                     -- 排名标识
    REGION_TYPE             VARCHAR2(50),                     -- 地区类型
    COMMODITY_CODE          VARCHAR2(50),                     -- 商品编码
    TRADE_MODE_CODE         VARCHAR2(50),                     -- 贸易方式编码
    ENTERPRISE_TYPE_CODE    VARCHAR2(50),                     -- 企业性质编码
    
    -- 元数据字段
    DATA_SOURCE             VARCHAR2(50),                     -- 数据来源
    SOURCE_TABLE            VARCHAR2(100),                    -- 源表名称
    BATCH_ID                VARCHAR2(50),                     -- 批次ID
    CREATE_TIME             DATE DEFAULT SYSDATE,             -- 创建时间
    UPDATE_TIME             DATE DEFAULT SYSDATE,             -- 更新时间
    CREATE_USER             VARCHAR2(50),                     -- 创建用户
    UPDATE_USER             VARCHAR2(50),                     -- 更新用户
    
    -- 主键约束
    CONSTRAINT PK_CUS_TRADE_UNIFIED PRIMARY KEY (ID)
);

-- 添加表注释
COMMENT ON TABLE CUS_TRADE_UNIFIED_STATISTICS IS '海关进出口统计数据统一大表';

-- 添加字段注释
COMMENT ON COLUMN CUS_TRADE_UNIFIED_STATISTICS.ID IS '主键ID';
COMMENT ON COLUMN CUS_TRADE_UNIFIED_STATISTICS.STAT_DATE IS '统计日期';
COMMENT ON COLUMN CUS_TRADE_UNIFIED_STATISTICS.STAT_TYPE IS '统计类型编码(01-总值,02-国别,03-构成,04-类章,05-贸易方式等)';
COMMENT ON COLUMN CUS_TRADE_UNIFIED_STATISTICS.STAT_TYPE_NAME IS '统计类型名称';
COMMENT ON COLUMN CUS_TRADE_UNIFIED_STATISTICS.CURRENCY_TYPE IS '货币类型(人民币/美元)';
COMMENT ON COLUMN CUS_TRADE_UNIFIED_STATISTICS.UNIT IS '计量单位';
COMMENT ON COLUMN CUS_TRADE_UNIFIED_STATISTICS.DIMENSION_1 IS '主要分类维度(国家、商品、贸易方式等)';
COMMENT ON COLUMN CUS_TRADE_UNIFIED_STATISTICS.DIMENSION_2 IS '次要分类维度(用于交叉分析)';
COMMENT ON COLUMN CUS_TRADE_UNIFIED_STATISTICS.DIMENSION_3 IS '第三分类维度(预留扩展)';
COMMENT ON COLUMN CUS_TRADE_UNIFIED_STATISTICS.TRADE_DIRECTION IS '贸易方向(进口/出口/进出口)';
COMMENT ON COLUMN CUS_TRADE_UNIFIED_STATISTICS.MONTH_IE_AMOUNT IS '当月进出口金额';
COMMENT ON COLUMN CUS_TRADE_UNIFIED_STATISTICS.MONTH_EXP_AMOUNT IS '当月出口金额';
COMMENT ON COLUMN CUS_TRADE_UNIFIED_STATISTICS.MONTH_IMP_AMOUNT IS '当月进口金额';
COMMENT ON COLUMN CUS_TRADE_UNIFIED_STATISTICS.YTD_IE_AMOUNT IS '累计进出口金额';
COMMENT ON COLUMN CUS_TRADE_UNIFIED_STATISTICS.YTD_EXP_AMOUNT IS '累计出口金额';
COMMENT ON COLUMN CUS_TRADE_UNIFIED_STATISTICS.YTD_IMP_AMOUNT IS '累计进口金额';
COMMENT ON COLUMN CUS_TRADE_UNIFIED_STATISTICS.DATA_SOURCE IS '数据来源';
COMMENT ON COLUMN CUS_TRADE_UNIFIED_STATISTICS.SOURCE_TABLE IS '源表名称';
COMMENT ON COLUMN CUS_TRADE_UNIFIED_STATISTICS.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN CUS_TRADE_UNIFIED_STATISTICS.UPDATE_TIME IS '更新时间';

-- 创建索引
-- 主要查询索引
CREATE INDEX IDX_CUS_UNIFIED_DATE_TYPE ON CUS_TRADE_UNIFIED_STATISTICS(STAT_DATE, STAT_TYPE);
CREATE INDEX IDX_CUS_UNIFIED_CURRENCY ON CUS_TRADE_UNIFIED_STATISTICS(CURRENCY_TYPE, STAT_DATE);
CREATE INDEX IDX_CUS_UNIFIED_DIM1 ON CUS_TRADE_UNIFIED_STATISTICS(DIMENSION_1, STAT_DATE);
CREATE INDEX IDX_CUS_UNIFIED_TRADE_DIR ON CUS_TRADE_UNIFIED_STATISTICS(TRADE_DIRECTION, STAT_DATE);

-- 复合索引
CREATE INDEX IDX_CUS_UNIFIED_MAIN ON CUS_TRADE_UNIFIED_STATISTICS(STAT_TYPE, STAT_DATE, CURRENCY_TYPE, DIMENSION_1);
CREATE INDEX IDX_CUS_UNIFIED_CROSS ON CUS_TRADE_UNIFIED_STATISTICS(DIMENSION_1, DIMENSION_2, STAT_DATE);

-- 数据源索引
CREATE INDEX IDX_CUS_UNIFIED_SOURCE ON CUS_TRADE_UNIFIED_STATISTICS(DATA_SOURCE, SOURCE_TABLE);

-- 创建唯一约束(防重复)
CREATE UNIQUE INDEX UK_CUS_UNIFIED_BUSINESS ON CUS_TRADE_UNIFIED_STATISTICS(
    STAT_DATE, STAT_TYPE, CURRENCY_TYPE, 
    NVL(DIMENSION_1,'NULL'), NVL(DIMENSION_2,'NULL'), NVL(DIMENSION_3,'NULL'),
    NVL(TRADE_DIRECTION,'NULL')
);

-- 创建分区表版本(可选，用于大数据量场景)
/*
-- 删除分区表（如果存在）
DROP TABLE CUS_TRADE_UNIFIED_STATISTICS_PART CASCADE CONSTRAINTS;

-- 创建按月分区的统一大表
CREATE TABLE CUS_TRADE_UNIFIED_STATISTICS_PART (
    -- 字段定义与上面相同
    ID                      NUMBER(20) GENERATED BY DEFAULT AS IDENTITY,
    STAT_DATE               DATE NOT NULL,
    STAT_TYPE               VARCHAR2(10) NOT NULL,
    STAT_TYPE_NAME          VARCHAR2(100),
    CURRENCY_TYPE           VARCHAR2(10) NOT NULL,
    UNIT                    VARCHAR2(50),
    DIMENSION_1             VARCHAR2(500),
    DIMENSION_2             VARCHAR2(500),
    DIMENSION_3             VARCHAR2(500),
    TRADE_DIRECTION         VARCHAR2(10),
    MONTH_IE_AMOUNT         NUMBER(19,4),
    MONTH_EXP_AMOUNT        NUMBER(19,4),
    MONTH_IMP_AMOUNT        NUMBER(19,4),
    YTD_IE_AMOUNT           NUMBER(19,4),
    YTD_EXP_AMOUNT          NUMBER(19,4),
    YTD_IMP_AMOUNT          NUMBER(19,4),
    MONTH_IE_YOY            NUMBER(10,2),
    MONTH_EXP_YOY           NUMBER(10,2),
    MONTH_IMP_YOY           NUMBER(10,2),
    YTD_IE_YOY              NUMBER(10,2),
    YTD_EXP_YOY             NUMBER(10,2),
    YTD_IMP_YOY             NUMBER(10,2),
    DATA_SOURCE             VARCHAR2(50),
    CREATE_TIME             DATE DEFAULT SYSDATE,
    UPDATE_TIME             DATE DEFAULT SYSDATE,

    CONSTRAINT PK_CUS_TRADE_UNIFIED_PART PRIMARY KEY (ID, STAT_DATE)
)
PARTITION BY RANGE (STAT_DATE) (
    PARTITION P_2023 VALUES LESS THAN (DATE '2024-01-01'),
    PARTITION P_2024 VALUES LESS THAN (DATE '2025-01-01'),
    PARTITION P_2025 VALUES LESS THAN (DATE '2026-01-01'),
    PARTITION P_FUTURE VALUES LESS THAN (MAXVALUE)
);
*/

-- 创建统计类型代码表
CREATE TABLE CUS_STAT_TYPE_CODE (
    STAT_TYPE               VARCHAR2(10) PRIMARY KEY,
    STAT_TYPE_NAME          VARCHAR2(100) NOT NULL,
    DESCRIPTION             VARCHAR2(500),
    DIMENSION_1_NAME        VARCHAR2(100),
    DIMENSION_2_NAME        VARCHAR2(100),
    DIMENSION_3_NAME        VARCHAR2(100),
    IS_ACTIVE               CHAR(1) DEFAULT 'Y',
    CREATE_TIME             DATE DEFAULT SYSDATE
);

-- 插入统计类型代码数据
INSERT INTO CUS_STAT_TYPE_CODE VALUES ('01', '总值统计', '进出口商品总值表', NULL, NULL, NULL, 'Y', SYSDATE);
INSERT INTO CUS_STAT_TYPE_CODE VALUES ('02', '国别地区', '国别地区总值表', '国家/地区', NULL, NULL, 'Y', SYSDATE);
INSERT INTO CUS_STAT_TYPE_CODE VALUES ('03', '商品构成', '进出口商品构成表', '商品构成', NULL, NULL, 'Y', SYSDATE);
INSERT INTO CUS_STAT_TYPE_CODE VALUES ('04', '商品类章', '进出口商品类章总值表', '商品类章', NULL, NULL, 'Y', SYSDATE);
INSERT INTO CUS_STAT_TYPE_CODE VALUES ('05', '贸易方式', '贸易方式总值表', '贸易方式', NULL, NULL, 'Y', SYSDATE);
INSERT INTO CUS_STAT_TYPE_CODE VALUES ('06', '贸易方式×企业性质', '贸易方式企业性质总值表', '贸易方式', '企业性质', NULL, 'Y', SYSDATE);
INSERT INTO CUS_STAT_TYPE_CODE VALUES ('07', '收发货人所在地', '收发货人所在地总值表', '地区', NULL, NULL, 'Y', SYSDATE);
INSERT INTO CUS_STAT_TYPE_CODE VALUES ('08', '境内目的地货源地', '境内目的地货源地总值表', '地区', NULL, NULL, 'Y', SYSDATE);
INSERT INTO CUS_STAT_TYPE_CODE VALUES ('09', '关别统计', '关别总值表', '海关', NULL, NULL, 'Y', SYSDATE);
INSERT INTO CUS_STAT_TYPE_CODE VALUES ('10', '特定地区', '特定地区进出口总值表', '地区', NULL, NULL, 'Y', SYSDATE);
INSERT INTO CUS_STAT_TYPE_CODE VALUES ('11', '外商投资企业', '外商投资企业进出口总值表', '企业类型', NULL, NULL, 'Y', SYSDATE);
INSERT INTO CUS_STAT_TYPE_CODE VALUES ('12', '主要商品量值', '主要商品量值表', '商品名称', NULL, NULL, 'Y', SYSDATE);
INSERT INTO CUS_STAT_TYPE_CODE VALUES ('13', '国家×商品', '国家商品类章金额表', '国家', '商品', NULL, 'Y', SYSDATE);
INSERT INTO CUS_STAT_TYPE_CODE VALUES ('14', '商品×贸易方式', '商品贸易方式量值表', '商品', '贸易方式', NULL, 'Y', SYSDATE);

COMMIT;

-- 显示创建结果
SELECT 'Table CUS_TRADE_UNIFIED_STATISTICS created successfully!' as STATUS FROM DUAL;
SELECT 'Stat type codes inserted: ' || COUNT(*) as CODES_COUNT FROM CUS_STAT_TYPE_CODE;
