import cx_Oracle as oracle
import pandas as pd
import logging
import os
import configparser

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    filename='db_sync.log'
)
logger = logging.getLogger('db_sync')

# 获取配置文件路径
script_dir = os.path.dirname(os.path.abspath(__file__))
config_path = os.path.join(script_dir, 'db_config.ini')

def get_db_config(section):
    """从配置文件读取数据库配置"""
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
    config = configparser.ConfigParser()
    config.read(config_path)
    
    if section not in config:
        raise ValueError(f"配置文件中未找到 {section} 节")
    
    return {
        'username': config[section]['username'],
        'password': config[section]['password'],
        'host': config[section]['host'],
        'port': config[section]['port'],
        'service_name': config[section]['service_name']
    }

def get_connection_string(db_config):
    """根据配置生成数据库连接字符串"""
    return f"{db_config['username']}/{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['service_name']}"

def select_sql_CS_SINGLEWINDOW_PRODUCT_CODE():
    try:
        test_db_config = get_db_config('TEST_DB')
        conn = oracle.connect(get_connection_string(test_db_config))
        cursor = conn.cursor()
        select_sql = 'select * from CS_SINGLEWINDOW_PRODUCT_CODE t where t.createtime >sysdate -0.5'
        cursor.execute(select_sql)
        result = cursor.fetchall()
        conn.commit()
        pd_data = pd.DataFrame(result, columns=['CODETS', 'GNAME', 'NOTES', 'CREATETIME'])
        insert_sql(pd_data)
        cursor.close()
        conn.close()
        print('查询完成')
    except Exception as e:
        try:
            print(e)
            logger.info('select_sql_CS_SINGLEWINDOW_PRODUCT_CODE报错')
            logger.info(str(e))
        finally:
            e = None
            del e


def insert_sql(pd_data):
    print('insert_sql')
    try:
        prod_db_config = get_db_config('PROD_DB')
        conn = oracle.connect(get_connection_string(prod_db_config))
        cursor = conn.cursor()
        index = [':%s' % i for i in range(1, len(pd_data.columns.values) + 1)]
        v_sql = f"insert into T_BC_SW_TS_CODE values({','.join(index)})"
        data1 = pd_data.where(pd_data.notnull(), '')
        data2 = [tuple(r) for r in data1.values.tolist()]
        
        cursor.executemany(v_sql, data2)
        conn.commit()
        print('导入成功！')
    except oracle.Error as err:
        try:
            print(err)
            logger.info(str(err))
        finally:
            err = None
            del err
    finally:
        if 'cursor' in locals() and cursor:
            cursor.close()
        if 'conn' in locals() and conn:
            conn.close()

if __name__ == "__main__":
    select_sql_CS_SINGLEWINDOW_PRODUCT_CODE()
