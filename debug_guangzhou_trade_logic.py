import pandas as pd
import sys
sys.path.append('.')
from analyze_customs_data import analyze_trade_mode_data

# 读取广州数据
file_path = r'C:\Users\<USER>\Desktop\海关数据\小超海关统计\全国进出口\20250530\2025年1-4月 - 广州\2025年1-4月广州关区所辖7地市进出口综合统计资料.xls'
df = pd.read_excel(file_path, sheet_name='贸易方式进出口总值表')

print('DataFrame shape:', df.shape)

print('\n广州数据区域（第3-22行）的详细内容:')
for i in range(3, 23):
    if i < len(df):
        row_data = df.iloc[i]
        if pd.notna(row_data.iloc[0]):
            row_text = str(row_data.iloc[0]).strip()
            print(f'行{i}: "{row_text}"')
        else:
            # 检查第1列是否有贸易方式信息
            if pd.notna(row_data.iloc[1]):
                col1_text = str(row_data.iloc[1]).strip()
                print(f'行{i}: 第0列空，第1列: "{col1_text}"')
                if '保税' in col1_text:
                    print(f'  *** 在第1列发现保税相关！ ***')
                    print(f'  累计进出口: {row_data.iloc[2]}')
                    print(f'  累计出口: {row_data.iloc[3]}')
                    print(f'  累计进口: {row_data.iloc[4]}')

print('\n\n测试当前的analyze_trade_mode_data函数:')
try:
    result = analyze_trade_mode_data(file_path, sheet_name='贸易方式进出口总值表')
    if result:
        print('累计数据:')
        print(f'  一般贸易: 进出口={result["general_trade"]["value_billion"]}亿, 同比={result["general_trade"]["yoy"]}%, 占比={result["general_trade"]["share"]}%')
        print(f'  加工贸易: 进出口={result["processing_trade"]["value_billion"]}亿, 同比={result["processing_trade"]["yoy"]}%, 占比={result["processing_trade"]["share"]}%')
        print(f'  保税贸易: 进出口={result["bonded_trade"]["value_billion"]}亿, 同比={result["bonded_trade"]["yoy"]}%, 占比={result["bonded_trade"]["share"]}%')
        print(f'  总计: 进出口={result["total"]["value_billion"]}亿, 同比={result["total"]["yoy"]}%, 占比={result["total"]["share"]}%')
    else:
        print('函数返回None')
except Exception as e:
    print(f'函数执行出错: {e}')