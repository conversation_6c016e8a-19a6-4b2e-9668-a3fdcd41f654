# 文件名: (17_18)统一-商品x贸易方式入库.py
# 这是一个完整、可运行的、合并了(17)和(18)号表处理逻辑的最终版本。

import pandas as pd
import cx_Oracle
import os
import re
import sys
from tqdm import tqdm
from pathlib import Path
import numpy as np

def get_db_connection():
    """建立并返回数据库连接"""
    try:
        connection = cx_Oracle.connect("manifest_dcb/manifest_dcb@*************/TEST")
        return connection
    except Exception as e:
        print(f"数据库连接失败: {e}")
        sys.exit(1)

def get_unit_from_dataframe(df):
    """从DataFrame中动态查找单位信息"""
    for _, row in df.head(5).iterrows():
        for cell in row:
            if isinstance(cell, str) and '单位：' in cell:
                return cell.split('：')[1].strip()
    return "不明"

def parse_product_trademode_data(file_path):
    """
    【融合】一个通用的解析器，能够处理(17)和(18)号表相似的Excel结构。
    """
    filename = os.path.basename(file_path)
    date_match = re.search(r'(\d{4})年1至(\d{1,2})月', filename)
    if not date_match:
        date_match = re.search(r'(\d{4})年(\d{1,2})月', filename)
    if not date_match:
        print(f"警告: 无法从文件名 '{filename}' 中提取日期。")
        return None
        
    year, month = date_match.groups()
    current_month_str = f"{year}-{int(month):02d}-01"
    
    try:
        df_raw = pd.read_excel(file_path, header=None, engine=None)
    except Exception as e:
        print(f"错误: 使用pandas读取文件失败: {file_path}, 错误: {e}")
        return None

    unit = get_unit_from_dataframe(df_raw)
    
    header_row_idx = 3
    data_start_row_idx = 5
    
    trade_modes_header = df_raw.iloc[header_row_idx].values
    
    trade_mode_map = {}
    current_trade_mode = ""
    for i, v in enumerate(trade_modes_header):
        if pd.notna(v) and '贸易' in str(v):
            cleaned_v = str(v).split('\n')[0].strip()
            if cleaned_v:
                current_trade_mode = cleaned_v
        if i >= 3 and (i - 3) % 2 == 0:
            trade_mode_map[i] = current_trade_mode

    df_data = df_raw.iloc[data_start_row_idx:]
    processed_data = []

    for i in range(0, len(df_data), 2):
        if i + 1 >= len(df_data): continue
            
        value_row = df_data.iloc[i]
        yoy_row = df_data.iloc[i + 1]

        product_name = value_row.iloc[1]
        if pd.isna(product_name) or not str(product_name).strip() or '计' in str(product_name) or '总' in str(product_name): continue
            
        measure_unit = str(value_row.iloc[2]).strip() if pd.notna(value_row.iloc[2]) else None

        for col_idx, trade_mode in trade_mode_map.items():
            if not trade_mode: continue

            quantity = pd.to_numeric(value_row.iloc[col_idx], errors='coerce')
            amount = pd.to_numeric(value_row.iloc[col_idx + 1], errors='coerce')
            quantity_yoy = pd.to_numeric(yoy_row.iloc[col_idx], errors='coerce')
            amount_yoy = pd.to_numeric(yoy_row.iloc[col_idx + 1], errors='coerce')

            if pd.notna(quantity) or pd.notna(amount):
                processed_data.append({
                    "product_name": str(product_name).strip(),
                    "measure_unit": measure_unit,
                    "trade_mode": trade_mode,
                    "quantity": quantity,
                    "amount": amount,
                    "quantity_yoy": quantity_yoy,
                    "amount_yoy": amount_yoy
                })

    if not processed_data:
        return None
        
    df = pd.DataFrame(processed_data)
    df['current_month'] = pd.to_datetime(current_month_str)
    df['unit'] = unit
    return df

def upsert_to_17_18(connection, df, trade_direction):
    """
    【新】使用MERGE语句将数据插入或更新到统一表 TEMP_CUS_MON_17_18
    """
    if df is None or df.empty:
        return 0

    cursor = connection.cursor()
    
    # 关键：为DataFrame批量增加贸易方向列
    df['trade_direction'] = trade_direction
    df = df.replace({np.nan: None})
    data_to_merge = df.to_dict('records')

    # 【新】MERGE语句，目标是新表，ON条件和INSERT部分都增加了trade_direction
    merge_sql = """
    MERGE INTO TEMP_CUS_MON_17_18 dest
    USING (
        SELECT
            :current_month AS current_month,
            :currency_type AS currency_type,
            :product_name AS product_name,
            :trade_mode AS trade_mode,
            :trade_direction AS trade_direction
        FROM dual
    ) src ON (
        dest.current_month = src.current_month AND
        dest.currency_type = src.currency_type AND
        dest.product_name = src.product_name AND
        dest.trade_mode = src.trade_mode AND
        dest.trade_direction = src.trade_direction
    )
    WHEN MATCHED THEN
        UPDATE SET
            dest.unit = :unit,
            dest.measure_unit = :measure_unit,
            dest.quantity = :quantity,
            dest.amount = :amount,
            dest.quantity_yoy = :quantity_yoy,
            dest.amount_yoy = :amount_yoy
    WHEN NOT MATCHED THEN
        INSERT (
            current_month, currency_type, product_name, trade_mode, trade_direction,
            unit, measure_unit, quantity, amount, quantity_yoy, amount_yoy
        ) VALUES (
            :current_month, :currency_type, :product_name, :trade_mode, :trade_direction,
            :unit, :measure_unit, :quantity, :amount, :quantity_yoy, :amount_yoy
        )
    """
    
    try:
        cursor.executemany(merge_sql, data_to_merge)
        connection.commit()
        print(f"        -> 成功合并 {cursor.rowcount} 条 [{trade_direction}] 记录.")
        return cursor.rowcount
    except Exception as e:
        print(f"        [!] 执行批量合并时发生严重错误: {e}")
        connection.rollback()
        return 0
    finally:
        cursor.close()

def process_directory(directory_path, trade_direction, connection):
    """【通用】处理指定目录下的所有Excel文件"""
    base_dir = Path(directory_path)
    if not base_dir.exists():
        print(f"错误: 数据目录不存在 -> {base_dir}，跳过处理。")
        return

    files_to_process = sorted(list(base_dir.rglob('*.xlsx')))
    
    print(f"\n--- 开始处理目录 ({trade_direction}): {base_dir.name} ---")
    
    for file_path in tqdm(files_to_process, desc=f"处理 {base_dir.name}"):
        if file_path.name.startswith('~'):
            continue
            
        df = parse_product_trademode_data(str(file_path))
        
        if df is not None and not df.empty:
            # 从文件名判断币种
            df['currency_type'] = "人民币" if "人民币" in str(file_path) else "美元"
            upsert_to_17_18(connection, df, trade_direction)
        else:
            print(f"    -> 文件 {file_path.name} 解析为空或失败，跳过。")

def main():
    """【新】主执行函数，自动处理人民币和美元的进出口四个目录"""
    base_dir = os.getcwd()
    # (17) 出口目录
    exp_rmb_dir_17 = os.path.join(base_dir, '进出口商品统计表_人民币值', '(17)部分出口商品主要贸易方式量值表_人民币值')
    exp_usd_dir_17 = os.path.join(base_dir, '进出口商品统计表_美元值', '(17)部分出口商品主要贸易方式量值表_美元值')
    # (18) 进口目录
    imp_rmb_dir_18 = os.path.join(base_dir, '进出口商品统计表_人民币值', '(18)部分进口商品主要贸易方式量值表_人民币值')
    imp_usd_dir_18 = os.path.join(base_dir, '进出口商品统计表_美元值', '(18)部分进口商品主要贸易方式量值表_美元值')

    connection = None
    try:
        connection = get_db_connection()
        print("数据库连接成功。")
        
        # 按顺序处理所有目录，并传入正确的贸易方向
        process_directory(exp_rmb_dir_17, '出口', connection)
        process_directory(exp_usd_dir_17, '出口', connection)
        process_directory(imp_rmb_dir_18, '进口', connection)
        process_directory(imp_usd_dir_18, '进口', connection)

        print("\n--- (17)和(18)所有目录处理完毕 ---")

    except Exception as e:
        print(f"\n处理过程中发生未预料的错误: {e}")
    finally:
        if connection:
            connection.close()
            print("数据库连接已关闭。")

if __name__ == "__main__":
    main() 