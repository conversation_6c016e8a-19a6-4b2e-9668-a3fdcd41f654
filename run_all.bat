@ECHO OFF
:: 设置代码页为UTF-8，以正确处理中文路径和文件名
CHCP 65001 > NUL

:: 设置环境变量，强制Python使用UTF-8编码，这是解决乱码的最可靠方法
SET PYTHONUTF8=1

:: --- 自动化处理海关数据脚本 ---
:: 作者: Gemini
:: 功能: 1. 运行下载程序. 2. 依次执行1-18号表的入库脚本.
:: 环境: 需要在Conda的'oracle_env'虚拟环境中运行.
:: -----------------------------------------

ECHO.
ECHO --- 步骤 1: 开始运行下载脚本 ---
ECHO 正在执行: 下载全量进出口商品统计表.py
:: "D:\conda\Scripts\conda.exe" run -n oracle_env python "下载全量进出口商品统计表.py"
ECHO --- 下载脚本执行完毕 ---
ECHO.
ECHO.

ECHO --- 步骤 2: 开始依次执行1-18号表的入库脚本 ---

ECHO.
ECHO --- 正在处理 (1) 进出口商品总值表_B月度表 ---
"D:\conda\Scripts\conda.exe" run -n oracle_env python "(1)进出口商品总值表_B月度表_入库.py"

ECHO.
ECHO --- 正在处理 (1A) 进出口商品总值表_年度表 ---
"D:\conda\Scripts\conda.exe" run -n oracle_env python "(1A)进出口商品总值表_年度表_入库.py"

ECHO.
ECHO --- 正在处理 (2) 进出口商品国别(地区)总值表 ---
"D:\conda\Scripts\conda.exe" run -n oracle_env python "(2)国别地区总值表_入库.py"

ECHO.
ECHO --- 正在处理 (3) 进出口商品构成表 ---
"D:\conda\Scripts\conda.exe" run -n oracle_env python "(3)进出口商品构成表_入库.py"

ECHO.
ECHO --- 正在处理 (4) 进出口商品类章总值表 ---
"D:\conda\Scripts\conda.exe" run -n oracle_env python "(4)进出口商品类章总值表_入库.py"

ECHO.
ECHO --- 正在处理 (5) 进出口商品贸易方式总值表 ---
"D:\conda\Scripts\conda.exe" run -n oracle_env python "(5)贸易方式总值表_入库.py"

ECHO.
ECHO --- 正在处理 (6) 出口商品贸易方式企业性质总值表 ---
"D:\conda\Scripts\conda.exe" run -n oracle_env python "(6)出口贸易方式企业性质总值表_入库.py"

ECHO.
ECHO --- 正在处理 (7) 进口商品贸易方式企业性质总值表 ---
"D:\conda\Scripts\conda.exe" run -n oracle_env python "(7)进口商品贸易方式企业性质总值表_入库.py"

ECHO.
ECHO --- 正在处理 (8) 进出口商品收发货人所在地总值表 ---
"D:\conda\Scripts\conda.exe" run -n oracle_env python "(8)收发货人所在地总值表_入库.py"

ECHO.
ECHO --- 正在处理 (9) 进出口商品境内目的地货源地总值表 ---
"D:\conda\Scripts\conda.exe" run -n oracle_env python "(9)进出口商品境内目的地货源地总值表_入库.py"

ECHO.
ECHO --- 正在处理 (10) 进出口商品关别总值表 ---
"D:\conda\Scripts\conda.exe" run -n oracle_env python "(10)进出口商品关别总值表_入库.py"

ECHO.
ECHO --- 正在处理 (11) 特定地区进出口总值表 ---
"D:\conda\Scripts\conda.exe" run -n oracle_env python "(11)特定地区进出口总值表_入库.py"

ECHO.
ECHO --- 正在处理 (12) 外商投资企业进出口总值表 ---
"D:\conda\Scripts\conda.exe" run -n oracle_env python "(12)外商投资企业进出口总值表_入库.py"

ECHO.
ECHO --- 正在处理 (13) 出口主要商品量值表 ---
"D:\conda\Scripts\conda.exe" run -n oracle_env python "(13)出口主要商品量值表_入库.py"

ECHO.
ECHO --- 正在处理 (14) 进口主要商品量值表 ---
"D:\conda\Scripts\conda.exe" run -n oracle_env python "(14)进口主要商品量值表_入库.py"

ECHO.
ECHO --- 正在处理 (15) 对部分国家(地区)出口商品类章金额表 ---
"D:\conda\Scripts\conda.exe" run -n oracle_env python "(15)对部分国家(地区)出口商品类章金额表_入库.py"

ECHO.
ECHO --- 正在处理 (16) 自部分国家(地区)进口商品类章金额表 ---
"D:\conda\Scripts\conda.exe" run -n oracle_env python "(16)自部分国家(地区)进口商品类章金额表_入库.py"

ECHO.
ECHO --- 正在处理 (17) 部分出口商品主要贸易方式量值表 ---
"D:\conda\Scripts\conda.exe" run -n oracle_env python "(17)部分出口商品主要贸易方式量值表_入库.py"

ECHO.
ECHO --- 正在处理 (18) 部分进口商品主要贸易方式量值表 ---
"D:\conda\Scripts\conda.exe" run -n oracle_env python "(18)部分进口商品主要贸易方式量值表_入库.py"

ECHO.
ECHO.
ECHO --- 所有脚本执行完毕 ---
ECHO. 