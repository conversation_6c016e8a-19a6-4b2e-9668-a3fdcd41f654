Title: 🛰️ 截图和录像
URL: https://www.drissionpage.cn/browser_control/screen/

🛰️ 截图和录像
云服务器38元/年起，大模型限免超7000万 tokens广告


✅️️ 页面截图​
使用页面对象的get_screenshot()方法对页面进行截图，可对整个网页、可见网页、指定范围截图。
对可视范围外截图需要 90 以上版本浏览器支持。
下面三个参数三选一，优先级：as_bytes>as_base64>path。
参数名称	类型	默认值	说明
path	str
Path	None	保存图片的路径，为None时保存在当前文件夹
name	str	None	完整文件名，后缀可选'jpg'、'jpeg'、'png'、'webp'，为None时以用 jpg 格式
as_bytes	str
True	None	是否以字节形式返回图片，可选'jpg'、'jpeg'、'png'、'webp'、None、True
不为None时path参数无效
为True时选用 jpg 格式
as_base64	str
True	None	是否以 base64 形式返回图片，可选'jpg'、'jpeg'、'png'、'webp'、None、True
不为None时path参数无效
为True时选用 jpg 格式
full_page	bool	False	是否整页截图，为True截取整个网页，为False截取可视窗口
left_top	Tuple[int, int]	None	截取范围左上角坐标
right_bottom	Tuple[int, int]	None	截取范围右下角坐标
返回类型	说明
bytes	as_bytes生效时返回图片字节
str	as_bytes和as_base64为None时返回图片完整路径
str	as_base64生效时返回 base64 格式的字符串
说明
如path为包含文件名的完整路径，name参数无效。
示例：
# 对整页截图并保存
tab.get_screenshot(path='tmp', name='pic.jpg', full_page=True)

️️ ✅️️ 元素截图​
使用元素对象的get_screenshot()方法对元素进行截图。
若元素范围超出视口，需 90 以上版本内核支持。
下面三个参数三选一，优先级：as_bytes>as_base64>path。
参数名称	类型	默认值	说明
path	str
Path	None	保存图片的路径，为None时保存在当前文件夹
name	str	None	完整文件名，后缀可选'jpg'、'jpeg'、'png'、'webp'，为None时以用 jpg 格式
as_bytes	str
True	None	是否以字节形式返回图片，可选'jpg'、'jpeg'、'png'、'webp'、None、True
不为None时path和as_base64参数无效
为True时选用 jpg 格式
as_base64	str
True	None	是否以 base64 形式返回图片，可选'jpg'、'jpeg'、'png'、'webp'、None、True
不为None时path参数无效
为True时选用 jpg 格式
scroll_to_center	bool	True	截图前是否滚动到视口中央
返回类型	说明
bytes	as_bytes生效时返回图片字节
str	as_bytes和as_base64为None时返回图片完整路径
str	as_base64生效时返回 base64 格式的字符串
说明
如path为包含文件名的完整路径，name参数无效。
示例：
img = tab('tag:img')
img.get_screenshot()
bytes_str = img.get_screenshot(as_bytes='png')  # 返回截图二进制文本

✅️️ 页面录像​
使用页面对象的screencast功能，可以录取屏幕图片或视频。
📌 设置录制模式​
录制模式一共有 5 种，通过screencast.set_mode.xxx_mode()设置。
模式	说明
video_mode()	持续录制页面，停止时生成没有声音的视频
frugal_video_mode()	页面有变化时才录制，停止时生成没有声音的视频
js_video_mode()	可生成有声音的视频，但需要手动启动
imgs_mode()	持续对页面进行截图
frugal_imgs_mode()	页面有变化时才保存页面图像
📌 设置存放路径​
使用screencast.set_save_path()设置录制结果保存路径。
参数名称	类型	默认值	说明
save_path	str
Path	None	保存图片或视频的路径
返回：None
📌 screencast.start()​
此方法用于开始录制浏览器窗口。
参数名称	类型	默认值	说明
save_path	str
Path	None	保存图片或视频的路径
返回：None
注意
保存路径必需设置，无论是用screencast.set()还是screencast.start()都可以。
📌 screencast.stop()​
此方法用于停止录取屏幕。
参数名称	类型	默认值	说明
video_name	str	None	视频文件名，为None时以当前时间名命
suffix	str	'mp4'	视频文件后缀
coding	str	'mp4v'	视频编码格式，仅video_mode模式有效，根据cv2.VideoWriter_fourcc()定义
返回类型	说明
str	保存为视频时返回视频文件路径，否则返回保存图片的文件夹路径
📌 注意事项​
使用video_mode和frugal_video_mode时，保存路径和保存文件名必需是英文。
使用video_mode和frugal_video_mode时，需先安装 opencv 库。pip install opencv-python
使用js_video_mode时，需用鼠标手动选择要录制的目标，才能开始录制
使用js_video_mode时，如要对一个窗口进行录制，需在另一个窗口开始录制，否则如窗口出现跳转，会使录制失效
📌 示例​
from DrissionPage import Chromium

tab = Chromium().latest_tab
tab.screencast.set_save_path('video')  # 设置视频存放路径
tab.screencast.set_mode.video_mode()  # 设置录制
tab.screencast.start()  # 开始录制
tab.wait(3)
tab.screencast.stop()  # 停止录制