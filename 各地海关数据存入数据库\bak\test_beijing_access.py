from DrissionPage import ChromiumPage
import time

def test_beijing_customs_access():
    """测试北京海关网站访问"""

    # 北京海关统计数据页面URL
    beijing_url = "http://beijing.customs.gov.cn/beijing_customs/434756/434804/2941702/434773/434774/index.html"

    # 创建页面对象
    page = ChromiumPage(timeout=20)

    try:
        print("正在访问北京海关统计数据页面...")
        page.get(beijing_url)
        time.sleep(3)  # 等待页面加载

        print(f"页面标题: {page.title}")
        print(f"当前URL: {page.url}")

        # 查找年份相关元素
        print("\n查找年份相关元素:")
        year_elements = page.eles("2024年") + page.eles("2025年")
        if year_elements:
            print(f"找到 {len(year_elements)} 个年份相关元素")
            for i, elem in enumerate(year_elements[:5]):
                print(f"  元素 {i+1}: {elem.text[:50]}...")
                if elem.tag == 'a':
                    print(f"  链接: {elem.attr('href')}")
        else:
            print("未找到年份相关元素")

        # 查找统计表格相关链接
        print("\n查找统计表格相关元素:")
        elements = page.eles("进出口")
        if elements:
            print(f"包含'进出口'的元素: {len(elements)}个")
            for i, elem in enumerate(elements[:5]):
                if elem.tag == 'a':
                    print(f"  链接 {i+1}: {elem.text[:50]}... -> {elem.attr('href')}")

        # 查找北京地区相关元素
        print("\n查找北京地区相关元素:")
        beijing_elements = page.eles("北京地区")
        if beijing_elements:
            print(f"包含'北京地区'的元素: {len(beijing_elements)}个")
            for i, elem in enumerate(beijing_elements[:5]):
                if elem.tag == 'a':
                    print(f"  链接 {i+1}: {elem.text[:50]}... -> {elem.attr('href')}")

        print("\n=== 测试完成 ===")

    except Exception as e:
        print(f"测试过程中出错: {e}")

    finally:
        # 关闭浏览器
        page.quit()

if __name__ == "__main__":
    test_beijing_customs_access()