import pandas as pd
import os
import re
import cx_Oracle
import numpy as np
from pathlib import Path
from tqdm import tqdm
import sys

def convert_to_float_or_none(value):
    """清理数据，将'-'或空值转为None，其余转为浮点数"""
    if pd.isna(value) or str(value).strip() in ['-', '…']:
        return None
    try:
        return float(str(value).replace(',', ''))
    except (ValueError, TypeError):
        return None

def get_db_connection():
    """建立并返回数据库连接"""
    try:
        connection = cx_Oracle.connect("manifest_dcb/manifest_dcb@*************/TEST")
        return connection
    except Exception as e:
        print(f"数据库连接失败: {e}")
        sys.exit(1)

def parse_table_11(file_path):
    """
    专门为(11)特定地区进出口总值表编写的解析器。
    能够处理其复杂的多行表头和10列数据结构。
    返回一个元组 (DataFrame, unit)
    """
    try:
        df_raw = pd.read_excel(file_path, header=None)

        unit = None
        header_search_start_row = 0
        for i in range(min(10, len(df_raw))):
            row_text = ' '.join(str(s) for s in df_raw.iloc[i].tolist() if pd.notna(s))
            unit_match = re.search(r'单位[：:]\s*([^\s]+)', row_text)
            if unit_match:
                unit = unit_match.group(1).strip()
                header_search_start_row = i + 1
                break
        
        header_start_row = -1
        for i, row in df_raw.iloc[header_search_start_row:].iterrows():
            row_str = ' '.join(str(s) for s in row if pd.notna(s))
            if '进出口' in row_str and '出口' in row_str and '进口' in row_str:
                header_start_row = i
                break
        
        if header_start_row == -1:
            print(f"    [!] 错误: 在文件 '{os.path.basename(file_path)}' 中未找到表头行。")
            return None, None

        data_start_row = header_start_row + 2
        df_data = df_raw.iloc[data_start_row:, 1:12].copy() # 数据在 B-L 列
        
        processed_data = []
        for i in range(len(df_data)):
            row = df_data.iloc[i].values
            
            region_name = row[0]
            if pd.isna(region_name) or str(region_name).strip().startswith('注'):
                continue
            
            processed_data.append({
                'REGION': str(region_name).strip(),
                'MONTH_IE_VALUE': convert_to_float_or_none(row[1]),
                'YTD_IE_VALUE': convert_to_float_or_none(row[2]),
                'MONTH_EXPORT_VALUE': convert_to_float_or_none(row[3]),
                'YTD_EXPORT_VALUE': convert_to_float_or_none(row[4]),
                'MONTH_IMPORT_VALUE': convert_to_float_or_none(row[5]),
                'YTD_IMPORT_VALUE': convert_to_float_or_none(row[6]),
                'YOY_IE': convert_to_float_or_none(row[7]),
                'YOY_EXPORT': convert_to_float_or_none(row[8]),
                'YOY_IMPORT': convert_to_float_or_none(row[9])
            })

        return pd.DataFrame(processed_data), unit

    except Exception as e:
        print(f"    [!] 解析文件 '{os.path.basename(file_path)}' 时出错: {e}")
        return None, None

def get_current_month_from_filename(filename):
    """从文件名中提取年月"""
    match = re.search(r'(\d{4})年(?:1至)?(\d{1,2})月', filename)
    if match:
        year, month = match.groups()
        return f"{year}{month.zfill(2)}01"
    return None

def process_directory(directory_path, connection):
    """使用Pathlib和tqdm处理指定目录下的所有Excel文件"""
    base_dir = Path(directory_path)
    if not base_dir.is_dir():
        print(f"错误: 数据目录不存在 -> {base_dir}，跳过处理。")
        return 0, 0

    files_to_process = sorted(list(base_dir.rglob('*.xlsx'))) + sorted(list(base_dir.rglob('*.xls')))
    if not files_to_process:
        print(f"在目录 {base_dir.name} 中未找到任何 .xlsx 或 .xls 文件。")
        return 0, 0
    
    print(f"\n--- 开始处理目录: {base_dir.name} ---")
    print(f"发现 {len(files_to_process)} 个文件待处理。")
    
    total_new_in_dir = 0
    total_skipped_in_dir = 0
    
    cursor = connection.cursor()
    try:
        for file_path in tqdm(files_to_process, desc=f"处理 {base_dir.name}"):
            if file_path.name.startswith('~'):
                continue
            
            current_month_str = get_current_month_from_filename(file_path.name)
            if not current_month_str:
                print(f"    [!] 无法从文件名 {file_path.name} 中提取年月，跳过。")
                continue
            
            currency_type = "人民币" if "人民币" in str(file_path) else "美元"
            
            df, unit_from_file = parse_table_11(str(file_path))
            unit = unit_from_file if unit_from_file else ("万元" if currency_type == "人民币" else "千美元")

            if df is None or df.empty:
                continue

            select_keys_query = "SELECT REGION FROM temp_cus_mon_11 WHERE CURRENT_MONTH = TO_DATE(:1, 'YYYYMMDD') AND CURRENCY_TYPE = :2"
            cursor.execute(select_keys_query, (current_month_str, currency_type))
            existing_regions = {row[0] for row in cursor.fetchall()}

            df_new = df[~df['REGION'].isin(existing_regions)]
            total_skipped_in_dir += (len(df) - len(df_new))

            if df_new.empty:
                continue

            df_new = df_new.copy()
            df_new['CURRENT_MONTH'] = current_month_str
            df_new['CURRENCY_TYPE'] = currency_type
            df_new['UNIT'] = unit
            df_new.replace({np.nan: None}, inplace=True)
            
            data_to_insert = df_new.to_dict('records')
            
            insert_query = """
            INSERT INTO temp_cus_mon_11 (
                REGION, MONTH_IE_VALUE, YTD_IE_VALUE, MONTH_EXPORT_VALUE, YTD_EXPORT_VALUE,
                MONTH_IMPORT_VALUE, YTD_IMPORT_VALUE, YOY_IE, YOY_EXPORT, YOY_IMPORT,
                CURRENT_MONTH, CURRENCY_TYPE, UNIT
            ) VALUES (
                :REGION, :MONTH_IE_VALUE, :YTD_IE_VALUE, :MONTH_EXPORT_VALUE, :YTD_EXPORT_VALUE,
                :MONTH_IMPORT_VALUE, :YTD_IMPORT_VALUE, :YOY_IE, :YOY_EXPORT, :YOY_IMPORT,
                TO_DATE(:CURRENT_MONTH, 'YYYYMMDD'), :CURRENCY_TYPE, :UNIT
            )
            """
            
            cursor.executemany(insert_query, data_to_insert)
            total_new_in_dir += cursor.rowcount

    except cx_Oracle.Error as e:
        print(f"    [!] 数据库错误: {e}")
        connection.rollback() # Rollback on error
    finally:
        cursor.close()
        
    return total_new_in_dir, total_skipped_in_dir

def main():
    """主执行函数，自动处理人民币和美元两个目录"""
    base_dir = os.getcwd() # 使用当前工作目录
    rmb_dir = os.path.join(base_dir, "进出口商品统计表_人民币值", "(11)特定地区进出口总值表_人民币值")
    usd_dir = os.path.join(base_dir, "进出口商品统计表_美元值", "(11)特定地区进出口总值表_美元值")

    connection = None
    try:
        connection = get_db_connection()
        print("数据库连接成功。")
        
        rmb_new, rmb_skipped = process_directory(rmb_dir, connection)
        print(f"目录 {Path(rmb_dir).name} 处理完成: 新增 {rmb_new} 条, 跳过 {rmb_skipped} 条。")
        
        usd_new, usd_skipped = process_directory(usd_dir, connection)
        print(f"目录 {Path(usd_dir).name} 处理完成: 新增 {usd_new} 条, 跳过 {usd_skipped} 条。")

        connection.commit()
        print("\n--- 所有数据提交成功 ---")
        print(f"总计: 新增 {rmb_new + usd_new} 条, 跳过 {rmb_skipped + usd_skipped} 条。")

    except Exception as e:
        print(f"\n处理过程中发生未预料的错误: {e}")
        if connection:
            connection.rollback()
    finally:
        if connection:
            connection.close()
            print("数据库连接已关闭。")

if __name__ == "__main__":
    main() 