import cx_Oracle as oracle
import logging
import os
import configparser
from datetime import datetime
import json

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    filename='number_data_sync.log'
)
logger = logging.getLogger('data_sync')

# 获取配置文件路径
script_dir = os.path.dirname(os.path.abspath(__file__))
config_path = os.path.join(script_dir, 'db_config.ini')

def get_db_config(section):
    """从配置文件读取数据库配置"""
    if not os.path.exists(config_path):
        # 创建默认配置文件
        config = configparser.ConfigParser()
        config['TEST_DB'] = {
            'username': 'manifest',
            'password': 'manifest',
            'host': 'ip',
            'port': '1522',
            'service_name': 'TEST'
        }
        config['PROD_DB'] = {
            'username': 'manifest2',
            'password': 'manifest',
            'host': 'ip',
            'port': '1521',
            'service_name': 'test2'
        }
        with open(config_path, 'w') as f:
            config.write(f)
        print(f"已创建配置文件: {config_path}")
        
    config = configparser.ConfigParser()
    config.read(config_path)
    
    if section not in config:
        raise ValueError(f"配置文件中未找到 {section} 节")
    
    return {
        'username': config[section]['username'],
        'password': config[section]['password'],
        'host': config[section]['host'],
        'port': config[section]['port'],
        'service_name': config[section]['service_name']
    }

def get_connection_string(db_config):
    """根据配置生成数据库连接字符串"""
    return f"{db_config['username']}/{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['service_name']}"

def check_table_exists(table_name, conn):
    """检查表是否存在"""
    cursor = conn.cursor()
    try:
        cursor.execute(f"""
        SELECT COUNT(*) FROM user_tables WHERE table_name = '{table_name.upper()}'
        """)
        result = cursor.fetchone()[0]
        return result > 0
    finally:
        cursor.close()

def get_table_definition(table_name, conn):
    """获取表的完整定义，包括列信息、数据类型和约束"""
    cursor = conn.cursor()
    try:
        # 获取列信息
        cursor.execute(f"""
        SELECT column_name, data_type, 
               data_length, data_precision, data_scale, 
               nullable, data_default, column_id
        FROM all_tab_columns 
        WHERE table_name = '{table_name.upper()}'
        ORDER BY column_id
        """)
        
        columns = []
        for row in cursor.fetchall():
            column = {
                'name': row[0],
                'type': row[1],
                'length': row[2],
                'precision': row[3],
                'scale': row[4],
                'nullable': row[5] == 'Y',
                'default': row[6],
                'column_id': row[7]
            }
            columns.append(column)
            
        # 获取主键信息
        cursor.execute(f"""
        SELECT cols.column_name 
        FROM all_constraints cons, all_cons_columns cols
        WHERE cons.constraint_type = 'P'
        AND cons.constraint_name = cols.constraint_name
        AND cons.owner = cols.owner
        AND cons.table_name = '{table_name.upper()}'
        ORDER BY cols.position
        """)
        
        pk_columns = [row[0] for row in cursor.fetchall()]
        
        return {
            'name': table_name.upper(),
            'columns': columns,
            'primary_key': pk_columns
        }
    finally:
        cursor.close()

def create_table_from_definition(table_def, conn):
    """根据表定义创建表"""
    cursor = conn.cursor()
    try:
        # 先删除表（如果存在）
        try:
            cursor.execute(f"DROP TABLE {table_def['name']}")
            conn.commit()
            print(f"已删除目标表 {table_def['name']}")
        except:
            # 表不存在，忽略错误
            conn.rollback()
            
        # 构建创建表的SQL语句
        create_sql = f"CREATE TABLE {table_def['name']} (\n"
        
        # 添加列定义
        column_defs = []
        for col in table_def['columns']:
            # 构建数据类型定义
            if col['type'] == 'NUMBER':
                if col['precision'] is not None:
                    if col['scale'] is not None and int(col['scale']) > 0:
                        type_def = f"NUMBER({col['precision']},{col['scale']})"
                    else:
                        type_def = f"NUMBER({col['precision']})"
                else:
                    type_def = "NUMBER"
            elif col['type'] in ('VARCHAR2', 'CHAR', 'NVARCHAR2'):
                type_def = f"{col['type']}({col['length']})"
            else:
                type_def = col['type']
                
            # 添加NULL约束
            null_def = "" if col['nullable'] else " NOT NULL"
            
            # 添加默认值
            default_def = ""
            if col['default'] is not None:
                if "SYSDATE" in str(col['default']).upper():
                    default_def = " DEFAULT SYSDATE"
                else:
                    default_def = f" DEFAULT {col['default']}"
            
            column_defs.append(f"  {col['name']} {type_def}{default_def}{null_def}")
        
        create_sql += ",\n".join(column_defs)
        
        # 添加主键约束
        if table_def['primary_key']:
            pk_constraint = f",\n  CONSTRAINT PK_{table_def['name']} PRIMARY KEY ({', '.join(table_def['primary_key'])})"
            create_sql += pk_constraint
            
        create_sql += "\n)"
        
        # 执行创建表语句
        print(f"创建表SQL: {create_sql}")
        cursor.execute(create_sql)
        conn.commit()
        print(f"成功创建表 {table_def['name']}")
        return True
    except Exception as e:
        conn.rollback()
        print(f"创建表失败: {str(e)}")
        logger.error(f"创建表失败: {str(e)}")
        return False
    finally:
        cursor.close()

def get_columns_as_string(table_def):
    """获取所有列名的字符串，用于构建SQL语句"""
    return ", ".join([col['name'] for col in table_def['columns']])

def fetch_data(table_name, source_conn, where_clause=None, batch_size=1000):
    """从源表获取数据，分批处理"""
    cursor = source_conn.cursor()
    
    try:
        # 设置NLS参数确保一致的数字格式
        cursor.execute("ALTER SESSION SET NLS_NUMERIC_CHARACTERS = '. '")
        source_conn.commit()
        
        # 获取表定义
        table_def = get_table_definition(table_name, source_conn)
        
        # 构建查询SQL
        columns = get_columns_as_string(table_def)
        query = f"SELECT {columns} FROM {table_name}"
        if where_clause:
            query += f" WHERE {where_clause}"
            
        # 执行查询
        print(f"执行查询: {query}")
        cursor.execute(query)
        
        # 分批获取结果
        while True:
            rows = cursor.fetchmany(batch_size)
            if not rows:
                break
            yield rows, table_def
            
    finally:
        cursor.close()

def insert_data_batch(target_table, columns, data_batch, target_conn):
    """批量插入数据到目标表"""
    if not data_batch:
        return 0
        
    cursor = target_conn.cursor()
    try:
        # 设置NLS参数确保一致的数字格式
        cursor.execute("ALTER SESSION SET NLS_NUMERIC_CHARACTERS = '. '")
        target_conn.commit()
        
        # 构建占位符
        placeholders = ", ".join([f":{i+1}" for i in range(len(columns.split(", ")))])
        
        # 构建插入SQL
        insert_sql = f"INSERT INTO {target_table} ({columns}) VALUES ({placeholders})"
        
        # 执行批量插入
        cursor.executemany(insert_sql, data_batch)
        affected_rows = cursor.rowcount
        target_conn.commit()
        return affected_rows
    except Exception as e:
        target_conn.rollback()
        print(f"批量插入失败: {str(e)}")
        logger.error(f"批量插入失败: {str(e)}")
        
        # 尝试逐行插入，跳过有问题的行
        successful_inserts = 0
        for i, row in enumerate(data_batch):
            try:
                cursor.execute(insert_sql, row)
                target_conn.commit()
                successful_inserts += 1
            except Exception as row_error:
                target_conn.rollback()
                error_msg = str(row_error)
                if i < 5:  # 只显示前5个错误
                    print(f"行 {i} 插入失败: {error_msg[:100]}")
                    print(f"问题数据: {row}")
        
        return successful_inserts
    finally:
        cursor.close()

def validate_number_fields(source_table, source_conn):
    """验证NUMBER字段中的数据格式"""
    source_cursor = source_conn.cursor()
    try:
        # 获取所有NUMBER类型的列
        source_cursor.execute(f"""
        SELECT column_name
        FROM all_tab_columns
        WHERE table_name = '{source_table.upper()}'
        AND data_type = 'NUMBER'
        """)
        number_columns = [row[0] for row in source_cursor.fetchall()]
        
        if not number_columns:
            print("未找到NUMBER类型列")
            return True
            
        # 检查每个NUMBER列中是否有无效值
        for col in number_columns:
            try:
                # 检查NULL值
                source_cursor.execute(f"SELECT COUNT(*) FROM {source_table} WHERE {col} IS NULL")
                null_count = source_cursor.fetchone()[0]
                print(f"列 {col} 中有 {null_count} 行为NULL值")
                
                # 检查所有值
                source_cursor.execute(f"SELECT MIN({col}), MAX({col}) FROM {source_table} WHERE {col} IS NOT NULL")
                min_max = source_cursor.fetchone()
                if min_max[0] is not None:
                    print(f"列 {col} 的值范围: {min_max[0]} 到 {min_max[1]}")
                    
                print(f"列 {col} 的数据格式有效")
            except Exception as e:
                print(f"检查列 {col} 时出错: {str(e)}")
        
        return True
    except Exception as e:
        print(f"验证NUMBER字段失败: {str(e)}")
        logger.error(f"验证NUMBER字段失败: {str(e)}")
        return False
    finally:
        source_cursor.close()

def sync_data(source_table, target_table, where_clause=None, is_first_sync=False):
    """同步数据从测试环境到生产环境"""
    print(f"开始同步数据: {source_table} -> {target_table}")
    print(f"条件: {where_clause if where_clause else '全量数据'}")
    
    start_time = datetime.now()
    
    # 连接数据库
    source_conn = None
    target_conn = None
    
    try:
        # 连接源数据库（测试环境）
        test_db_config = get_db_config('TEST_DB')
        source_conn = oracle.connect(get_connection_string(test_db_config))
        print("已连接到测试环境数据库")
        
        # 连接目标数据库（生产环境）
        prod_db_config = get_db_config('PROD_DB')
        target_conn = oracle.connect(get_connection_string(prod_db_config))
        print("已连接到生产环境数据库")
        
        # 验证NUMBER字段
        print("验证源表的NUMBER字段...")
        validate_number_fields(source_table, source_conn)
        
        # 检查目标表是否存在，不存在则创建
        table_exists = check_table_exists(target_table, target_conn)
        if not table_exists:
            print(f"目标表 {target_table} 不存在，正在创建...")
            # 获取源表结构
            table_def = get_table_definition(source_table, source_conn)
            # 创建目标表
            success = create_table_from_definition(table_def, target_conn)
            if not success:
                raise ValueError("创建表失败，中止同步")
        
        # 如果是第一次同步，先清空目标表
        if is_first_sync and table_exists:
            target_cursor = target_conn.cursor()
            try:
                target_cursor.execute(f"DELETE FROM {target_table}")
                target_conn.commit()
                print(f"已清空目标表 {target_table}")
            except Exception as e:
                target_conn.rollback()
                print(f"清空表失败: {str(e)}")
            finally:
                target_cursor.close()
        
        # 分批获取和插入数据
        total_rows = 0
        for batch, table_def in fetch_data(source_table, source_conn, where_clause):
            columns = get_columns_as_string(table_def)
            rows_inserted = insert_data_batch(target_table, columns, batch, target_conn)
            total_rows += rows_inserted
            print(f"已插入 {total_rows} 行数据")
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        print(f"同步完成! 总共插入 {total_rows} 行数据")
        print(f"开始时间: {start_time}, 结束时间: {end_time}, 耗时: {duration} 秒")
            
    except Exception as e:
        print(f"同步过程中发生错误: {str(e)}")
        logger.error(f"同步过程中发生错误: {str(e)}")
    finally:
        if source_conn:
            source_conn.close()
        if target_conn:
            target_conn.close()

if __name__ == "__main__":
    # 简单交互式配置
    print("=== NUMBER类型数据同步工具 ===")
    print("1. 全量同步 - 第一次操作")
    print("2. 增量同步 - 后续操作")
    choice = input("请选择操作类型 [1/2]: ") or "1"
    
    if choice == "1":
        # 全量同步
        source_table = input("请输入源表名称 [CS_SINGLEWINDOW_PRODUCT_CODE]: ") or "CS_SINGLEWINDOW_PRODUCT_CODE"
        target_table = input("请输入目标表名称 [T_BC_SW_TS_CODE]: ") or "T_BC_SW_TS_CODE"
        sync_data(source_table, target_table, is_first_sync=True)
    else:
        # 增量同步
        source_table = input("请输入源表名称 [CS_SINGLEWINDOW_PRODUCT_CODE]: ") or "CS_SINGLEWINDOW_PRODUCT_CODE"
        target_table = input("请输入目标表名称 [T_BC_SW_TS_CODE]: ") or "T_BC_SW_TS_CODE"
        
        # 默认使用createtime > 最近12小时的数据
        where_clause = input("请输入过滤条件 [createtime > sysdate - 0.5]: ") or "createtime > sysdate - 0.5"
        sync_data(source_table, target_table, where_clause=where_clause, is_first_sync=False)