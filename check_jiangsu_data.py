import pandas as pd

# 读取江苏省数据
file_path = r"C:\Users\<USER>\Desktop\海关数据\小超海关统计\全国进出口\20250530\2025年1-4月 - 江苏\2025年4月江苏省主要进出口数据.xls"
df = pd.read_excel(file_path, sheet_name='贸易方式')

print('Data shape:', df.shape)
print('\nFirst 15 rows:')
for i in range(min(15, len(df))):
    row_data = []
    for j in range(min(6, df.shape[1])):
        val = df.iloc[i, j]
        if pd.isna(val):
            row_data.append('NaN')
        else:
            row_data.append(str(val)[:20])  # 限制长度
    print(f'Row {i}: {" | ".join(row_data)}')

print('\n查找包含"总值"的行:')
for i in range(len(df)):
    for j in range(df.shape[1]):
        val = str(df.iloc[i, j])
        if '总值' in val:
            print(f'Row {i}, Col {j}: {val}')
            # 显示总值行的完整数据
            print(f'  总值行完整数据: {[str(df.iloc[i, k]) for k in range(min(10, df.shape[1]))]}')
            break

print('\n查找包含"一般贸易"的行:')
for i in range(len(df)):
    for j in range(df.shape[1]):
        val = str(df.iloc[i, j])
        if '一般贸易' in val:
            print(f'Row {i}, Col {j}: {val}')
            # 显示这一行的所有数据
            print(f'  完整行数据: {[str(df.iloc[i, k]) for k in range(min(10, df.shape[1]))]}')
            break