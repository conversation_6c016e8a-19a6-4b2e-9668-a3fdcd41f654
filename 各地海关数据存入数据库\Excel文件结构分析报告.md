# 浙江省进出口数据Excel文件结构分析报告

## 文件概述
本次分析了两个Excel文件的结构：
1. **2025年1月浙江省进出口情况.xlsx** - 新格式文件
2. **浙江单6月数据.xlsx** - 旧格式文件

## 文件结构对比分析

### 1. 工作表结构对比

两个文件都包含相同的6个工作表：
- 十一地市
- 主要国别（地区）
- 企业性质
- 贸易方式
- 主出商品
- 主进商品

### 2. 各工作表详细对比

#### 2.1 十一地市工作表
**相同点：**
- 列名完全一致：['Unnamed: 0', '项目', '当期进出口', '当期进口', '当期出口', '进出口同比', '进口同比', '出口同比']
- 行数相同：14行
- 数据类型相同：所有数值列均为float64类型

**差异点：**
- 数据内容不同（这是正常的，因为是不同时期的数据）
- 2025年1月文件中"当期进出口"总额为：5.49亿元
- 6月文件中"当期进出口"总额为：27.33亿元

#### 2.2 主要国别（地区）工作表
**相同点：**
- 列名结构相同：['项目', '当期进出口', '当期进口', '当期出口', '进出口同比', '进口同比', '出口同比', 'Unnamed: 7', '\xa0单位：人民币，万元，%']
- 数据类型相同

**差异点：**
- 行数略有差异：
  - 2025年1月文件：35行
  - 6月文件：34行
- 数据内容不同

#### 2.3 企业性质工作表
**相同点：**
- 列名完全一致：['项目', '当期进出口', '当期进口', '当期出口', '进出口同比', '进口同比', '出口同比']
- 行数相同：16行
- 数据类型相同

**差异点：**
- 数据内容不同
- 2025年1月文件中"当期进出口"总额为：8.45亿元
- 6月文件中"当期进出口"总额为：27.33亿元

#### 2.4 贸易方式工作表
**相同点：**
- 列名完全一致：['项目', '当期进出口', '当期进口', '当期出口', '进出口同比', '进口同比', '出口同比']
- 数据类型相同

**差异点：**
- 行数不同：
  - 2025年1月文件：16行
  - 6月文件：23行
- 6月文件包含更多贸易方式分类（如国家间、国际组织无偿援助和赠送的物资等）
- 数据内容不同

#### 2.5 主出商品工作表
**相同点：**
- 列名结构相同：['项目', '当期', '同比', '\xa0单位：人民币，万元，%']
- 数据类型基本相同

**差异点：**
- 行数略有差异：
  - 2025年1月文件：160行
  - 6月文件：161行
- 同比列的数据类型不同：
  - 2025年1月文件：object类型
  - 6月文件：float64类型
- 数据内容不同

#### 2.6 主进商品工作表
**相同点：**
- 列名结构相同：['项目', '当期', '同比', '\xa0单位：人民币，万元，%']
- 数据类型基本相同

**差异点：**
- 行数不同：
  - 2025年1月文件：116行
  - 6月文件：122行
- 同比列的数据类型不同：
  - 2025年1月文件：object类型
  - 6月文件：object类型
- 数据内容不同

## 数据格式特点分析

### 1. 通用格式特点
- 所有工作表都使用人民币作为货币单位
- 数值单位为万元
- 同比数据以百分比形式表示
- 包含当期数据和同比数据

### 2. 数据类型问题
通过详细分析发现以下数据类型不一致的问题：

**2025年1月文件问题：**
- 贸易方式工作表中的同比列（进出口同比、进口同比、出口同比）为object类型
- 主出商品工作表中的同比列为object类型，而6月文件中为float64类型
- 主进商品工作表中的同比列为object类型

**6月文件问题：**
- 贸易方式工作表中的同比列（进出口同比、进口同比、出口同比）为object类型
- 主进商品工作表中的同比列为object类型

### 3. 数据质量问题
- 存在Unnamed列，这些列通常包含空值或无关数据
- 某些列名包含特殊字符（如\xa0）
- 数据中存在缺失值和特殊字符（如"*"）
- 缺失值统计显示某些列存在数据缺失

### 4. 数据行数差异
- 主出商品工作表：2025年1月文件160行，6月文件161行（差异1行）
- 主要国别（地区）工作表：2025年1月文件35行，6月文件34行（差异1行）
- 主进商品工作表：2025年1月文件116行，6月文件122行（差异6行）
- 贸易方式工作表：2025年1月文件16行，6月文件23行（差异7行）

## 修改建议

### 1. 数据清洗建议
1. **删除无用列**：删除所有"Unnamed"列，这些列通常包含空值或无关数据
2. **处理特殊字符**：清理列名和数据中的特殊字符（如\xa0）
3. **统一数据类型**：确保所有数值列（特别是同比列）统一为float64类型
4. **处理缺失值**：对缺失值进行适当处理（填充或删除）
5. **处理特殊字符**：移除数据中的"*"等特殊字符

### 2. 数据标准化建议
1. **列名标准化**：统一所有工作表的列名格式
2. **数据格式标准化**：确保所有数值数据的格式一致
3. **添加时间标识**：在数据中添加明确的时间标识字段
4. **统一数据结构**：处理不同文件间行数差异的问题

### 3. 具体问题处理方案

#### 3.1 数据类型统一
```python
def convert_numeric_columns(df):
    """统一数值列数据类型"""
    numeric_cols = ['当期进出口', '当期进口', '当期出口', '进出口同比', '进口同比', '出口同比']
    for col in numeric_cols:
        if col in df.columns:
            # 移除特殊字符并转换为数值
            df[col] = df[col].astype(str).str.replace('*', '').str.strip()
            df[col] = pd.to_numeric(df[col], errors='coerce')
    return df
```

#### 3.2 特殊工作表处理
```python
def process_special_sheets(df, sheet_name):
    """处理特殊工作表"""
    if sheet_name in ['主出商品', '主进商品']:
        df['当期'] = pd.to_numeric(df['当期'], errors='coerce')
        df['同比'] = df['同比'].astype(str).str.replace('*', '').str.strip()
        df['同比'] = pd.to_numeric(df['同比'], errors='coerce')
    return df
```

#### 3.3 完整数据清洗函数
```python
import pandas as pd
import numpy as np

def clean_excel_data(file_path):
    """完整的Excel数据清理函数"""
    excel_file = pd.ExcelFile(file_path)
    cleaned_data = {}
    
    for sheet_name in excel_file.sheet_names:
        # 读取原始数据
        df = pd.read_excel(file_path, sheet_name=sheet_name)
        
        # 删除Unnamed列
        df = df.loc[:, ~df.columns.str.contains('^Unnamed')]
        
        # 清理列名中的特殊字符
        df.columns = df.columns.str.replace('\xa0', ' ').str.strip()
        
        # 统一数值列数据类型
        df = convert_numeric_columns(df)
        
        # 处理特殊工作表
        df = process_special_sheets(df, sheet_name)
        
        # 处理缺失值
        df = df.dropna(subset=['项目'])  # 删除项目为空的行
        
        cleaned_data[sheet_name] = df
    
    return cleaned_data
```

### 4. 数据验证建议
1. **数据完整性检查**：验证所有工作表的基本数据结构
2. **数据类型验证**：确保所有数值列的数据类型正确
3. **数据范围检查**：验证同比数据是否在合理范围内
4. **重复数据检查**：检查是否存在重复的记录

## 结论

两个Excel文件的结构基本一致，可以兼容处理。主要差异在于：

### 结构差异
1. **工作表结构完全相同**：两个文件都包含相同的6个工作表
2. **列名基本一致**：主要列名结构相同，仅存在特殊字符差异
3. **数据行数略有不同**：不同工作表的行数存在1-7行的差异

### 数据质量差异
1. **数据类型不一致**：同比列在部分工作表中为object类型
2. **存在数据质量问题**：包含特殊字符、缺失值等
3. **数据内容不同**：这是正常的，因为是不同时期的数据

### 处理建议
1. **优先处理数据质量问题**：清理特殊字符和缺失值
2. **统一数据类型**：确保所有数值列的数据类型一致
3. **标准化处理流程**：建立统一的数据处理流程
4. **数据验证**：处理后进行数据质量验证

建议在进行数据库存储前进行完整的数据清洗和标准化，以确保数据质量和一致性。两个文件可以使用相同的处理流程，只需要注意处理行数差异的问题。