# 正确的DATA_SOURCE编码映射

## 官方编码规范
根据用户确认的正确编码：

```
01：全国
02：北京  ← 北京海关
03：上海
04：广州
05：深圳
06：江苏
07：浙江  ← 浙江海关
```

## 当前系统配置

### ✅ 浙江海关数据
- **文件**: `batch_transform_excel_smart.py`
- **编码**: `DATA_SOURCE = '07'` ✅ 正确

### ✅ 北京海关数据  
- **文件**: `beijing_customs_comprehensive_import.py`
- **编码**: `DATA_SOURCE = '02'` ✅ 已修正

## 验证SQL

```sql
-- 查看数据分布
SELECT DATA_SOURCE, 
       CASE DATA_SOURCE 
         WHEN '01' THEN '全国'
         WHEN '02' THEN '北京' 
         WHEN '03' THEN '上海'
         WHEN '04' THEN '广州'
         WHEN '05' THEN '深圳'
         WHEN '06' THEN '江苏'
         WHEN '07' THEN '浙江'
         ELSE '未知'
       END as 地区,
       COUNT(*) as 记录数
FROM T_STATISTICAL_CUS_TOTAL_CS 
GROUP BY DATA_SOURCE 
ORDER BY DATA_SOURCE;
```

## 执行流程

1. **清空表**: `TRUNCATE TABLE T_STATISTICAL_CUS_TOTAL_CS;`
2. **浙江数据**: `DATA_SOURCE = '07'`
3. **北京数据**: `DATA_SOURCE = '02'`