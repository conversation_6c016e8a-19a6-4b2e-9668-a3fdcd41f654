import pandas as pd
import os
import re
import cx_Oracle
import numpy as np

def convert_to_float_or_none(value):
    """清理数据，将'-'或空值转为None，其余转为浮点数"""
    if pd.isna(value) or str(value).strip() in ['-', '…']:
        return None
    try:
        return float(str(value).replace(',', ''))
    except (ValueError, TypeError):
        return None

def parse_table_7(file_path):
    """
    为表(7)重写的解析器，适配其真实的二维表格结构。
    """
    try:
        df_raw = pd.read_excel(file_path, header=None)
        
        # 1. 动态查找单位
        unit = "不明"
        for i in range(min(5, len(df_raw))):
            row_text = ' '.join(str(s) for s in df_raw.iloc[i].tolist() if pd.notna(s))
            unit_match = re.search(r'单位[：:]\s*([^\s]+)', row_text)
            if unit_match:
                unit = unit_match.group(1).strip()
                break
        
        # 2. 动态查找表头行
        header_start_row = -1
        for i, row in df_raw.iterrows():
            row_str = ' '.join(str(s) for s in row if pd.notna(s))
            if '贸易方式' in row_str and '合计' in row_str and '企业性质' in row_str:
                header_start_row = i
                break
        if header_start_row == -1: return None, None

        # 3. 数据从表头下两行开始
        data_start_row = header_start_row + 2
        
        # 4. 提取备注信息
        remarks = ''
        data_end_row = len(df_raw)
        for i in range(data_start_row, len(df_raw)):
            # 备注通常在第一列或第二列
            cell_value = str(df_raw.iloc[i, 1]) if len(df_raw.iloc[i]) > 1 else ''
            if '注' in cell_value:
                data_end_row = i
                remarks = cell_value.strip()
                break

        df_data = df_raw.iloc[data_start_row:data_end_row]

        # 5. 合并隔行数据 (金额和同比)
        processed_data = []
        for i in range(0, len(df_data), 2):
            row_amount = df_data.iloc[i]
            if i + 1 >= len(df_data): continue
            row_yoy = df_data.iloc[i+1]
            
            trade_mode = row_amount.iloc[1]
            if pd.isna(trade_mode) or str(trade_mode).strip() == '': continue

            record = {
                'TRADE_MODE': str(trade_mode).strip(),
                'TOTAL_AMOUNT': convert_to_float_or_none(row_amount.iloc[2]),
                'TOTAL_YOY': convert_to_float_or_none(row_yoy.iloc[2]),
                'STATE_OWNED_AMOUNT': convert_to_float_or_none(row_amount.iloc[3]),
                'STATE_OWNED_YOY': convert_to_float_or_none(row_yoy.iloc[3]),
                'FOREIGN_INVESTED_AMOUNT': convert_to_float_or_none(row_amount.iloc[4]),
                'FOREIGN_INVESTED_YOY': convert_to_float_or_none(row_yoy.iloc[4]),
                'PRIVATE_AMOUNT': convert_to_float_or_none(row_amount.iloc[5]),
                'PRIVATE_YOY': convert_to_float_or_none(row_yoy.iloc[5]),
                'OTHER_AMOUNT': convert_to_float_or_none(row_amount.iloc[6]),
                'OTHER_YOY': convert_to_float_or_none(row_yoy.iloc[6]),
            }
            processed_data.append(record)
        
        df_result = pd.DataFrame(processed_data)
        if not df_result.empty:
            df_result['REMARKS'] = remarks
        return df_result, unit

    except Exception as e:
        print(f"    [!] 解析文件 '{os.path.basename(file_path)}' 时出错: {e}")
        return None, None

def check_if_data_exists_7(filename, cursor):
    """根据文件名和贸易方式'总值'检查数据是否已存在"""
    year_month_match = re.search(r'(\d{4})年(?:1至)?(\d{1,2})月', filename)
    if not year_month_match: return False
    year, month = year_month_match.groups()
    current_month = f"{year}{month.zfill(2)}01"
    currency_type = "人民币" if "人民币" in filename else "美元"
    
    query = "SELECT COUNT(*) FROM temp_cus_mon_7 WHERE CURRENT_MONTH = TO_DATE(:1, 'YYYYMMDD') AND CURRENCY_TYPE = :2 AND TRADE_MODE = '总值'"
    cursor.execute(query, (current_month, currency_type))
    return cursor.fetchone()[0] > 0

def batch_process_directory_7(directory_path, cursor):
    """遍历目录，对新文件执行增量入库"""
    print(f"\n--- 开始扫描目录: {directory_path} ---")
    if not os.path.isdir(directory_path):
        print(f"    [!] 错误: 目录不存在")
        return

    files_to_process = [f for f in sorted(os.listdir(directory_path)) if f.lower().endswith(('.xls', '.xlsx')) and not f.startswith('~')]
    
    for filename in files_to_process:
        if check_if_data_exists_7(filename, cursor):
            # print(f"    数据已存在, 跳过: {filename}")
            continue

        file_path = os.path.join(directory_path, filename)
        print(f"    [*] 发现新文件, 正在处理: {filename}")
        
        df, unit = parse_table_7(file_path)
        
        if df is not None and not df.empty:
            year_month_match = re.search(r'(\d{4})年(?:1至)?(\d{1,2})月', filename)
            if not year_month_match:
                print(f"    [!] 无法从文件名中解析年月: {filename}")
                continue
            
            year, month = year_month_match.groups()
            df['CURRENT_MONTH'] = f"{year}{month.zfill(2)}01"
            df['CURRENCY_TYPE'] = "人民币" if "人民币" in filename else "美元"
            df['UNIT'] = unit
            
            if 'REMARKS' not in df.columns:
                df['REMARKS'] = ''

            column_order = [
                'TRADE_MODE', 'CURRENT_MONTH', 'CURRENCY_TYPE', 'UNIT', 'REMARKS',
                'TOTAL_AMOUNT', 'TOTAL_YOY',
                'STATE_OWNED_AMOUNT', 'STATE_OWNED_YOY',
                'FOREIGN_INVESTED_AMOUNT', 'FOREIGN_INVESTED_YOY',
                'PRIVATE_AMOUNT', 'PRIVATE_YOY',
                'OTHER_AMOUNT', 'OTHER_YOY'
            ]
            df = df[column_order]

            df = df.replace({np.nan: None})
            
            data_to_insert = [tuple(row) for row in df.to_records(index=False)]

            insert_query = """
            INSERT INTO temp_cus_mon_7 (
                TRADE_MODE, CURRENT_MONTH, CURRENCY_TYPE, UNIT, REMARKS,
                TOTAL_AMOUNT, TOTAL_YOY,
                STATE_OWNED_AMOUNT, STATE_OWNED_YOY,
                FOREIGN_INVESTED_AMOUNT, FOREIGN_INVESTED_YOY,
                PRIVATE_AMOUNT, PRIVATE_YOY,
                OTHER_AMOUNT, OTHER_YOY
            ) VALUES (
                :1, TO_DATE(:2, 'YYYYMMDD'), :3, :4, :5, 
                :6, :7, :8, :9, :10, :11, :12, :13, :14, :15
            )
            """
            try:
                cursor.executemany(insert_query, data_to_insert)
                print(f"        -> 成功插入 {cursor.rowcount} 条记录.")
            except cx_Oracle.Error as e:
                print(f"        [!] 数据库插入错误: {e}")
        else:
            print(f"    -> 文件 {filename} 解析为空或失败，跳过。")

    print(f"--- 目录扫描完成 ---")

def main():
    """主执行函数"""
    base_dir = os.getcwd()
    rmb_dir = os.path.join(base_dir, "进出口商品统计表_人民币值", "(7)进口商品贸易方式企业性质总值表_人民币值")
    usd_dir = os.path.join(base_dir, "进出口商品统计表_美元值", "(7)进口商品贸易方式企业性质总值表_美元值")
    
    conn = None
    try:
        print("正在连接到Oracle数据库...")
        conn = cx_Oracle.connect("manifest_dcb/manifest_dcb@192.168.1.151/TEST")
        cursor = conn.cursor()
        print("数据库连接成功。")

        # 处理(7)进口表
        batch_process_directory_7(rmb_dir, cursor)
        batch_process_directory_7(usd_dir, cursor)
        
        conn.commit()
        print("\n数据提交成功。")
        
    except cx_Oracle.Error as error:
        print(f"\n[!!!] 数据库操作期间发生严重错误: {error}")
    except Exception as e:
        print(f"\n[!!!] 批量处理期间发生未知错误: {e}")
    finally:
        if 'cursor' in locals() and cursor: cursor.close()
        if conn:
            conn.close()
            print("数据库连接已关闭。")

if __name__ == "__main__":
    main() 