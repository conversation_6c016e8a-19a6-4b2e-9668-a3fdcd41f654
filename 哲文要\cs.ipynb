{"cells": [{"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["\n", "from DrissionPage import Chromium, ChromiumOptions\n", "from datetime import datetime\n", "import pandas as pd\n", "import os\n", "import re\n", "import time\n", "import random\n", "import json\n", "\n", "co = ChromiumOptions().auto_port()\n", "browser = Chromium(addr_or_opts=co)\n", "tab = browser.latest_tab"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["tab.get('http://gdfs.customs.gov.cn/guangdong_sub/zwgk62/sjgb59/index.html')\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["<ChromiumElement a href='/guangdong_sub/zwgk62/sjgb59/6595462/index.html' onclick='void(0)' target='_blank' title='（3）2025年1-5月广东省外贸进出口月度总值表（人民币值）' istitle='true'>"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["tab.ele('（3）', timeout=3)"]}], "metadata": {"kernelspec": {"display_name": "oracle_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.20"}}, "nbformat": 4, "nbformat_minor": 2}