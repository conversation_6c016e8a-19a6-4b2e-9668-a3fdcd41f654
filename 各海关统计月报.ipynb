from DrissionPage import Chromium, ChromiumOptions
from DrissionPage import SessionPage
from urllib.parse import urlparse
from datetime import datetime

import shutil
import json
import time

import requests
import time
import json
import os
import re
import random




# 先判断文件夹是否存在，如果不存在则创建它
# if not os.path.exists('数据落地'):
#     os.mkdir('数据落地')




# 读取excel数据
# 读取当前路径的excel文件
# 获取当前目录
# 获取当前目录
current_directory = os.getcwd()
# 获取当前日期并格式化为 YYYYMMDD 的字符串
today = datetime.now().strftime("%Y%m%d")
# 拼接新文件夹的完整路径
new_folder_path = os.path.join(current_directory, today)
# 如果新文件夹不存在则创建
if not os.path.exists(new_folder_path):
    os.makedirs(new_folder_path)
# 要创建的子文件夹名称列表
sub_folders = ['北京', '广州', '江苏', '全国', '上海', '浙江']
for sub_folder in sub_folders:
    sub_folder_path = os.path.join(new_folder_path, sub_folder)
    if not os.path.exists(sub_folder_path):
        os.makedirs(sub_folder_path)
        print(f"在 {new_folder_path} 下成功创建文件夹: {sub_folder}")

        
folder_full_path = os.path.join(current_directory, today)


#tab = Chromium().latest_tab

co = ChromiumOptions().auto_port()

tab = Chromium(addr_or_opts=co).latest_tab



def sanitize_filename(filename):
    """
    去除文件名中的非法字符
    :param filename: 原始文件名
    :return: 处理后的文件名
    """
    INVALID_CHARS_REGEX = r'[\\/*?:"<>|]'

    return re.sub(INVALID_CHARS_REGEX, '', filename)

def download_file(url, save_path):
    # 这里假设 SessionPage 是已经定义好的类
    page = SessionPage()
    res = page.download(url, save_path)
    print(res)
    return res

def find_a_to_down(text_path,download_path):
    down_name = text_path.text

    new_month = ''
    # 定位这个元素的下一个元素
    text_path = text_path.parent().children()[1]
    # print(text_path)
    # 获取 td 元素下所有的 a 标签
    a_elements = text_path.children('tag:a')

    # 用于存储最后一个有效的 href
    last_valid_href = None

    # 遍历所有 a 标签
    for a in a_elements:
        href = a.attr('href')
        if href:
            last_valid_href = href
            new_month = a.text
            print(new_month)

    # 输出最后一个有效的 href
    if last_valid_href:
        print('# 输出最后一个有效的 href:',last_valid_href)
    else:
        print("未找到有效的 a 标签链接。")

    print('new_month:',new_month)
    tab.get(last_valid_href)


    down_name = tab.ele('xpath=/html/body/div[4]/div/div[2]/div/div/div[1]/h2').text

    # 设置下载路径
    # download_path = r'C:\Users\<USER>\Desktop\海关数据\总署统计快讯\全国'
    download_path = download_path

    # 假设 tab 是已经定义好的对象
    this_url = tab.eles('下载')[-1].attr('href')
    # 假设 tab 是已经定义好的对象
    this_url = tab.eles('下载')[-1].attr('href')
    download_result = download_file(this_url, download_path)
    # 从 URL 中提取文件名
    parsed_url = urlparse(this_url)
    web_file_name = os.path.basename(parsed_url.path)
    print(web_file_name)

    # 假设 down_name 是已经定义好的变量
    # 设置文件名字
    file_name = down_name + '.xls'

    # 处理新文件名，去除非法字符
    new_file_name = sanitize_filename(file_name)

    # 构建原文件和新文件的完整路径
    old_file_path = os.path.join(download_path, web_file_name)
    new_file_path = os.path.join(download_path, new_file_name)

    try:
        # 重命名文件
        os.rename(old_file_path, new_file_path)
        print(f"文件重命名成功，原文件 {web_file_name} 已重命名为 {new_file_name}")
    except FileNotFoundError:
        print(f"未找到文件: {old_file_path}，请检查文件路径是否正确。")
    except FileExistsError:
        print(f"新文件名 {new_file_name} 已存在，请更换新的文件名。")
    except PermissionError:
        print("没有权限对文件进行重命名操作，请检查文件权限。")
    except Exception as e:
        print(f"重命名文件时出现未知错误: {e}")



def find_a_to_down_last_mon(text_path,download_path):
    down_name = text_path.text

    new_month = ''
    last_month = ''
    # 定位这个元素的下一个元素
    text_path = text_path.parent().children()[1]
    # print(text_path)
    # 获取 td 元素下所有的 a 标签
    a_elements = text_path.children('tag:a')

    # 用于存储最后一个有效的 href
    last_valid_href = None
    last_href = None
    # 遍历所有 a 标签
    for a in a_elements:
        href = a.attr('href')
        if href:
            last_href = last_valid_href
            last_valid_href = href


            last_month = new_month
            new_month = a.text
            print(new_month)

    new_month = last_month
    # 输出最后一个有效的 href
    if last_valid_href:
        print('# 输出最后一个有效的 href:',last_valid_href)
    else:
        print("未找到有效的 a 标签链接。")

    print('new_month:',new_month)
    tab.get(last_href)


    down_name = tab.ele('xpath=/html/body/div[4]/div/div[2]/div/div/div[1]/h2').text
    print(down_name)
    # 设置下载路径
    # download_path = r'C:\Users\<USER>\Desktop\海关数据\总署统计快讯\全国'
    download_path = download_path
    # 假设 tab 是已经定义好的对象
    this_url = tab.eles('下载')[-1].attr('href')
    # 假设 tab 是已经定义好的对象
    this_url = tab.eles('下载')[-1].attr('href')
    download_result = download_file(this_url, download_path)
    # 从 URL 中提取文件名
    parsed_url = urlparse(this_url)
    web_file_name = os.path.basename(parsed_url.path)
    print(web_file_name)

    # 假设 down_name 是已经定义好的变量
    # 设置文件名字
    file_name = down_name + '.xls'

    # 处理新文件名，去除非法字符
    new_file_name = sanitize_filename(file_name)

    # 构建原文件和新文件的完整路径
    old_file_path = os.path.join(download_path, web_file_name)
    new_file_path = os.path.join(download_path, new_file_name)

    try:
        # 重命名文件
        os.rename(old_file_path, new_file_path)
        print(f"文件重命名成功，原文件 {web_file_name} 已重命名为 {new_file_name}")
    except FileNotFoundError:
        print(f"未找到文件: {old_file_path}，请检查文件路径是否正确。")
    except FileExistsError:
        print(f"新文件名 {new_file_name} 已存在，请更换新的文件名。")
    except PermissionError:
        print("没有权限对文件进行重命名操作，请检查文件权限。")
    except Exception as e:
        print(f"重命名文件时出现未知错误: {e}")




# 全国的
tab.get('http://www.customs.gov.cn/eportal/ui?pageId=302275&currentPage=1&moduleId=9f806879368d4feabb9644105dcdeba3&staticRequest=yes')

# tab定位a标签内容为：“统计月报”.
try:
    tab.ele('统计月报').click()
except Exception as e:
    print(e)

#  年进出口商品总值表 B:月度表
download_path = os.path.join(folder_full_path, '全国')


# 1 进出口商品总值表 B:月度表
text_path = tab.eles('进出口商品总值表 B:月度表')[0]
#设置当前月份
find_a_to_down(text_path,download_path)
tab.back()

# 2 进出口商品国别（地区）总值表（人民币）
text_path = tab.eles('进出口商品国别(地区)总值表')[0]
#设置当前月份
find_a_to_down(text_path, download_path)
tab.back()


#  5
text_path = tab.eles('进出口商品贸易方式总值表')[0]
#设置当前月份
find_a_to_down(text_path, download_path)
tab.back()


#  6
text_path = tab.eles('出口商品贸易方式企业性质总值表')[0]
#设置当前月份
find_a_to_down(text_path, download_path)
tab.back()

#  6
text_path = tab.eles('出口商品贸易方式企业性质总值表')[0]
#设置当前月份
find_a_to_down_last_mon(text_path, download_path)
tab.back()

#  7
text_path = tab.eles('进口商品贸易方式企业性质总值表')[0]
#设置当前月份
find_a_to_down(text_path, download_path)
tab.back()



#  7
text_path = tab.eles('进口商品贸易方式企业性质总值表')[0]
#设置当前月份
find_a_to_down_last_mon(text_path, download_path)
tab.back()


#  13
text_path = tab.eles('出口主要商品量值表')[0]
#设置当前月份
find_a_to_down(text_path, download_path)
tab.back()

#  14
text_path = tab.eles('进口主要商品量值表')[0]
#设置当前月份
find_a_to_down(text_path, download_path)
tab.back()



# 去年的这个月的  2
text_path = tab.eles('进出口商品国别(地区)总值表')[0]
down_name = text_path.text

# 定位这个元素的下一个元素
text_path = text_path.parent().children()[1]
# print(text_path)
# 获取 td 元素下所有的 a 标签
a_elements = text_path.children('tag:a')

# 用于存储最后一个有效的 href
last_valid_href = None

# 遍历所有 a 标签
for a in a_elements:
    href = a.attr('href')
    if href:
        last_valid_href = href
        new_month = a.text
        print(new_month)
#  去年同期的进出口商品国别（地区）总值表（人民币）
try:
    last_year = tab.ele('.tjYear').eles('tag:a')[1].text
    # 获取去年年份
    tab.ele(str(last_year)).click()
except Exception as e:
    print(e)

time.sleep(5)

text_path = tab.eles('进出口商品国别(地区)总值表')[0]

down_name = text_path.text

# 定位这个元素的下一个元素
text_path = text_path.parent().children()[1]
# print(text_path)
# 获取 td 元素下所有的 a 标签
a_elements = text_path.children('tag:a')

# 用于存储最后一个有效的 href
last_valid_href = None

# 遍历所有 a 标签
for a in a_elements:
    href = a.attr('href')
    if a.text == new_month :
        last_valid_href = href
        new_month = a.text
        print(new_month)

# 输出最后一个有效的 href
if last_valid_href:
    print('# 输出最后一个有效的 href:',last_valid_href)
else:
    print("未找到有效的 a 标签链接。")

print('new_month:',new_month)
tab.get(last_valid_href)



down_name = tab.ele('xpath=/html/body/div[4]/div/div[2]/div/div/div[1]/h2').text


# 假设 tab 是已经定义好的对象
this_url = tab.eles('下载')[-1].attr('href')
# 假设 tab 是已经定义好的对象
this_url = tab.eles('下载')[-1].attr('href')
download_result = download_file(this_url, download_path)
# 从 URL 中提取文件名
parsed_url = urlparse(this_url)
web_file_name = os.path.basename(parsed_url.path)
print(web_file_name)

# 假设 down_name 是已经定义好的变量
# 设置文件名字
file_name = down_name + '.xls'

# 处理新文件名，去除非法字符
new_file_name = sanitize_filename(file_name)

# 构建原文件和新文件的完整路径
old_file_path = os.path.join(download_path, web_file_name)
new_file_path = os.path.join(download_path, new_file_name)

try:
    # 重命名文件
    os.rename(old_file_path, new_file_path)
    print(f"文件重命名成功，原文件 {web_file_name} 已重命名为 {new_file_name}")
except FileNotFoundError:
    print(f"未找到文件: {old_file_path}，请检查文件路径是否正确。")
except FileExistsError:
    print(f"新文件名 {new_file_name} 已存在，请更换新的文件名。")
except PermissionError:
    print("没有权限对文件进行重命名操作，请检查文件权限。")
except Exception as e:
    print(f"重命名文件时出现未知错误: {e}")





# 全国的数据进行处理    先搞贸易方式的。


# 北京
tab.get('http://beijing.customs.gov.cn/beijing_customs/434756/434804/2941702/434773/434774/index.html')


tab.get(tab.eles('本关统计')[0].attr('href'))

def down_func(table_9, download_path):
    text_path = table_9[0]
    # text_path.click()
    
    
    print(text_path.attr('href'))
    tab.get(text_path.attr('href'))


    # tab.get(last_valid_href)
    # #设置当前月份


    down_name = tab.ele('xpath=/html/body/div[4]/div/div/div/div[2]/div/div/div[1]/h2').text

    # 设置下载路径
    #download_path = r'C:\Users\<USER>\Desktop\海关数据\总署统计快讯\北京'
    download_path = download_path

    # 假设 tab 是已经定义好的对象
    this_url = tab.ele('xpath=/html/body/div[4]/div/div/div/div[2]/div/div/div[2]/div[1]/p/a').attr('href')
    download_result = download_file(this_url, download_path)
    # 从 URL 中提取文件名
    parsed_url = urlparse(this_url)
    web_file_name = os.path.basename(parsed_url.path)
    print(web_file_name)

    # 假设 down_name 是已经定义好的变量
    # 设置文件名字
    file_name = down_name + '.xls'

    # 处理新文件名，去除非法字符
    new_file_name = sanitize_filename(file_name)

    # 构建原文件和新文件的完整路径
    old_file_path = os.path.join(download_path, web_file_name)
    new_file_path = os.path.join(download_path, new_file_name)

    try:
        # 重命名文件
        os.rename(old_file_path, new_file_path)
        print(f"文件重命名成功，原文件 {web_file_name} 已重命名为 {new_file_name}")
    except FileNotFoundError:
        print(f"未找到文件: {old_file_path}，请检查文件路径是否正确。")
    except FileExistsError:
        print(f"新文件名 {new_file_name} 已存在，请更换新的文件名。")
    except PermissionError:
        print("没有权限对文件进行重命名操作，请检查文件权限。")
    except Exception as e:
        print(f"重命名文件时出现未知错误: {e}")

# （9）北京地区进口主要商品量值表
table_9 = tab.eles('（9）北京地区进口主要商品量值表')

download_path = os.path.join(folder_full_path, '北京')


if table_9:
    down_func(table_9, download_path)
    tab.back()


# （8）北京地区出口主要商品量值表
table_8 = tab.eles('（8）北京地区出口主要商品量值表')

if table_8:
    down_func(table_8, download_path)
    tab.back()


# （7）北京地区进口商品贸易方式企业性质总值表
table_7 = tab.eles('（7）北京地区进口商品贸易方式企业性质总值表')

if table_7:
    down_func(table_7, download_path)
    tab.back()

# （6）北京地区出口商品贸易方式企业性质总值表

table_6 = tab.eles('（6）北京地区出口商品贸易方式企业性质总值表')

if table_6:
    down_func(table_6, download_path)
    tab.back()


# （5）北京地区进出口商品贸易方式总值表（2024年1-12月）
table_5 = tab.eles('（5）北京地区进出口商品贸易方式总值表')
if table_5:
    down_func(table_5, download_path)
    tab.back()




# 点击下一页
try:
    tab.ele('下一页').click()
except Exception as e:
    time.sleep(5)

time.sleep(5)


# （2）北京地区进出口商品国别（地区）总值表（2024年1-12月）
table_2 = tab.eles('（2）北京地区进出口商品国别（地区）总值表')
if table_2:
    down_func(table_2, download_path)
    tab.back()

time.sleep(5)



# （1）北京地区进出口商品总值表B：月度表（2024年1-12月）
table_1 = tab.eles('（1）北京地区进出口商品总值表B：月度表')
if table_1:
    down_func(table_1, download_path)
    tab.back()



# 6 7 上个月的
# 点击下一页
try:
    tab.ele('下一页').click()
except Exception as e:
    time.sleep(5)

time.sleep(5)



# （7）北京地区进口商品贸易方式企业性质总值表
table_7 = tab.eles('（7）北京地区进口商品贸易方式企业性质总值表')

if table_7:
    down_func(table_7, download_path)
    tab.back()

# （6）北京地区出口商品贸易方式企业性质总值表

table_6 = tab.eles('（6）北京地区出口商品贸易方式企业性质总值表')

if table_6:
    down_func(table_6, download_path)
    tab.back()

tab.get('http://guangzhou.customs.gov.cn/guangzhou_customs/381558/fdzdgknr33/381638/381572/381573/index.html')
tab.get(tab.eles('本关统计')[0].attr('href'))

def down_func(table_9,download_path):
    text_path = table_9[0]
    # text_path.click()
    
    print(text_path.attr('href'))
    tab.get(text_path.attr('href'))


    # tab.get(last_valid_href)
    # #设置当前月份


    down_name = tab.ele('xpath=/html/body/div[4]/div/div/div/div[2]/div/div/div[1]/h2').text

    # 设置下载路径
    #download_path = r'C:\Users\<USER>\Desktop\海关数据\总署统计快讯\北京'
    download_path = download_path

    # 假设 tab 是已经定义好的对象
    this_url = tab.ele('xpath=/html/body/div[4]/div/div/div/div[2]/div/div/div[2]/div[1]/p/a').attr('href')
    download_result = download_file(this_url, download_path)
    # 从 URL 中提取文件名
    parsed_url = urlparse(this_url)
    web_file_name = os.path.basename(parsed_url.path)
    print(web_file_name)

    # 假设 down_name 是已经定义好的变量
    # 设置文件名字
    file_name = down_name + '.xls'

    # 处理新文件名，去除非法字符
    new_file_name = sanitize_filename(file_name)

    # 构建原文件和新文件的完整路径
    old_file_path = os.path.join(download_path, web_file_name)
    new_file_path = os.path.join(download_path, new_file_name)

    try:
        # 重命名文件
        os.rename(old_file_path, new_file_path)
        print(f"文件重命名成功，原文件 {web_file_name} 已重命名为 {new_file_name}")
    except FileNotFoundError:
        print(f"未找到文件: {old_file_path}，请检查文件路径是否正确。")
    except FileExistsError:
        print(f"新文件名 {new_file_name} 已存在，请更换新的文件名。")
    except PermissionError:
        print("没有权限对文件进行重命名操作，请检查文件权限。")
    except Exception as e:
        print(f"重命名文件时出现未知错误: {e}")

table_6 = tab.eles('广州关区所辖7地市进出口综合统计资料')
download_path = os.path.join(folder_full_path, '广州')

if table_6:
    down_func(table_6, download_path)
    tab.back()





# 江苏
tab.get('http://nanjing.customs.gov.cn/nanjing_customs/zfxxgk58/fdzdgknr95/3010051/index.html')
time.sleep(1)
print(tab.eles('本关统计')[-2].attr('href'))
tab.get(tab.eles('本关统计')[-2].attr('href'))
# 设置下载路径
download_path = os.path.join(folder_full_path, '江苏')

# download_path = r'C:\Users\<USER>\Desktop\总署统计快讯\江苏'

# 假设 tab 是已经定义好的对象

time.sleep(5)
table_6 = tab.eles('江苏省主要进出口数据')

if table_6:

    print(table_6[0].text)
    this_url = table_6[0].attr('href')
    download_file(this_url, download_path)
    print(table_6[1].text)
    this_url = table_6[1].attr('href')
    download_file(this_url, download_path)

    #down_func(table_6)
    #tab.back()
    pass


# 上海 
tab.get('http://shanghai.customs.gov.cn/shanghai_customs/423405/fdzdgknr8/423468/1865071/5682924/5682955/index.html')
tab.get(tab.eles('海关统计')[0].attr('href'))
tab.get(tab.eles('关区数据')[0].attr('href'))
download_path = os.path.join(folder_full_path, '上海')
def up_file_name(this_url,down_name):
        # 下载文件
    parsed_url = urlparse(this_url)
    web_file_name = os.path.basename(parsed_url.path)
    print(web_file_name)

    # 假设 down_name 是已经定义好的变量
    # 设置文件名字
    file_name = down_name + '.xls'

    # 处理新文件名，去除非法字符
    new_file_name = sanitize_filename(file_name)

    # 构建原文件和新文件的完整路径
    old_file_path = os.path.join(download_path, web_file_name)
    new_file_path = os.path.join(download_path, new_file_name)

    try:
        # 重命名文件
        os.rename(old_file_path, new_file_path)
        print(f"文件重命名成功，原文件 {web_file_name} 已重命名为 {new_file_name}")
    except FileNotFoundError:
        print(f"未找到文件: {old_file_path}，请检查文件路径是否正确。")
    except FileExistsError:
        print(f"新文件名 {new_file_name} 已存在，请更换新的文件名。")
    except PermissionError:
        print("没有权限对文件进行重命名操作，请检查文件权限。")
    except Exception as e:
        print(f"重命名文件时出现未知错误: {e}")

# 这里要点下一页
tab.eles('下一页')[-1].click()



table_ = tab.ele('进出口商品企业性质总值表（人民币值）')
this_url = table_.attr('href')
download_file(this_url, download_path)
up_file_name(this_url,table_.text)

table_ = tab.ele('出口主要商品量值表（人民币值）')
this_url = table_.attr('href')
download_file(this_url, download_path)
up_file_name(this_url,table_.text)

table_ = tab.ele('进口主要商品量值表（人民币值）')
this_url = table_.attr('href')
download_file(this_url, download_path)
up_file_name(this_url,table_.text)

table_ = tab.ele('进出口商品章节总值表（人民币值）')
this_url = table_.attr('href')
download_file(this_url, download_path)
up_file_name(this_url,table_.text)



# 这里要点下一页
tab.eles('下一页')[-1].click()


table_ = tab.ele('进出口商品总值表（人民币值）')
this_url = table_.attr('href')
download_file(this_url, download_path)
up_file_name(this_url,table_.text)

table_ = tab.ele('进出口商品贸易方式总值表（人民币值）')
this_url = table_.attr('href')
download_file(this_url, download_path)
up_file_name(this_url,table_.text)

table_ = tab.ele('进出口商品国别（地区）总值表（人民币值）')
this_url = table_.attr('href')
download_file(this_url, download_path)
up_file_name(this_url,table_.text)







# 山东
tab.get('http://jinan.customs.gov.cn/jinan_customs/zfxxgk93/3014222/3014291/index.html')
tab.get(tab.eles('本关统计')[-1].attr('href'))


# 山东的要自己选上下月  不开发了
tab.get(tab.eles('山东省外贸进出口数据')[0].attr('href'))





# 辽宁省 的要自己选上下月  不开发了
tab.get('http://dalian.customs.gov.cn/shenyang_customs/zfxxgk4391/fdzdgknr57/bgtj98/3420901/2024/6328074/index.html')
ul_ = tab.ele('xpath=//*[@id="44b9e79bf31c412fa2a5a0d75916d6d9"]/div[2]/ul')
ul_.eles('tag:a')[0].click()


tab.get('http://xiamen.customs.gov.cn/xiamen_customs/zfxxgk22/3017978/3018709/index.html')

try:
    tab.eles('本关统计')[1].click()
except Exception as e:
    print(e)
time.sleep(5)





tab.get(tab.eles('厦门、泉州、漳州、龙岩进出口情况表')[0].attr('href'))





download_path = folder_full_path

down_name = tab.ele('xpath=/html/body/div[4]/div/div/div/div[2]/div/div/div[1]/h2').text
table_ = tab.ele('厦门、泉州、漳州、龙岩进出口情况表.docx')
this_url = table_.attr('href')
this_url
download_file(this_url, download_path)
        # 下载文件
parsed_url = urlparse(this_url)
web_file_name = os.path.basename(parsed_url.path)
print(web_file_name)

# 假设 down_name 是已经定义好的变量
# 设置文件名字
file_name = down_name + '.docx'

# 处理新文件名，去除非法字符
new_file_name = sanitize_filename(file_name)

# 构建原文件和新文件的完整路径
old_file_path = os.path.join(download_path, web_file_name)
new_file_path = os.path.join(download_path, new_file_name)

try:
    # 重命名文件
    os.rename(old_file_path, new_file_path)
    print(f"文件重命名成功，原文件 {web_file_name} 已重命名为 {new_file_name}")
except FileNotFoundError:
    print(f"未找到文件: {old_file_path}，请检查文件路径是否正确。")
except FileExistsError:
    print(f"新文件名 {new_file_name} 已存在，请更换新的文件名。")
except PermissionError:
    print("没有权限对文件进行重命名操作，请检查文件权限。")
except Exception as e:
    print(f"重命名文件时出现未知错误: {e}")
