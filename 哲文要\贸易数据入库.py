import pandas as pd
import os
import re
import cx_Oracle
import glob
import sys

# 设置pandas显示选项
pd.set_option('display.max_columns', None)
pd.set_option('display.width', 1000)
pd.set_option('display.max_colwidth', None)

def convert_to_float_or_none(value):
    """处理可能为'-'或包含逗号的字符串，转换为浮点数或None"""
    if pd.isna(value):
        return None
    if isinstance(value, str):
        value = value.replace(',', '').strip()
        if value == '-':
            return None
    try:
        return float(value)
    except (ValueError, TypeError):
        return None

def get_db_connection():
    try:
        conn = cx_Oracle.connect("manifest_dcb/manifest_dcb@*************/TEST")
        cursor = conn.cursor()
        print("数据库连接成功。")
        return conn, cursor
    except cx_Oracle.Error as error:
        print(f"数据库连接失败: {error}")
        return None

def check_record_exists(cursor, month_str, region):
    """检查数据库中是否已存在指定月份和地区的记录。"""
    query = "SELECT COUNT(*) FROM T_REGIONAL_TRADE_SUMMARY_RMB WHERE MONTH_LABEL = :1 AND REGION = :2"
    try:
        cursor.execute(query, (month_str, region))
        count = cursor.fetchone()[0]
        return count > 0
    except cx_Oracle.Error as error:
        print(f"查询记录是否存在时出错: {error}")
        return False # 出错时假定不存在，以防意外阻断

def insert_record(cursor, connection, data):
    """向数据库插入一条新记录。"""
    sql = "INSERT INTO T_REGIONAL_TRADE_SUMMARY_RMB (MONTH_LABEL, REGION, TOTAL_IMPORT_EXPORT, TOTAL_EXPORT, TOTAL_IMPORT) VALUES (:1, :2, :3, :4, :5)"
    try:
        cursor.execute(sql, data)
        connection.commit()
        return True
    except cx_Oracle.Error as error:
        print(f"数据插入失败: {error}")
        connection.rollback()
        return False

def process_trade_file(file_path, conn, cursor):
    """处理单个贸易统计文件（鲁棒版），提取累计值并插入数据库"""
    print(f"\n处理文件: {file_path}")
    
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return False

    try:
        filename = os.path.basename(file_path)

        # 1. 提取年月字符串 (最终修正版正则表达式)
        # 匹配 "2024年5月" 或 "2024年1-5月" 或 "2024年1至5月"
        year_month_match = re.search(r'(\d{4})年(\d{1,2}(?:至|-)\d{1,2}|\d{1,2})月', filename)

        if year_month_match:
            year = year_month_match.group(1)
            month_part = year_month_match.group(2)
            if '-' in month_part:
                start_month, end_month = month_part.split('-')
                current_month_str = f"{year}-{start_month.zfill(2)}/{end_month.zfill(2)}"
            elif '至' in month_part:
                start_month, end_month = month_part.split('至')
                current_month_str = f"{year}-{start_month.zfill(2)}/{end_month.zfill(2)}"
            else:
                current_month_str = f"{year}-{month_part.zfill(2)}"
            
            print(f"提取的年月字符串: {current_month_str}")
        else:
            print("无法从文件名中提取年月信息，跳过。")
            return False
            
        # 2. 提取地区
        region = '未知'
        if '全国' in filename: region = '全国'
        elif '广东省' in filename: region = '广东省'
        print(f"提取的地区: {region}")

        # 3. 读取Excel并按指定关键字暴力查找
        df = pd.read_excel(file_path, header=None)
        
        data = {'total': None, 'export': None, 'import': None}
        found_keys = set()

        # 遍历所有单元格来查找关键字
        for r_idx in range(df.shape[0]):
            for c_idx in range(df.shape[1]):
                cell_value = df.iloc[r_idx, c_idx]
                if pd.isna(cell_value) or not isinstance(cell_value, str):
                    continue
                
                clean_cell = cell_value.strip()

                key = None
                if '进出口总值' == clean_cell: key = 'total'
                elif '出口总值' == clean_cell: key = 'export'
                elif '进口总值' == clean_cell: key = 'import'

                # 如果找到关键字，并且右侧有单元格，则提取数据
                if key and key not in found_keys and c_idx + 1 < df.shape[1]:
                    target_value = df.iloc[r_idx, c_idx + 1]
                    data[key] = convert_to_float_or_none(target_value)
                    found_keys.add(key)
            
            # 如果三个值都找到了，就提前结束循环
            if len(found_keys) == 3:
                break

        # 4. 准备插入数据
        data_to_insert = (
            current_month_str,
            region,
            data.get('total'),
            data.get('export'),
            data.get('import')
        )

        print("提取的入库数据:", data_to_insert)

        # 5. 执行插入
        if check_record_exists(cursor, current_month_str, region):
            print(f"记录已存在于数据库中 (月份: {current_month_str}, 地区: {region})，跳过。")
            return False
        elif insert_record(cursor, conn, data_to_insert):
            print(f"成功插入 1 条数据到 T_REGIONAL_TRADE_SUMMARY_RMB")
            return True
        else:
            print(f"插入失败，跳过此文件。")
            return False

    except Exception as e:
        print(f"处理文件时发生错误: {e}")
        if conn: conn.rollback()
        return False

def batch_process_trade_files(directory_path):
    """批量处理目录下的所有贸易统计文件"""
    print(f"开始批量处理目录: {directory_path}")
    
    if not os.path.exists(directory_path):
        print(f"错误: 目录不存在 - {directory_path}")
        return
    
    all_files = glob.glob(os.path.join(directory_path, '**', '*.xls*'), recursive=True)
    if not all_files:
        print(f"未在 {directory_path} 目录下找到任何Excel文件。")
        return
    
    print(f"找到 {len(all_files)} 个Excel文件。")
    
    try:
        print("\n正在连接到Oracle数据库...")
        conn, cursor = get_db_connection()
        
        success_count = sum(1 for file_path in all_files if process_trade_file(file_path, conn, cursor))
        
        print("\n==== 处理完成 ====")
        print(f"共处理: {len(all_files)} 文件")
        print(f"成功入库: {success_count} 文件")
        
    except cx_Oracle.Error as error:
        print(f"Oracle数据库连接错误: {error}")
    except Exception as e:
        print(f"发生未知错误: {e}")
    finally:
        if 'cursor' in locals() and cursor: cursor.close()
        if 'conn' in locals() and conn:
            conn.close()
            print("数据库连接已关闭。")

if __name__ == "__main__":
    # 目标目录固定为 "文件落地"
    base_path = os.path.dirname(os.path.abspath(__file__))
    target_directory = os.path.join(base_path, "文件落地")

    print(f"将要处理的目标根目录是: {target_directory}")
    
    # 从命令行参数获取要处理的特定子目录名
    specific_folder = sys.argv[1] if len(sys.argv) > 1 else None

    if specific_folder:
        target_path = os.path.join(target_directory, specific_folder)
        if os.path.exists(target_path) and os.path.isdir(target_path):
            print(f"--- 已指定特定目录, 开始处理: {target_path} ---")
            batch_process_trade_files(target_path)
        else:
            print(f"错误: 指定的目录 '{target_path}' 不存在。")
    else:
        print(f"--- 未指定特定目录, 将处理根目录下所有子目录 ---")
        # 遍历根目录下的所有子文件夹
        for folder_name in os.listdir(target_directory):
            full_path = os.path.join(target_directory, folder_name)
            if os.path.isdir(full_path):
                batch_process_trade_files(full_path) 