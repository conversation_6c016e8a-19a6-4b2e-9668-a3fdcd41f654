
import pandas as pd
import os
import re
import cx_Oracle
import datetime

# 设置pandas显示选项
pd.set_option('display.max_columns', None)
pd.set_option('display.width', 1000)
pd.set_option('display.max_colwidth', None)

def convert_to_float_or_none(value):
    """清理数据，将'-'或空值转为None，其余转为浮点数"""
    if pd.isna(value) or str(value).strip() == '-':
        return None
    try:
        # 移除千分位分隔符
        cleaned_value = str(value).replace(',', '')
        # 尝试转换为浮点数
        return float(cleaned_value)
    except (ValueError, TypeError):
        return None

def process_new_table_format(file_path, conn):
    """
    处理新的三行式月度数据Excel文件。
    数据结构: 每个月包含 数值行、同比行、环比行。
    """
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return False
    
    filename = os.path.basename(file_path)
    print(f"[*] 正在处理文件: {filename}")

    # 1. 从文件名提取年份和货币类型
    year_match = re.search(r'(\d{4})', filename)
    if not year_match:
        print(f"    [!] 错误: 无法从文件名 '{filename}' 中提取年份。")
        return False
    target_year = year_match.group(1)
    
    currency_type = "人民币" if "人民币" in filename else "美元"
    
    try:
        df_raw = pd.read_excel(file_path, header=None)

        # 动态从文件内容中查找单位
        unit = None
        header_search_start_row = 0 
        for i in range(min(10, len(df_raw))): # 在文件前10行中搜索
            row_text = ' '.join(str(s) for s in df_raw.iloc[i].tolist() if pd.notna(s))
            unit_match = re.search(r'单位[：:]\s*([^\s]+)', row_text) # 匹配 "单位：" 或 "单位:"
            if unit_match:
                unit = unit_match.group(1).strip()
                header_search_start_row = i + 1 # 从下一行开始找表头
                break
        
        # 如果未找到单位，则使用修正后的默认值
        if not unit:
            print("    [!] 警告: 未在文件中找到单位信息，将使用修正后的默认值。")
            unit = "亿元" if currency_type == "人民币" else "千美元"
    
        print(f"    目标年份: {target_year}, 货币类型: {currency_type}, 单位: {unit}")

        # 2. 查找表头行
        header_start_idx = -1
        header_columns = []
        for i in range(header_search_start_row, len(df_raw)):
            row = df_raw.iloc[i]
            row_str = ' '.join(str(s) for s in row if pd.notna(s))
            if all(k in row_str for k in ['进出口', '出口', '进口', '贸易差额']):
                header_start_idx = i
                header_columns = row.tolist()
                break

        if header_start_idx == -1:
            print("    [!] 错误: 未找到表头行。")
            return False

        # 3. 数据从表头的下一行开始
        df = df_raw.iloc[header_start_idx + 1:].reset_index(drop=True)
        df.dropna(how='all', inplace=True)
        df = df.iloc[:, :len(header_columns)]
        df.columns = [f"COL_{i}" for i in range(len(header_columns))]

        # 4. 遍历数据，按3行一组处理
        i = 0
        processed_count = 0
        while i < len(df) - 2:
            current_row = df.iloc[i]
            # 年月信息在第二列（索引为1）
            cell_for_date = current_row.iloc[1]
            year_from_data, month_from_data = None, None

            try:
                if pd.notna(cell_for_date):
                    if isinstance(cell_for_date, (pd.Timestamp, datetime.datetime)):
                        year_from_data = str(cell_for_date.year)
                        month_from_data = str(cell_for_date.month)
                    else:
                        match = re.match(r'(\d{4})\.(\d{1,2})', str(cell_for_date).strip())
                        if match:
                            year_from_data, month_from_data = match.groups()
            except Exception:
                pass

            if not year_from_data or year_from_data != target_year:
                i += 1
                continue

            # 检查是否为三行结构（通过判断下一行的第二列是否为空）
            if pd.notna(df.iloc[i + 1].iloc[1]):
                i += 1
                continue
            
            current_month_str = f"{year_from_data}{str(month_from_data).zfill(2)}01"
            print(f"    -> 正在处理 {year_from_data}年{month_from_data}月 的数据...")
            
            # 数据应从第三列（索引2）开始提取，以跳过年月列
            value_data = current_row.iloc[2:]
            yoy_row = df.iloc[i + 1].iloc[2:]
            mom_row = df.iloc[i + 2].iloc[2:]
            
            data_to_insert = tuple([
                current_month_str, currency_type, unit,
                *map(convert_to_float_or_none, value_data),
                *map(convert_to_float_or_none, yoy_row),
                *map(convert_to_float_or_none, mom_row)
            ])

            cursor = None
            try:
                cursor = conn.cursor()

                # 1. 检查数据是否已存在
                check_query = "SELECT COUNT(*) FROM temp_cus_mon_1 WHERE CURRENT_MONTH = TO_DATE(:1, 'YYYYMMDD') AND CURRENCY_TYPE = :2"
                cursor.execute(check_query, (current_month_str, currency_type))
                exists = cursor.fetchone()[0]

                if exists:
                    print(f"        - 数据已存在，跳过插入。")
                    i += 3 # 移动到下一个数据块
                    continue

                # 2. 如果不存在，则插入数据
                insert_query = """
                INSERT INTO temp_cus_mon_1 (
                    CURRENT_MONTH, CURRENCY_TYPE, UNIT,
                    VAL_MONTH_IE, VAL_MONTH_EXP, VAL_MONTH_IMP, VAL_MONTH_TB,
                    VAL_YTD_IE, VAL_YTD_EXP, VAL_YTD_IMP, VAL_YTD_TB,
                    YOY_MONTH_IE, YOY_MONTH_EXP, YOY_MONTH_IMP, YOY_MONTH_TB,
                    YOY_YTD_IE, YOY_YTD_EXP, YOY_YTD_IMP, YOY_YTD_TB,
                    MOM_MONTH_IE, MOM_MONTH_EXP, MOM_MONTH_IMP, MOM_MONTH_TB,
                    MOM_YTD_IE, MOM_YTD_EXP, MOM_YTD_IMP, MOM_YTD_TB
                ) VALUES (
                    TO_DATE(:1, 'YYYYMMDD'), :2, :3, 
                    :4, :5, :6, :7, :8, :9, :10, :11,
                    :12, :13, :14, :15, :16, :17, :18, :19,
                    :20, :21, :22, :23, :24, :25, :26, :27
                )"""
                cursor.execute(insert_query, data_to_insert)
                conn.commit()
                print(f"        - 成功插入 1 条新数据。")
                processed_count += 1
            except cx_Oracle.Error as error:
                print(f"    [!] Oracle数据库错误: {error}")
                conn.rollback()
            finally:
                if cursor:
                    cursor.close()
            
            i += 3
        
        if processed_count == 0:
            print(f"    [!] 警告: 在文件 {filename} 中未找到与年份 {target_year} 匹配的有效数据。")
        else:
            print(f"    [*] 文件处理完成，共处理 {processed_count} 个月的数据。")
        
        return processed_count > 0

    except Exception as e:
        print(f"    [!] 处理文件时发生未知错误: {e}")
        return False

def batch_process_directory(directory_path, conn):
    """遍历目录及其子目录，对所有匹配的Excel文件执行处理"""
    from pathlib import Path

    print(f"\n--- 开始扫描目录: {directory_path} ---")
    base_dir = Path(directory_path)
    if not base_dir.is_dir():
        print(f"    [!] 错误: 目录不存在 - {directory_path}")
        return

    files_to_process = sorted(list(base_dir.rglob('*.xlsx'))) + sorted(list(base_dir.rglob('*.xls')))
    
    if not files_to_process:
        print(f"    [!] 警告: 在目录 {directory_path} 中未找到任何 .xlsx 或 .xls 文件。")
        return

    for file_path in files_to_process:
        if file_path.name.startswith('~'):
            continue
        process_new_table_format(str(file_path), conn)

    print(f"--- 目录扫描完成: {directory_path} ---")

if __name__ == "__main__":
    base_dir = os.getcwd() 
    # 恢复为用户提供的正确目录名
    rmb_directory = os.path.join(base_dir, "进出口商品统计表_人民币值", "(1)进出口商品总值表_B月度表_人民币值")
    usd_directory = os.path.join(base_dir, "进出口商品统计表_美元值", "(1)进出口商品总值表_B月度表_美元值")

    conn_main = None
    try:
        print("---[ 开始批量处理 ]---")
        print("建立主数据库连接...")
        conn_main = cx_Oracle.connect("manifest_dcb/manifest_dcb@192.168.1.151/TEST")
        print("数据库连接成功。")

        # 分别处理两个目录
        batch_process_directory(rmb_directory, conn_main)
        batch_process_directory(usd_directory, conn_main)
        
    except cx_Oracle.Error as error:
        print(f"数据库主程序发生错误: {error}")
    except Exception as e:
        print(f"批量处理期间发生未知错误: {e}")
    finally:
        if conn_main:
            conn_main.close()
            print("\n---[ 处理结束 ]---")
            print("主数据库连接已关闭。") 