-- 海关统计数据表建表语句（新结构）
-- 删除现有表（如果存在）
DROP TABLE T_STATISTICAL_CUS_TOTAL_CS CASCADE CONSTRAINTS;

-- 创建新表
CREATE TABLE T_STATISTICAL_CUS_TOTAL_CS (
    STAT_DATE VARCHAR2(50),  -- 统计月份
    STAT_TYPE VARCHAR2(20),  -- 统计类型代码
    STAT_NAME VARCHAR2(50),  -- 统计类型名称
    STAT_CODE VARCHAR2(50),  -- 统计代码
    STAT_CONTENT_RAW VARCHAR2(500),  -- 统计类容（原用）
    STAT_CONTENT_CLEANSE VARCHAR2(500),  -- 统计类容（清洗）
    ACC_A_CNY_AMOUNT NUMBER(19,4),  -- 累计进出口人民币（深圳亿元，全国万元）
    ACC_A_CNY_YOY NUMBER(19,4),  -- 累计进出口人民币同比（%）
    ACC_A_USD_AMOUNT NUMBER(19,4),  -- 累计进出口美元（深圳亿元，全国万元）
    ACC_A_USD_YOY NUMBER(19,4),  -- 累计进出口美元同比（%）
    MON_A_CNY_AMOUNT NUMBER(19,4),  -- 当月进出口人民币（深圳亿元，全国万元）
    MON_A_CNY_YOY NUMBER(19,4),  -- 当月进出口人民币同比（%）
    MON_A_USD_AMOUNT NUMBER(19,4),  -- 当月进出口美元（深圳亿元，全国万元）
    MON_A_USD_YOY NUMBER(19,4),  -- 当月进出口美元同比（%）
    ACC_E_CNY_AMOUNT NUMBER(19,4),  -- 累计出口人民币（深圳亿元，全国万元）
    ACC_E_CNY_YOY NUMBER(19,4),  -- 累计出口人民币同比（%）
    ACC_E_USD_AMOUNT NUMBER(19,4),  -- 累计出口美元（深圳亿元，全国万元）
    ACC_E_USD_YOY NUMBER(19,4),  -- 累计出口美元同比（%）
    MON_E_CNY_AMOUNT NUMBER(19,4),  -- 当月出口人民币（深圳亿元，全国万元）
    MON_E_CNY_YOY NUMBER(19,4),  -- 当月出口人民币同比（%）
    MON_E_USD_AMOUNT NUMBER(19,4),  -- 当月出口美元（深圳亿元，全国万元）
    MON_E_USD_YOY NUMBER(19,4),  -- 当月出口美元同比（%）
    ACC_I_CNY_AMOUNT NUMBER(19,4),  -- 累计进口人民币（深圳亿元，全国万元）
    ACC_I_CNY_YOY NUMBER(19,4),  -- 累计进口人民币同比（%）
    ACC_I_USD_AMOUNT NUMBER(19,4),  -- 累计进口美元（深圳亿元，全国万元）
    ACC_I_USD_YOY NUMBER(19,4),  -- 累计进口美元同比（%）
    MON_I_CNY_AMOUNT NUMBER(19,4),  -- 当月进口人民币（深圳亿元，全国万元）
    MON_I_CNY_YOY NUMBER(19,4),  -- 当月进口人民币同比（%）
    MON_I_USD_AMOUNT NUMBER(19,4),  -- 当月进口美元（深圳亿元，全国万元）
    MON_I_USD_YOY NUMBER(19,4),  -- 当月进口美元同比（%）
    ACC_E_AMOUNT NUMBER(19,4),  -- 累计出口数量
    ACC_E_AMOUNT_UNIT VARCHAR2(20),  -- 累计出口数量单位
    ACC_E_AMOUNT_YOY NUMBER(19,4),  -- 累计出口数量同比
    MON_E_AMOUNT NUMBER(19,4),  -- 当月出口数量
    MON_E_AMOUNT_UNIT VARCHAR2(20),  -- 当月出口数量单位
    MON_E_AMOUNT_YOY NUMBER(19,4),  -- 当月出口数量同比
    ACC_I_AMOUNT NUMBER(19,4),  -- 累计进口数量
    ACC_I_AMOUNT_UNIT VARCHAR2(20),  -- 累计进口数量单位
    ACC_I_AMOUNT_YOY NUMBER(19,4),  -- 累计进口数量同比
    MON_I_AMOUNT NUMBER(19,4),  -- 当月进口数量
    MON_I_AMOUNT_UNIT VARCHAR2(20),  -- 当月进口数量单位
    MON_I_AMOUNT_YOY NUMBER(19,4),  -- 当月进口数量同比
    RANK_MARKERS VARCHAR2(10),  -- 是否参与排序标识
    DATA_SOURCE VARCHAR2(10),  -- 数据来源
    EMPHASIS_OR_EMERGING_MARK VARCHAR2(10),  -- 新兴市场/重点商品标识
    CREATE_TIME VARCHAR2(50)  -- 数据创建时间
);

-- 添加表注释
COMMENT ON TABLE T_STATISTICAL_CUS_TOTAL_CS IS '海关进出口统计数据表';

-- 添加列注释
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.STAT_DATE IS '统计月份';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.STAT_TYPE IS '统计类型代码;01:贸易方式,02:企业性质,03:国别地区,04:主出商品,05:主进商品,06:深加工结转,07:异地报关,08:特殊监管区';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.STAT_NAME IS '统计类型名称';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.STAT_CODE IS '统计代码';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.STAT_CONTENT_RAW IS '统计类容（原用）';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.STAT_CONTENT_CLEANSE IS '统计类容（清洗）';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.ACC_A_CNY_AMOUNT IS '累计进出口人民币（深圳亿元，全国万元）';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.ACC_A_CNY_YOY IS '累计进出口人民币同比（%）';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.ACC_A_USD_AMOUNT IS '累计进出口美元（深圳亿元，全国万元）';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.ACC_A_USD_YOY IS '累计进出口美元同比（%）';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.MON_A_CNY_AMOUNT IS '当月进出口人民币（深圳亿元，全国万元）';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.MON_A_CNY_YOY IS '当月进出口人民币同比（%）';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.MON_A_USD_AMOUNT IS '当月进出口美元（深圳亿元，全国万元）';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.MON_A_USD_YOY IS '当月进出口美元同比（%）';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.ACC_E_CNY_AMOUNT IS '累计出口人民币（深圳亿元，全国万元）';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.ACC_E_CNY_YOY IS '累计出口人民币同比（%）';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.ACC_E_USD_AMOUNT IS '累计出口美元（深圳亿元，全国万元）';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.ACC_E_USD_YOY IS '累计出口美元同比（%）';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.MON_E_CNY_AMOUNT IS '当月出口人民币（深圳亿元，全国万元）';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.MON_E_CNY_YOY IS '当月出口人民币同比（%）';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.MON_E_USD_AMOUNT IS '当月出口美元（深圳亿元，全国万元）';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.MON_E_USD_YOY IS '当月出口美元同比（%）';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.ACC_I_CNY_AMOUNT IS '累计进口人民币（深圳亿元，全国万元）';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.ACC_I_CNY_YOY IS '累计进口人民币同比（%）';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.ACC_I_USD_AMOUNT IS '累计进口美元（深圳亿元，全国万元）';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.ACC_I_USD_YOY IS '累计进口美元同比（%）';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.MON_I_CNY_AMOUNT IS '当月进口人民币（深圳亿元，全国万元）';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.MON_I_CNY_YOY IS '当月进口人民币同比（%）';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.MON_I_USD_AMOUNT IS '当月进口美元（深圳亿元，全国万元）';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.MON_I_USD_YOY IS '当月进口美元同比（%）';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.ACC_E_AMOUNT IS '累计出口数量';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.ACC_E_AMOUNT_UNIT IS '累计出口数量单位';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.ACC_E_AMOUNT_YOY IS '累计出口数量同比';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.MON_E_AMOUNT IS '当月出口数量';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.MON_E_AMOUNT_UNIT IS '当月出口数量单位';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.MON_E_AMOUNT_YOY IS '当月出口数量同比';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.ACC_I_AMOUNT IS '累计进口数量';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.ACC_I_AMOUNT_UNIT IS '累计进口数量单位';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.ACC_I_AMOUNT_YOY IS '累计进口数量同比';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.MON_I_AMOUNT IS '当月进口数量';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.MON_I_AMOUNT_UNIT IS '当月进口数量单位';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.MON_I_AMOUNT_YOY IS '当月进口数量同比';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.RANK_MARKERS IS '是否参与排序标识;01:参与排名,02:不参与排名';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.DATA_SOURCE IS '数据来源;01:全国,02:北京,03:上海,04:广州,05:深圳,06:江苏,07:浙江';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.EMPHASIS_OR_EMERGING_MARK IS '01:新兴市场,02:三类重点商品';
COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.CREATE_TIME IS '数据创建时间';

-- 创建索引
CREATE INDEX IDX_CUS_STAT_DATE ON T_STATISTICAL_CUS_TOTAL_CS(STAT_DATE);
CREATE INDEX IDX_CUS_STAT_TYPE ON T_STATISTICAL_CUS_TOTAL_CS(STAT_TYPE);
CREATE INDEX IDX_CUS_DATA_SOURCE ON T_STATISTICAL_CUS_TOTAL_CS(DATA_SOURCE);
CREATE INDEX IDX_CUS_STAT_CONTENT ON T_STATISTICAL_CUS_TOTAL_CS(STAT_CONTENT_CLEANSE);