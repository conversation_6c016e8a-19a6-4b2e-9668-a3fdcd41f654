import pandas as pd
import os
import re
import cx_Oracle
from datetime import datetime
import glob

# 设置pandas显示选项，确保数据完整显示
pd.set_option('display.max_columns', None)
pd.set_option('display.width', 1000)
pd.set_option('display.max_colwidth', None)

# 函数：处理值为"-"的情况
def convert_to_float_or_none(value):
    if pd.isna(value):
        return None
    if isinstance(value, str):
        # 去除千分位逗号
        value = value.replace(',', '')
        if value.strip() == '-':
            return None
    try:
        return float(value)
    except (ValueError, TypeError):
        return None

def process_trade_statistics(file_path, conn, cursor):
    """处理单个贸易统计文件，并将数据插入到数据库"""
    print(f"\n处理文件: {file_path}")
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return False
    
    try:
        # 读取XLS文件，只读取B列到K列（索引1-10）
        original_df = pd.read_excel(file_path, usecols=range(1, 11))
        
        # 提取当前月份信息
        # 先读取原始Excel获取标题信息
        title_df = pd.read_excel(file_path, usecols=[1], nrows=1)
        title_text = title_df.iloc[0, 0] if not title_df.empty and not title_df.iloc[0, 0] is None else ""
        
        print(f"标题文本: {title_text}")
        
        # 从标题中提取年月信息
        current_month = ""
        if isinstance(title_text, str):
            year_month_match = re.search(r'(\d{4})年(\d{1,2})月', title_text)
            if year_month_match:
                year = year_month_match.group(1)
                month = year_month_match.group(2).zfill(2)
                current_month = f"{year}{month}01"  # 设置为当月第一天
                print(f"提取的年月: {current_month}")
            else:
                # 尝试从文件名提取年月
                filename = os.path.basename(file_path)
                year_month_match = re.search(r'(\d{4})年(\d{1,2})月', filename)
                if year_month_match:
                    year = year_month_match.group(1)
                    month = year_month_match.group(2).zfill(2)
                    current_month = f"{year}{month}01"
                    print(f"从文件名提取的年月: {current_month}")
                else:
                    print("无法从标题或文件名中提取年月信息")
                    # 使用文件修改时间
                    file_time = os.path.getmtime(file_path)
                    dt = datetime.fromtimestamp(file_time)
                    current_month = f"{dt.year}{dt.month:02d}01"
                    print(f"使用文件修改时间作为年月: {current_month}")
        else:
            print("标题不是文本格式")
        
        # 显示原始总行数
        print(f"原始总行数: {len(original_df)}")
        
        # 查找以"总值"开头的行
        first_col = original_df.columns[0]
        total_value_idx = -1
        
        for i, row in original_df.iterrows():
            if isinstance(row[first_col], str) and row[first_col].strip() == "总值":
                total_value_idx = i
                break
        
        if total_value_idx != -1:
            print(f"找到'总值'行，索引为: {total_value_idx}")
            
            # 创建新的数据框，从'总值'行开始
            df = original_df.iloc[total_value_idx:].reset_index(drop=True)
            
            # 检查最后一行的第一个值是否以"注"开头
            last_row = df.iloc[-1]
            
            if isinstance(last_row[first_col], str) and last_row[first_col].strip().startswith("注"):
                print(f"删除最后一行，因为它以'注'开头: {last_row[first_col]}")
                # 删除最后一行
                df = df.iloc[:-1]
            
            # 设置列名
            df.columns = [
                "收发货人所在地",
                "当月进出口金额",
                "累计进出口金额",
                "当月出口金额",
                "累计出口金额",
                "当月进口金额",
                "累计进口金额",
                "累计比去年同期±%进出口",
                "累计比去年同期±%出口",
                "累计比去年同期±%进口"
            ]
            
            # 添加当前月份列
            df['当前月份'] = current_month
            
            print(f"处理后的数据框行数: {len(df)}")
            
            # 删除重复记录和空白行
            df = df.dropna(subset=['收发货人所在地'])
            df = df[~df['收发货人所在地'].str.contains("总值汇总", na=False)]
            
            # 准备插入数据
            try:
                # 准备数据
                data_to_insert = []
                insert_count = 0
                
                for _, row in df.iterrows():
                    # 跳过空行或不适合插入的行
                    if pd.isna(row['收发货人所在地']) or row['收发货人所在地'] == '':
                        continue
                    
                    # 将空值转换为None，将数值类型转换为float，处理"-"值
                    location = str(row['收发货人所在地']).strip() if not pd.isna(row['收发货人所在地']) else None
                    month_import_export = convert_to_float_or_none(row['当月进出口金额'])
                    month_export = convert_to_float_or_none(row['当月出口金额'])
                    month_import = convert_to_float_or_none(row['当月进口金额'])
                    ytd_import_export = convert_to_float_or_none(row['累计进出口金额'])
                    ytd_export = convert_to_float_or_none(row['累计出口金额'])
                    ytd_import = convert_to_float_or_none(row['累计进口金额'])
                    yoy_import_export = convert_to_float_or_none(row['累计比去年同期±%进出口'])
                    yoy_export = convert_to_float_or_none(row['累计比去年同期±%出口'])
                    yoy_import = convert_to_float_or_none(row['累计比去年同期±%进口'])
                    
                    # 日期格式
                    month_date = str(row['当前月份']) if not pd.isna(row['当前月份']) else None
                    
                    data_to_insert.append((
                        location, 
                        month_import_export, 
                        month_export, 
                        month_import, 
                        ytd_import_export, 
                        ytd_export, 
                        ytd_import, 
                        yoy_import_export, 
                        yoy_export, 
                        yoy_import, 
                        month_date
                    ))
                
                # 执行批量插入
                if data_to_insert:
                    insert_query = """
                    INSERT INTO TEMP_TRADE_STATISTICS (
                        LOCATION, 
                        MONTH_IMPORT_EXPORT, 
                        MONTH_EXPORT, 
                        MONTH_IMPORT, 
                        YTD_IMPORT_EXPORT, 
                        YTD_EXPORT, 
                        YTD_IMPORT, 
                        YOY_IMPORT_EXPORT, 
                        YOY_EXPORT, 
                        YOY_IMPORT, 
                        CURRENT_MONTH
                    ) VALUES (
                        :1, :2, :3, :4, :5, :6, :7, :8, :9, :10, TO_DATE(:11, 'YYYYMMDD')
                    )
                    """
                    
                    print(f"准备插入 {len(data_to_insert)} 条数据...")
                    cursor.executemany(insert_query, data_to_insert)
                    conn.commit()
                    insert_count = len(data_to_insert)
                    print(f"成功插入 {insert_count} 条数据到Oracle数据库")
                    
                return insert_count
            except cx_Oracle.Error as error:
                print(f"Oracle数据库错误: {error}")
                return 0
            except Exception as e:
                print(f"插入数据时发生错误: {e}")
                return 0
        else:
            print("未找到'总值'行")
            return 0
        
    except Exception as e:
        print(f"读取文件时发生错误: {e}")
        return 0

def batch_process_files(directory_path):
    """批量处理目录下的所有XLS文件"""
    print(f"开始处理目录: {directory_path}")
    
    # 验证目录是否存在
    if not os.path.exists(directory_path):
        print(f"错误: 目录不存在 - {directory_path}")
        return
    
    # 查找所有XLS文件
    xls_files = glob.glob(os.path.join(directory_path, '**', '*.xls'), recursive=True)
    xlsx_files = glob.glob(os.path.join(directory_path, '**', '*.xlsx'), recursive=True)
    all_files = xls_files + xlsx_files
    
    if not all_files:
        print(f"未在 {directory_path} 目录下找到任何XLS或XLSX文件")
        return
    
    print(f"找到 {len(all_files)} 个Excel文件")
    
    try:
        # 连接Oracle数据库
        print("\n正在连接到Oracle数据库...")
        conn = cx_Oracle.connect("manifest_dcb/manifest_dcb@192.168.1.151/TEST")
        cursor = conn.cursor()
        print("数据库连接成功")
        
        # 处理文件并记录结果
        total_files = len(all_files)
        success_files = 0
        total_records = 0
        
        for i, file_path in enumerate(all_files, 1):
            print(f"\n处理文件 [{i}/{total_files}]: {file_path}")
            records_inserted = process_trade_statistics(file_path, conn, cursor)
            
            if records_inserted > 0:
                success_files += 1
                total_records += records_inserted
        
        # 输出总结
        print("\n==== 处理完成 ====")
        print(f"共处理: {total_files} 文件")
        print(f"成功处理: {success_files} 文件")
        print(f"失败处理: {total_files - success_files} 文件")
        print(f"成功插入: {total_records} 条记录")
        
        # 关闭数据库连接
        cursor.close()
        conn.close()
        print("数据库连接已关闭")
        
    except cx_Oracle.Error as error:
        print(f"Oracle数据库连接错误: {error}")
    except Exception as e:
        print(f"发生错误: {e}")

if __name__ == "__main__":
    while True:
        print("\n=== 人民币数据批量入库工具 ===")
        print("请输入要处理的目录路径(包含XLS文件的目录)，或输入exit退出:")
        directory_path = input("目录路径: ").strip()
        
        if directory_path.lower() == 'exit':
            print("程序结束，再见!")
            break
        
        if os.path.exists(directory_path):
            batch_process_files(directory_path)
        else:
            print(f"错误: 目录不存在 - {directory_path}") 