import pandas as pd
import cx_Oracle
import os
import re
import sys
from tqdm import tqdm
from pathlib import Path
import numpy as np

def get_db_connection():
    """建立并返回数据库连接"""
    try:
        # 请替换为您的数据库连接信息
        connection = cx_Oracle.connect("manifest_dcb/manifest_dcb@*************/TEST")
        return connection
    except Exception as e:
        print(f"数据库连接失败: {e}")
        sys.exit(1)

def get_unit_from_dataframe(df):
    """从DataFrame中动态查找单位信息"""
    for _, row in df.head(5).iterrows():
        for cell in row:
            if isinstance(cell, str) and '单位：' in cell:
                return cell.split('：')[1].strip()
    return "不明"

def parse_excel_file(file_path):
    """
    解析表18的Excel文件。
    该文件结构与表17相同，包含多层表头和分组数据。
    """
    filename = os.path.basename(file_path)
    date_match = re.search(r'(\d{4})年1至(\d{1,2})月', filename)
    if not date_match:
        # 兼容 "2024年9月" 这样的格式
        date_match = re.search(r'(\d{4})年(\d{1,2})月', filename)
    if not date_match:
        print(f"警告: 无法从文件名 '{filename}' 中提取日期。")
        return None
        
    year, month = date_match.groups()
    current_month_str = f"{year}-{int(month):02d}-01"
    currency_type = "人民币" if "人民币" in filename else "美元"

    try:
        df_raw = pd.read_excel(file_path, header=None, engine=None)
    except Exception as e:
        print(f"错误: 使用pandas读取文件失败: {file_path}, 错误: {e}")
        return None

    unit = get_unit_from_dataframe(df_raw)
    
    header_row_idx = 3
    data_start_row_idx = 5
    
    trade_modes_header = df_raw.iloc[header_row_idx].values
    
    trade_mode_map = {}
    current_trade_mode = ""
    for i, v in enumerate(trade_modes_header):
        if pd.notna(v) and '贸易' in str(v):
            # 清理 "贸易方式\n   商品"
            cleaned_v = str(v).split('\n')[0].strip()
            if cleaned_v:
                current_trade_mode = cleaned_v
        if i >= 3 and (i - 3) % 2 == 0:
            trade_mode_map[i] = current_trade_mode

    df_data = df_raw.iloc[data_start_row_idx:]
    processed_data = []

    for i in range(0, len(df_data), 2):
        if i + 1 >= len(df_data):
            continue
            
        value_row = df_data.iloc[i]
        yoy_row = df_data.iloc[i + 1]

        product_name = value_row.iloc[1]
        if pd.isna(product_name) or not str(product_name).strip() or '计' in str(product_name) or '总' in str(product_name):
            continue
            
        measure_unit = str(value_row.iloc[2]).strip() if pd.notna(value_row.iloc[2]) else None

        for col_idx, trade_mode in trade_mode_map.items():
            if not trade_mode: continue

            quantity = pd.to_numeric(value_row.iloc[col_idx], errors='coerce')
            amount = pd.to_numeric(value_row.iloc[col_idx + 1], errors='coerce')
            quantity_yoy = pd.to_numeric(yoy_row.iloc[col_idx], errors='coerce')
            amount_yoy = pd.to_numeric(yoy_row.iloc[col_idx + 1], errors='coerce')

            if pd.notna(quantity) or pd.notna(amount):
                processed_data.append({
                    "product_name": str(product_name).strip(),
                    "measure_unit": measure_unit,
                    "trade_mode": trade_mode,
                    "quantity": quantity,
                    "amount": amount,
                    "quantity_yoy": quantity_yoy,
                    "amount_yoy": amount_yoy
                })

    if not processed_data:
        return None
        
    df = pd.DataFrame(processed_data)
    df['current_month'] = pd.to_datetime(current_month_str)
    df['currency_type'] = currency_type
    df['unit'] = unit
    return df

def upsert_data(connection, df_to_process):
    """
    使用 MERGE 语句将数据插入或更新到数据库。
    """
    if df_to_process is None or df_to_process.empty:
        print("    -> 无数据传入，跳过数据库操作。")
        return 0

    cursor = connection.cursor()
    
    df_to_process.replace({np.nan: None}, inplace=True)
    data_to_merge = df_to_process.to_dict('records')

    merge_sql = """
    MERGE INTO temp_cus_mon_18 dest
    USING (
        SELECT
            :current_month AS current_month,
            :currency_type AS currency_type,
            :product_name AS product_name,
            :trade_mode AS trade_mode
        FROM dual
    ) src ON (
        dest.current_month = src.current_month AND
        dest.currency_type = src.currency_type AND
        dest.product_name = src.product_name AND
        dest.trade_mode = src.trade_mode
    )
    WHEN MATCHED THEN
        UPDATE SET
            dest.unit = :unit,
            dest.measure_unit = :measure_unit,
            dest.quantity = :quantity,
            dest.amount = :amount,
            dest.quantity_yoy = :quantity_yoy,
            dest.amount_yoy = :amount_yoy,
            dest.created_at = CURRENT_TIMESTAMP
    WHEN NOT MATCHED THEN
        INSERT (
            current_month, currency_type, unit, product_name, 
            measure_unit, trade_mode, quantity, amount, 
            quantity_yoy, amount_yoy
        ) VALUES (
            :current_month, :currency_type, :unit, :product_name, 
            :measure_unit, :trade_mode, :quantity, :amount, 
            :quantity_yoy, :amount_yoy
        )
    """
    
    try:
        cursor.executemany(merge_sql, data_to_merge, batcherrors=True)
        error_count = len(cursor.getbatcherrors())
        merged_count = cursor.rowcount
        
        if error_count > 0:
            print(f"    [!] 批量合并时发生 {error_count} 个错误。")

        connection.commit()
        print(f"    -> 成功合并 (插入/更新) {merged_count} 条记录。")
        return merged_count
    except Exception as e:
        print(f"    [!] 执行批量合并时发生严重错误: {e}")
        connection.rollback()
        return 0
    finally:
        cursor.close()

def process_directory(directory_path, connection):
    """处理指定目录下的所有Excel文件"""
    base_dir = Path(directory_path)
    if not base_dir.exists():
        print(f"错误: 数据目录不存在 -> {base_dir}，跳过处理。")
        return 0

    files_to_process = sorted(list(base_dir.rglob('*.xlsx')))
    if not files_to_process:
        print(f"在目录 {base_dir.name} 中未找到任何 .xlsx 文件。")
        return 0
    
    print(f"\n--- 开始处理目录: {base_dir.name} ---")
    print(f"发现 {len(files_to_process)} 个文件待处理。")
    
    total_merged_in_dir = 0

    for file_path in tqdm(files_to_process, desc=f"处理 {base_dir.name}"):
        if file_path.name.startswith('~'):
            continue
        print(f"\n  -- 处理文件: {file_path.name} --")
        df = parse_excel_file(file_path)
        if df is not None and not df.empty:
            merged_count = upsert_data(connection, df)
            total_merged_in_dir += merged_count
        else:
            print("    -> 解析完成，无数据入库。")
            
    return total_merged_in_dir

def main():
    """主执行函数，自动处理人民币和美元两个目录"""
    # 使用 os.getcwd() 动态获取当前工作目录
    base_dir = os.getcwd()
    rmb_dir = os.path.join(base_dir, '进出口商品统计表_人民币值', '(18)部分进口商品主要贸易方式量值表_人民币值')
    usd_dir = os.path.join(base_dir, '进出口商品统计表_美元值', '(18)部分进口商品主要贸易方式量值表_美元值')

    connection = None
    grand_total_merged = 0
    
    try:
        connection = get_db_connection()
        print("数据库连接成功。")
        
        rmb_merged = process_directory(rmb_dir, connection)
        grand_total_merged += rmb_merged
        
        usd_merged = process_directory(usd_dir, connection)
        grand_total_merged += usd_merged

        print("\n--- 所有目录处理完毕 ---")
        print(f"总计合并 (插入/更新) 了 {grand_total_merged} 条记录。")

    except Exception as e:
        print(f"\n处理过程中发生未预料的错误: {e}")
    finally:
        if connection:
            connection.close()
            print("数据库连接已关闭。")

if __name__ == "__main__":
    main() 