from DrissionPage import ChromiumPage
import time
import os
import re

def save_page_source():
    """保存北京海关网页源码进行分析"""

    # 创建分析目录
    analysis_dir = "北京海关页面分析"
    if not os.path.exists(analysis_dir):
        os.makedirs(analysis_dir)

    page = ChromiumPage(timeout=30)

    try:
        # 访问北京海关主页
        beijing_url = "http://beijing.customs.gov.cn/beijing_customs/434756/434804/2941702/434773/434774/index.html"
        print(f"正在访问: {beijing_url}")

        page.get(beijing_url)
        time.sleep(5)

        print(f"页面标题: {page.title}")

        # 保存主页源码
        main_page_source = page.html
        with open(os.path.join(analysis_dir, "主页源码.html"), 'w', encoding='utf-8') as f:
            f.write(main_page_source)
        print("主页源码已保存")

        # 查找所有链接并保存信息
        all_links = page.eles('tag:a')
        links_info = []

        print(f"找到 {len(all_links)} 个链接")

        for i, link in enumerate(all_links):
            link_text = link.text.strip()
            href = link.attr('href')

            if href and link_text:
                # 构建完整URL
                if href.startswith('/'):
                    full_url = "http://beijing.customs.gov.cn" + href
                elif not href.startswith('http'):
                    current_url = page.url
                    base_path = '/'.join(current_url.split('/')[:-1]) + '/'
                    full_url = base_path + href
                else:
                    full_url = href

                links_info.append({
                    'index': i,
                    'text': link_text,
                    'href': href,
                    'full_url': full_url
                })

        # 保存所有链接信息
        with open(os.path.join(analysis_dir, "所有链接信息.txt"), 'w', encoding='utf-8') as f:
            f.write("序号\t链接文本\t原始href\t完整URL\n")
            for link in links_info:
                f.write(f"{link['index']}\t{link['text']}\t{link['href']}\t{link['full_url']}\n")

        print(f"所有链接信息已保存，共 {len(links_info)} 个链接")

        # 筛选包含2024或2025年的链接
        target_year_links = []
        for link in links_info:
            if any(year in link['text'] for year in ['2024', '2025']):
                target_year_links.append(link)

        print(f"找到 {len(target_year_links)} 个包含2024/2025年的链接")

        # 保存目标年份链接
        with open(os.path.join(analysis_dir, "目标年份链接.txt"), 'w', encoding='utf-8') as f:
            f.write("序号\t链接文本\t完整URL\n")
            for link in target_year_links:
                f.write(f"{link['index']}\t{link['text']}\t{link['full_url']}\n")

        # 访问每个目标链接并保存页面源码
        for i, link in enumerate(target_year_links[:10]):  # 只处理前10个，避免太多
            try:
                print(f"\n访问第 {i+1} 个目标链接: {link['text'][:50]}...")
                page.get(link['full_url'])
                time.sleep(3)

                # 保存页面源码
                page_source = page.html
                safe_filename = re.sub(r'[\\/*?:"<>|]', '_', link['text'][:50])
                filename = f"页面_{i+1}_{safe_filename}.html"

                with open(os.path.join(analysis_dir, filename), 'w', encoding='utf-8') as f:
                    f.write(page_source)

                print(f"页面源码已保存: {filename}")

                # 分析页面中的下载链接和表格
                download_elements = page.eles("下载")
                table_elements = page.eles('tag:table')

                analysis_text = f"""页面分析报告
============
URL: {link['full_url']}
标题: {page.title}
链接文本: {link['text']}

下载按钮数量: {len(download_elements)}
表格数量: {len(table_elements)}

下载按钮信息:
"""

                for j, btn in enumerate(download_elements):
                    try:
                        btn_text = btn.text.strip()
                        btn_href = btn.attr('href') if btn.tag == 'a' else 'N/A'
                        btn_tag = btn.tag
                        btn_class = btn.attr('class') or 'N/A'
                        btn_id = btn.attr('id') or 'N/A'

                        analysis_text += f"  按钮 {j+1}: 标签={btn_tag}, 文本='{btn_text}', href='{btn_href}', class='{btn_class}', id='{btn_id}'\n"
                    except Exception as e:
                        analysis_text += f"  按钮 {j+1}: 分析出错 - {e}\n"

                analysis_text += f"\n表格信息:\n"
                for j, table in enumerate(table_elements):
                    try:
                        rows = table.eles('tag:tr')
                        analysis_text += f"  表格 {j+1}: {len(rows)} 行\n"
                    except Exception as e:
                        analysis_text += f"  表格 {j+1}: 分析出错 - {e}\n"

                # 保存分析报告
                analysis_filename = f"分析报告_{i+1}_{safe_filename}.txt"
                with open(os.path.join(analysis_dir, analysis_filename), 'w', encoding='utf-8') as f:
                    f.write(analysis_text)

            except Exception as e:
                print(f"访问链接时出错: {e}")
                continue

        print(f"\n=== 分析完成 ===")
        print(f"所有文件已保存到目录: {analysis_dir}")

    except Exception as e:
        print(f"主程序出错: {e}")
        import traceback
        traceback.print_exc()

    finally:
        page.quit()

if __name__ == "__main__":
    save_page_source()