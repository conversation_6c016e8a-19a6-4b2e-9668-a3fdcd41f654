import pandas as pd
import numpy as np
import datetime
import os

def transform_beijing_data(input_folder, output_csv_path):
    """
    读取北京海关数据Excel文件，将其转换为符合Oracle表结构的CSV文件。
    完全匹配T_STATISTICAL_CUS_TOTAL表的43个字段。
    """
    try:
        # 北京数据文件列表
        files = [
            "（1）北京地区进出口商品总值表B：月度表（2025年1-5月）.xls",
            "（2）北京地区进出口商品国别（地区）总值表（2025年1-5月）.xls",
            # "（5）北京地区进出口商品贸易方式总值表（2025年1-5月）.xls", # 暂时移除，避免与（1）重复
            "（6）北京地区出口商品贸易方式企业性质总值表（2025年1-5月）.xls",
            "（7）北京地区进口商品贸易方式企业性质总值表（2025年1-5月）.xls",
            "（8）北京地区出口主要商品量值表（2025年1-5月）.xls",
            "（9）北京地区进口主要商品量值表（2025年1-5月）.xls"
        ]
        
        output_dfs = []
        
        # 目标CSV文件的列名（完全匹配Oracle表结构）
        output_columns = [
            "STAT_DATE", "STAT_TYPE", "STAT_NAME", "STAT_CODE", "STAT_CONTENT_RAW",
            "STAT_CONTENT_CLEANSE", "ACC_A_CNY_AMOUNT", "ACC_A_CNY_YOY", "ACC_A_USD_AMOUNT",
            "ACC_A_USD_YOY", "MON_A_CNY_AMOUNT", "MON_A_CNY_YOY", "MON_A_USD_AMOUNT",
            "MON_A_USD_YOY", "ACC_E_CNY_AMOUNT", "ACC_E_CNY_YOY", "ACC_E_USD_AMOUNT",
            "ACC_E_USD_YOY", "MON_E_CNY_AMOUNT", "MON_E_CNY_YOY", "MON_E_USD_AMOUNT",
            "MON_E_USD_YOY", "ACC_I_CNY_AMOUNT", "ACC_I_CNY_YOY", "ACC_I_USD_AMOUNT",
            "ACC_I_USD_YOY", "MON_I_CNY_AMOUNT", "MON_I_CNY_YOY", "MON_I_USD_AMOUNT",
            "MON_I_USD_YOY", "ACC_E_AMOUNT", "ACC_E_AMOUNT_UNIT", "ACC_E_AMOUNT_YOY",
            "MON_E_AMOUNT", "MON_E_AMOUNT_UNIT", "MON_E_AMOUNT_YOY", "ACC_I_AMOUNT",
            "ACC_I_AMOUNT_UNIT", "ACC_I_AMOUNT_YOY", "MON_I_AMOUNT", "MON_I_AMOUNT_UNIT",
            "MON_I_AMOUNT_YOY", "RANK_MARKERS", "DATA_SOURCE", "EMPHASIS_OR_EMERGING_MARK",
            "CREATE_TIME"
        ]
        
        # 文件到 STAT_TYPE 的映射
        file_to_stat_type = {
            "（1）北京地区进出口商品总值表B：月度表（2025年1-5月）.xls": ('01', '月度总值', '当月'),
            "（2）北京地区进出口商品国别（地区）总值表（2025年1-5月）.xls": ('03', '国别地区', '当月'),
            "（5）北京地区进出口商品贸易方式总值表（2025年1-5月）.xls": ('01', '贸易方式', '当月'), # STAT_TYPE '01'，但内容是贸易方式
            "（6）北京地区出口商品贸易方式企业性质总值表（2025年1-5月）.xls": ('02', '企业性质', '当月'),
            "（7）北京地区进口商品贸易方式企业性质总值表（2025年1-5月）.xls": ('02', '企业性质', '当月'),
            "（8）北京地区出口主要商品量值表（2025年1-5月）.xls": ('04', '主出商品', '当月'),
            "（9）北京地区进口主要商品量值表（2025年1-5月）.xls": ('05', '主进商品', '当月')
        }
        
        # 单独处理文件6和7的合并
        df6_path = os.path.join(input_folder, "（6）北京地区出口商品贸易方式企业性质总值表（2025年1-5月）.xls")
        df7_path = os.path.join(input_folder, "（7）北京地区进口商品贸易方式企业性质总值表（2025年1-5月）.xls")

        if os.path.exists(df6_path) and os.path.exists(df7_path):
            try:
                df6 = pd.read_excel(df6_path, header=None)
                df7 = pd.read_excel(df7_path, header=None)

                # --- 处理文件6 (出口) ---
                start_row_6 = 0
                for i, row in df6.iterrows():
                    if '项目' in str(row.iloc[0]):
                        start_row_6 = i
                        break
                header_6 = df6.iloc[start_row_6].tolist()
                data_df6 = df6.iloc[start_row_6 + 1:].copy()
                data_df6.columns = header_6
                project_col_6 = header_6[0]
                data_df6 = data_df6[data_df6[project_col_6].notna()]
                data_df6 = data_df6[~data_df6[project_col_6].astype(str).str.contains('单位|合计|总计|nan', case=False, na=False)]
                data_df6.rename(columns={project_col_6: '项目'}, inplace=True)
                df6_export = data_df6.iloc[:, [0, 1, 2]].copy()
                df6_export.columns = ['项目', 'ACC_E_CNY_AMOUNT', 'ACC_E_CNY_YOY']
                df6_export['项目'] = df6_export['项目'].astype(str).str.strip()

                # --- 处理文件7 (进口) ---
                start_row_7 = 0
                for i, row in df7.iterrows():
                    if '项目' in str(row.iloc[0]):
                        start_row_7 = i
                        break
                header_7 = df7.iloc[start_row_7].tolist()
                data_df7 = df7.iloc[start_row_7 + 1:].copy()
                data_df7.columns = header_7
                project_col_7 = header_7[0]
                data_df7 = data_df7[data_df7[project_col_7].notna()]
                data_df7 = data_df7[~data_df7[project_col_7].astype(str).str.contains('单位|合计|总计|nan', case=False, na=False)]
                data_df7.rename(columns={project_col_7: '项目'}, inplace=True)
                df7_import = data_df7.iloc[:, [0, 1, 2]].copy()
                df7_import.columns = ['项目', 'ACC_I_CNY_AMOUNT', 'ACC_I_CNY_YOY']
                df7_import['项目'] = df7_import['项目'].astype(str).str.strip()

                # --- 调试输出 ---
                print("--- 文件6 (出口) 数据预览 ---")
                print(df6_export.head())
                print(f"文件6 Shape: {df6_export.shape}")
                print("--- 文件7 (进口) 数据预览 ---")
                print(df7_import.head())
                print(f"文件7 Shape: {df7_import.shape}")

                # --- 合并数据 ---
                merged_df = pd.merge(df6_export, df7_import, on='项目', how='outer')
                print("--- 合并后数据预览 ---")
                print(merged_df.head())
                print(f"合并后 Shape: {merged_df.shape}")

                temp_df = pd.DataFrame(columns=output_columns)
                temp_df['STAT_CONTENT_RAW'] = merged_df['项目']
                temp_df['STAT_CONTENT_CLEANSE'] = merged_df['项目']
                temp_df['STAT_TYPE'] = '02'
                temp_df['STAT_NAME'] = '企业性质'
                temp_df['STAT_CODE'] = ''
                temp_df['ACC_E_CNY_AMOUNT'] = pd.to_numeric(merged_df['ACC_E_CNY_AMOUNT'].astype(str).str.replace(',', ''), errors='coerce')
                temp_df['ACC_E_CNY_YOY'] = pd.to_numeric(merged_df['ACC_E_CNY_YOY'], errors='coerce')
                temp_df['ACC_I_CNY_AMOUNT'] = pd.to_numeric(merged_df['ACC_I_CNY_AMOUNT'].astype(str).str.replace(',', ''), errors='coerce')
                temp_df['ACC_I_CNY_YOY'] = pd.to_numeric(merged_df['ACC_I_CNY_YOY'], errors='coerce')
                output_dfs.append(temp_df)
                print("文件（6）和（7）合并处理完成")

            except Exception as e:
                import traceback
                print(f"处理文件（6）和（7）时出错: {e}")
                print(traceback.format_exc())

        # 处理每个文件
        for filename in files:
            if '企业性质' in filename:
                continue # 跳过已单独处理的文件
            file_path = os.path.join(input_folder, filename)
            if not os.path.exists(file_path):
                print(f"警告：文件不存在 {file_path}")
                continue
                
            try:
                # 读取Excel文件
                df = pd.read_excel(file_path, header=None)
                print(f"正在处理文件: {filename}")
                print(f"数据形状: {df.shape}")
                
                # 显示前几行以了解数据结构
                print("数据预览:")
                print(df.head(10))
                
                # 根据文件类型处理数据
                stat_type, stat_name, period_type = file_to_stat_type.get(filename, ('', '', ''))
                
                # 创建一个临时的DataFrame来存储当前文件的数据
                temp_df = pd.DataFrame(columns=output_columns)
                
                # 数据清洗和预处理
                # 找到数据开始的行
                start_row = 0
                for i, row in df.iterrows():
                    if '项目' in str(row.iloc[0]) or '商品编码' in str(row.iloc[0]):
                        start_row = i
                        break
                
                header = df.iloc[start_row].tolist()
                data_df = df.iloc[start_row + 1:].copy()
                data_df.columns = header
                print(f"Columns found for {filename}: {data_df.columns.tolist()}")

                # 过滤掉空值、单位行、总计行等
                project_col = header[0]
                data_df = data_df[data_df[project_col].notna()]
                data_df = data_df[~data_df[project_col].astype(str).str.contains('单位|合计|总计|nan', case=False, na=False)]
                data_df = data_df[data_df[project_col].astype(str).str.strip() != '']

                temp_df['STAT_CONTENT_RAW'] = data_df[project_col]
                temp_df['STAT_CONTENT_CLEANSE'] = data_df[project_col].astype(str).str.strip()
                temp_df['STAT_TYPE'] = stat_type
                temp_df['STAT_NAME'] = stat_name
                temp_df['STAT_CODE'] = ''

                # 根据文件名和数据结构提取数据
                if filename == "（1）北京地区进出口商品总值表B：月度表（2025年1-5月）.xls":
                    # 进出口总值
                    temp_df['ACC_A_CNY_AMOUNT'] = pd.to_numeric(data_df.iloc[:, 1].astype(str).str.replace(',', ''), errors='coerce') / 10000
                    temp_df['ACC_A_CNY_YOY'] = pd.to_numeric(data_df.iloc[:, 2], errors='coerce')
                    temp_df['MON_A_CNY_AMOUNT'] = pd.to_numeric(data_df.iloc[:, 3].astype(str).str.replace(',', ''), errors='coerce') / 10000
                    temp_df['MON_A_CNY_YOY'] = pd.to_numeric(data_df.iloc[:, 4], errors='coerce')
                    # 出口
                    temp_df['ACC_E_CNY_AMOUNT'] = pd.to_numeric(data_df.iloc[:, 5].astype(str).str.replace(',', ''), errors='coerce') / 10000
                    temp_df['ACC_E_CNY_YOY'] = pd.to_numeric(data_df.iloc[:, 6], errors='coerce')
                    temp_df['MON_E_CNY_AMOUNT'] = pd.to_numeric(data_df.iloc[:, 7].astype(str).str.replace(',', ''), errors='coerce') / 10000
                    temp_df['MON_E_CNY_YOY'] = pd.to_numeric(data_df.iloc[:, 8], errors='coerce')
                    # 进口
                    temp_df['ACC_I_CNY_AMOUNT'] = pd.to_numeric(data_df.iloc[:, 9].astype(str).str.replace(',', ''), errors='coerce') / 10000
                    temp_df['ACC_I_CNY_YOY'] = pd.to_numeric(data_df.iloc[:, 10], errors='coerce')
                    temp_df['MON_I_CNY_AMOUNT'] = pd.to_numeric(data_df.iloc[:, 11].astype(str).str.replace(',', ''), errors='coerce') / 10000
                    temp_df['MON_I_CNY_YOY'] = pd.to_numeric(data_df.iloc[:, 12], errors='coerce')
                    output_dfs.append(temp_df)
                elif filename in ["（2）北京地区进出口商品国别（地区）总值表（2025年1-5月）.xls", "（5）北京地区进出口商品贸易方式总值表（2025年1-5月）.xls"]:
                    # 进出口累计
                    temp_df['ACC_A_CNY_AMOUNT'] = pd.to_numeric(data_df.iloc[:, 1].astype(str).str.replace(',', ''), errors='coerce') / 10000
                    temp_df['ACC_A_CNY_YOY'] = pd.to_numeric(data_df.iloc[:, 2], errors='coerce')
                    # 进出口当月
                    temp_df['MON_A_CNY_AMOUNT'] = pd.to_numeric(data_df.iloc[:, 5].astype(str).str.replace(',', ''), errors='coerce') / 10000
                    temp_df['MON_A_CNY_YOY'] = pd.to_numeric(data_df.iloc[:, 6], errors='coerce')
                    # 出口累计
                    temp_df['ACC_E_CNY_AMOUNT'] = pd.to_numeric(data_df.iloc[:, 9].astype(str).str.replace(',', ''), errors='coerce') / 10000
                    temp_df['ACC_E_CNY_YOY'] = pd.to_numeric(data_df.iloc[:, 10], errors='coerce')
                    # 出口当月
                    temp_df['MON_E_CNY_AMOUNT'] = pd.to_numeric(data_df.iloc[:, 13].astype(str).str.replace(',', ''), errors='coerce') / 10000
                    temp_df['MON_E_CNY_YOY'] = pd.to_numeric(data_df.iloc[:, 14], errors='coerce')
                    # 进口累计
                    temp_df['ACC_I_CNY_AMOUNT'] = pd.to_numeric(data_df.iloc[:, 17].astype(str).str.replace(',', ''), errors='coerce') / 10000
                    temp_df['ACC_I_CNY_YOY'] = pd.to_numeric(data_df.iloc[:, 18], errors='coerce')
                    # 进口当月
                    temp_df['MON_I_CNY_AMOUNT'] = pd.to_numeric(data_df.iloc[:, 21].astype(str).str.replace(',', ''), errors='coerce') / 10000
                    temp_df['MON_I_CNY_YOY'] = pd.to_numeric(data_df.iloc[:, 22], errors='coerce')
                    output_dfs.append(temp_df)

                elif filename in ["（8）北京地区出口主要商品量值表（2025年1-5月）.xls"]:
                    # ACC
                    temp_df['ACC_E_AMOUNT'] = pd.to_numeric(data_df.iloc[:, 2].astype(str).str.replace(',', ''), errors='coerce')
                    temp_df['ACC_E_AMOUNT_YOY'] = pd.to_numeric(data_df.iloc[:, 3], errors='coerce')
                    temp_df['ACC_E_CNY_AMOUNT'] = pd.to_numeric(data_df.iloc[:, 4].astype(str).str.replace(',', ''), errors='coerce') / 10000
                    temp_df['ACC_E_CNY_YOY'] = pd.to_numeric(data_df.iloc[:, 5], errors='coerce')
                    # MON
                    temp_df['MON_E_AMOUNT'] = pd.to_numeric(data_df.iloc[:, 8].astype(str).str.replace(',', ''), errors='coerce')
                    temp_df['MON_E_AMOUNT_YOY'] = pd.to_numeric(data_df.iloc[:, 9], errors='coerce')
                    temp_df['MON_E_CNY_AMOUNT'] = pd.to_numeric(data_df.iloc[:, 10].astype(str).str.replace(',', ''), errors='coerce') / 10000
                    temp_df['MON_E_CNY_YOY'] = pd.to_numeric(data_df.iloc[:, 11], errors='coerce')
                    output_dfs.append(temp_df)
                elif filename in ["（9）北京地区进口主要商品量值表（2025年1-5月）.xls"]:
                    # ACC
                    temp_df['ACC_I_AMOUNT'] = pd.to_numeric(data_df.iloc[:, 2].astype(str).str.replace(',', ''), errors='coerce')
                    temp_df['ACC_I_AMOUNT_YOY'] = pd.to_numeric(data_df.iloc[:, 3], errors='coerce')
                    temp_df['ACC_I_CNY_AMOUNT'] = pd.to_numeric(data_df.iloc[:, 4].astype(str).str.replace(',', ''), errors='coerce') / 10000
                    temp_df['ACC_I_CNY_YOY'] = pd.to_numeric(data_df.iloc[:, 5], errors='coerce')
                    # MON
                    temp_df['MON_I_AMOUNT'] = pd.to_numeric(data_df.iloc[:, 8].astype(str).str.replace(',', ''), errors='coerce')
                    temp_df['MON_I_AMOUNT_YOY'] = pd.to_numeric(data_df.iloc[:, 9], errors='coerce')
                    temp_df['MON_I_CNY_AMOUNT'] = pd.to_numeric(data_df.iloc[:, 10].astype(str).str.replace(',', ''), errors='coerce') / 10000
                    temp_df['MON_I_CNY_YOY'] = pd.to_numeric(data_df.iloc[:, 11], errors='coerce')
                    output_dfs.append(temp_df)

                print(f"文件 {filename} 处理完成")
                
            except Exception as e:
                print(f"处理文件 {filename} 时出错: {e}")
                continue
                
        # 合并所有处理过的数据
        if output_dfs:
            final_df = pd.concat(output_dfs, ignore_index=True)
        else:
            print("没有成功处理任何数据")
            return

        # 填充公共字段
        final_df['STAT_DATE'] = '2025/5/1'  # 假设处理5月数据
        final_df['DATA_SOURCE'] = '01'  # 北京
        final_df['EMPHASIS_OR_EMERGING_MARK'] = ''
        final_df['RANK_MARKERS'] = ''
        final_df['CREATE_TIME'] = datetime.datetime.now().strftime('%Y/%m/%d %H:%M:%S')
        
        # 确保所有列都存在
        for col in output_columns:
            if col not in final_df.columns:
                final_df[col] = np.nan if 'AMOUNT' in col or 'YOY' in col else ''
        
        # 按照目标顺序排列列
        final_df = final_df[output_columns]
        
        # 数据清理：将空字符串替换为NaN，然后填充适当的默认值
        for col in final_df.columns:
            if 'AMOUNT' in col or 'YOY' in col:
                final_df[col] = pd.to_numeric(final_df[col], errors='coerce')
            elif 'UNIT' in col or 'CODE' in col or 'MARKERS' in col or 'MARK' in col:
                final_df[col] = final_df[col].fillna('').astype(str)
        
        # 将处理好的数据保存为CSV文件
        final_df.to_csv(output_csv_path, index=False, encoding='utf-8-sig', float_format='%.4f')
        print(f"数据已成功转换并保存到 {output_csv_path}")
        print(f"总记录数: {len(final_df)}")
        print(f"字段数: {len(final_df.columns)}")
        
    except Exception as e:
        print(f"处理北京数据时发生错误: {e}")


if __name__ == '__main__':
    input_folder = '北京单月的'
    csv_file = 'T_STATISTICAL_CUS_TOTAL_BEIJING.csv'
    transform_beijing_data(input_folder, csv_file)