import cx_Oracle as oracle
import pandas as pd
import logging
import os
import sys
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    filename='data_sync.log'
)
logger = logging.getLogger('data_sync')

# 数据库配置
DB_CONFIG = {
    'TEST': {
        'username': 'manifest',
        'password': 'manifest',
        'host': 'ip',
        'port': '1522',
        'service_name': 'TEST'
    },
    'PROD': {
        'username': 'manifest2',
        'password': 'manifest',
        'host': 'ip',
        'port': '1521',
        'service_name': 'test2'
    }
}

def get_connection_string(db_config):
    """根据配置生成数据库连接字符串"""
    return f"{db_config['username']}/{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['service_name']}"

def clean_number(value):
    """清理数字，处理无效数字格式"""
    if value is None:
        return None
    
    # 如果已经是数字类型，直接返回
    if isinstance(value, (int, float)):
        return value
    
    # 如果是字符串，尝试转换为数字
    if isinstance(value, str):
        # 去除空白字符
        value = value.strip()
        
        # 如果是空字符串，返回None
        if not value:
            return None
            
        # 尝试转换为数字
        try:
            # 替换逗号等可能导致解析错误的字符
            value = value.replace(',', '')
            return float(value)
        except ValueError:
            # 如果无法转换，返回None
            return None
    
    return None

def get_column_types(cursor_description):
    """从游标描述中获取列类型"""
    column_types = {}
    for col in cursor_description:
        col_name = col[0]
        col_type = col[1]
        column_types[col_name] = col_type
    return column_types

def get_table_info(source_table, conn):
    """获取表完整的结构信息，包括默认值和约束"""
    cursor = conn.cursor()
    try:
        # 获取列信息
        cursor.execute(f"""
        SELECT 
            col.column_name, 
            col.data_type, 
            col.data_length, 
            col.data_precision, 
            col.data_scale, 
            col.nullable,
            col.data_default
        FROM all_tab_columns col
        WHERE col.table_name = '{source_table.upper()}'
        ORDER BY col.column_id
        """)
        return cursor.fetchall()
    finally:
        cursor.close()

def create_table_like_source(source_table, target_table, source_conn, target_conn):
    """根据源表创建目标表，保留默认值和约束"""
    # 获取源表结构
    try:
        # 获取列信息，包括默认值
        columns = get_table_info(source_table, source_conn)
        
        if not columns:
            raise ValueError(f"找不到源表 {source_table}")
        
        # 构建创建表SQL
        create_sql = f"CREATE TABLE {target_table} (\n"
        col_defs = []
        
        for col_name, data_type, data_length, data_precision, data_scale, nullable, data_default in columns:
            # 构建列定义
            if data_type == 'NUMBER' and data_precision is not None:
                # 对于NUMBER类型，使用VARCHAR2(100)代替，避免转换问题
                type_def = "VARCHAR2(100)"
            elif data_type in ('VARCHAR2', 'CHAR', 'NVARCHAR2'):
                type_def = f"{data_type}({data_length})"
            else:
                type_def = data_type
                
            # 处理NULL约束
            null_def = "" if nullable == 'Y' else " NOT NULL"
            
            # 处理默认值
            default_def = ""
            if data_default is not None:
                # 处理SYSDATE默认值
                if "SYSDATE" in str(data_default).upper():
                    default_def = " DEFAULT SYSDATE"
                else:
                    default_def = f" DEFAULT {data_default}"
            
            col_defs.append(f"  {col_name} {type_def}{null_def}{default_def}")
        
        create_sql += ",\n".join(col_defs)
        create_sql += "\n)"
        
        # 执行创建表
        create_cursor = target_conn.cursor()
        try:
            print(f"创建表SQL: {create_sql}")
            create_cursor.execute(create_sql)
            target_conn.commit()
            print(f"成功创建表 {target_table}")
            return True
        except Exception as e:
            target_conn.rollback()
            print(f"创建表失败: {str(e)}")
            logger.error(f"创建表失败: {str(e)}")
            return False
        finally:
            create_cursor.close()
            
    except Exception as e:
        print(f"获取表结构信息失败: {str(e)}")
        logger.error(f"获取表结构信息失败: {str(e)}")
        return False

def sync_data(source_table="CS_SINGLEWINDOW_PRODUCT_CODE", target_table="T_BC_SW_TS_CODE", 
              where_clause=None, is_first_sync=False):
    """同步数据从测试环境到生产环境，处理数据类型问题"""
    print(f"开始同步数据: {source_table} -> {target_table}")
    print(f"条件: {where_clause if where_clause else '全量数据'}")
    
    start_time = datetime.now()
    
    # 连接数据库
    source_conn = None
    target_conn = None
    
    try:
        # 连接源数据库（测试环境）
        source_conn = oracle.connect(get_connection_string(DB_CONFIG['TEST']))
        print("已连接到测试环境数据库")
        
        # 连接目标数据库（生产环境）
        target_conn = oracle.connect(get_connection_string(DB_CONFIG['PROD']))
        print("已连接到生产环境数据库")
        
        # 检查目标表是否存在
        target_cursor = target_conn.cursor()
        try:
            # 尝试查询目标表，如果不存在会抛出异常
            target_cursor.execute(f"SELECT COUNT(*) FROM {target_table} WHERE ROWNUM=1")
            table_exists = True
        except:
            table_exists = False
        finally:
            target_cursor.close()
        
        # 如果表不存在，需要创建
        if not table_exists:
            print(f"目标表 {target_table} 不存在，正在创建...")
            
            # 使用新的创建表函数
            success = create_table_like_source(source_table, target_table, source_conn, target_conn)
            if not success:
                raise ValueError("创建表失败，中止同步")
        
        # 构建查询语句
        source_cursor = source_conn.cursor()
        target_cursor = target_conn.cursor()
        
        try:
            # 构建查询SQL
            query_sql = f"SELECT * FROM {source_table}"
            if where_clause:
                query_sql += f" WHERE {where_clause}"
                
            print(f"执行查询: {query_sql}")
            source_cursor.execute(query_sql)
            
            # 获取列信息和类型
            columns = [col[0] for col in source_cursor.description]
            column_types = get_column_types(source_cursor.description)
            
            # 如果是第一次同步，先清空目标表
            if is_first_sync and table_exists:
                try:
                    target_cursor.execute(f"DELETE FROM {target_table}")
                    target_conn.commit()
                    print(f"已清空目标表 {target_table}")
                except Exception as e:
                    target_conn.rollback()
                    print(f"清空表失败: {str(e)}")
            
            # 准备批量插入
            placeholders = ', '.join([f':{i+1}' for i in range(len(columns))])
            insert_sql = f"INSERT INTO {target_table} ({', '.join(columns)}) VALUES ({placeholders})"
            
            # 批量拉取和插入数据，处理数据类型问题
            batch_size = 1000
            total_rows = 0
            total_failed = 0
            rows = source_cursor.fetchmany(batch_size)
            
            while rows:
                # 数据处理 - 修复NUMBER类型数据
                cleaned_rows = []
                for row in rows:
                    cleaned_row = list(row)  # 转换为列表以便修改
                    for i, value in enumerate(cleaned_row):
                        col_name = columns[i]
                        col_type = column_types.get(col_name)
                        
                        # 检查是否为NUMBER类型且需要清理
                        if col_type == oracle.NUMBER and isinstance(value, str):
                            cleaned_row[i] = clean_number(value)
                    
                    cleaned_rows.append(tuple(cleaned_row))  # 转回元组
                
                try:
                    target_cursor.executemany(insert_sql, cleaned_rows)
                    target_conn.commit()
                    total_rows += len(cleaned_rows)
                    print(f"已插入 {total_rows} 行数据")
                except Exception as e:
                    target_conn.rollback()
                    print(f"插入数据批次失败: {str(e)}")
                    logger.error(f"插入数据批次失败: {str(e)}")
                    
                    # 单条尝试插入，跳过有问题的记录
                    for i, row in enumerate(cleaned_rows):
                        try:
                            target_cursor.execute(insert_sql, row)
                            target_conn.commit()
                            total_rows += 1
                        except Exception as e_row:
                            total_failed += 1
                            error_msg = str(e_row)
                            logger.error(f"插入第 {i} 条记录失败: {error_msg[:100]}")
                            # 打印前几个出问题的行，帮助调试
                            if total_failed <= 5:
                                print(f"数据行 {i} 插入失败: {error_msg[:100]}")
                                print(f"问题数据: {row}")
                
                # 获取下一批数据
                rows = source_cursor.fetchmany(batch_size)
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            print(f"同步完成! 总共插入 {total_rows} 行数据，失败 {total_failed} 行")
            print(f"开始时间: {start_time}, 结束时间: {end_time}, 耗时: {duration} 秒")
            
        finally:
            source_cursor.close()
            target_cursor.close()
            
    except Exception as e:
        print(f"同步过程中发生错误: {str(e)}")
        logger.error(f"同步过程中发生错误: {str(e)}")
    finally:
        if source_conn:
            source_conn.close()
        if target_conn:
            target_conn.close()

def get_oracle_type(col_desc, use_varchar_for_number=False):
    """根据cx_Oracle列描述返回Oracle数据类型"""
    col_type = col_desc[1]
    col_size = col_desc[2]
    col_scale = col_desc[5]
    
    # Oracle NUMBER类型
    if col_type == oracle.NUMBER:
        if use_varchar_for_number:
            # 使用VARCHAR2替代NUMBER类型，解决类型转换问题
            return "VARCHAR2(100)"
        else:
            if col_scale is not None and col_scale > 0:
                return f"NUMBER({col_size},{col_scale})"
            elif col_size is not None:
                return f"NUMBER({col_size})"
            else:
                return "NUMBER"
    
    # Oracle字符类型
    elif col_type == oracle.STRING:
        return f"VARCHAR2({col_size})"
    
    # Oracle日期类型
    elif col_type == oracle.DATETIME:
        return "DATE"
    
    # 其他类型简单处理
    else:
        return "VARCHAR2(4000)"

def print_usage():
    print("""
使用方法:
  python 数据类型修复版_数据同步.py [参数]

参数:
  --full              全量同步，会清空目标表 (默认增量同步)
  --source=表名       源表名称 (默认: CS_SINGLEWINDOW_PRODUCT_CODE)
  --target=表名       目标表名称 (默认: T_BC_SW_TS_CODE)
  --where=条件        过滤条件 (默认: createtime > sysdate - 0.5)
  --fix-numbers       修复数字类型问题，将 NUMBER 类型列转为 VARCHAR2

示例:
  python 数据类型修复版_数据同步.py --full
  python 数据类型修复版_数据同步.py --source=TEMP_TRADE_STATISTICS --target=PROD_TRADE_STATISTICS
    """)

if __name__ == "__main__":
    # 解析命令行参数
    is_full = False
    source_table = "CS_SINGLEWINDOW_PRODUCT_CODE"
    target_table = "T_BC_SW_TS_CODE"
    where_clause = "createtime > sysdate - 0.5"
    
    # 简单的参数解析
    if len(sys.argv) > 1:
        for arg in sys.argv[1:]:
            if arg == "--full":
                is_full = True
            elif arg.startswith("--source="):
                source_table = arg.split("=", 1)[1]
            elif arg.startswith("--target="):
                target_table = arg.split("=", 1)[1]
            elif arg.startswith("--where="):
                where_clause = arg.split("=", 1)[1]
            elif arg in ["--help", "-h"]:
                print_usage()
                sys.exit(0)
    
    # 如果是全量同步，不使用where子句
    if is_full:
        where_clause = None
    
    # 开始同步
    sync_data(source_table, target_table, where_clause, is_full) 