import os
import time
import re
import importlib.util
import sys

# 检查并安装必要的依赖
required_packages = ['pandas', 'DrissionPage', 'openpyxl', 'lxml', 'beautifulsoup4', 'html5lib']

def check_and_install_packages():
    """检查并安装必要的包"""
    missing_packages = []
    for package in required_packages:
        try:
            importlib.import_module(package)
            print(f"已安装: {package}")
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("以下包需要安装:")
        for package in missing_packages:
            print(f"- {package}")
        
        choice = input("是否自动安装这些包? (y/n): ")
        if choice.lower() == 'y':
            for package in missing_packages:
                print(f"正在安装 {package}...")
                import pip
                pip.main(['install', package])
                print(f"{package} 安装完成")

# 检查依赖包
check_and_install_packages()

# 导入必要的模块
from DrissionPage import SessionPage, ChromiumPage
import pandas as pd

# 确保输出目录存在
output_dir = "提取的表格数据"
if not os.path.exists(output_dir):
    os.makedirs(output_dir)
    print(f"创建输出目录: {output_dir}")

# 获取脚本所在目录
script_dir = os.path.dirname(os.path.abspath(__file__))

def get_file_path(filename):
    """获取文件的完整路径"""
    return os.path.join(script_dir, filename)

def extract_links_from_file(file_path):
    """从文件中提取链接"""
    all_links = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                parts = line.strip().split(',', 2)  # 最多分成3部分（年份,文本,URL）
                if len(parts) == 3:
                    year, text, url = parts
                    link_info = {
                        "year": year,
                        "text": text,
                        "url": url
                    }
                    all_links.append(link_info)
    except Exception as e:
        print(f"读取链接文件时出错: {e}")
    
    return all_links

def extract_with_download(url, filename):
    """使用下载按钮提取数据"""
    try:
        # 动态导入模块，避免循环导入
        spec = importlib.util.spec_from_file_location("module.name", get_file_path("下载表格数据.py"))
        download_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(download_module)
        
        return download_module.download_and_save_excel(url, filename)
    except Exception as e:
        print(f"使用下载按钮提取时出错: {e}")
        return False

def extract_with_direct_parsing(url, filename):
    """使用直接提取方式提取数据"""
    try:
        # 动态导入模块，避免循环导入
        spec = importlib.util.spec_from_file_location("module.name", get_file_path("直接提取表格数据.py"))
        direct_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(direct_module)
        
        return direct_module.extract_table_from_url(url, filename)
    except Exception as e:
        print(f"使用直接提取时出错: {e}")
        return False

def process_links(links, method="auto", target_years=None):
    """
    处理链接列表，提取数据
    :param links: 链接列表
    :param method: 提取方法，"download"使用下载按钮，"direct"直接提取表格，"auto"自动选择
    :param target_years: 目标年份列表，如果指定则只处理这些年份的链接
    """
    success_count = 0
    fail_count = 0
    
    # 过滤目标年份
    if target_years:
        filtered_links = [link for link in links if link["year"] in target_years]
    else:
        filtered_links = links
    
    print(f"将处理 {len(filtered_links)} 个链接")
    
    # 创建浏览器页面对象
    if method in ["auto", "download"]:
        # 动态导入页面模块，初始化页面
        spec = importlib.util.spec_from_file_location("module.name", get_file_path("下载表格数据.py"))
        download_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(download_module)
        download_module.init_browser()
    
    if method in ["auto", "direct"]:
        # 动态导入页面模块
        spec = importlib.util.spec_from_file_location("module.name", get_file_path("直接提取表格数据.py"))
        direct_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(direct_module)
    
    # 处理链接
    for i, link_info in enumerate(filtered_links, 1):
        year = link_info['year']
        text = link_info['text']
        url = link_info['url']
        
        print(f"\n[{i}/{len(filtered_links)}] 开始处理: {year} - {text}")
        
        # 构造文件名
        clean_text = re.sub(r'[\\/*?:"<>|]', '', text)
        file_name = f"{year}_{clean_text}.xlsx"
        
        success = False
        
        # 根据方法选择提取方式
        if method == "download":
            success = download_module.download_and_save_excel(url, file_name)
        elif method == "direct":
            success = direct_module.extract_table_from_url(url, file_name)
        else:  # auto
            # 先尝试下载方式
            print("尝试使用下载按钮提取...")
            success = download_module.download_and_save_excel(url, file_name)
            
            # 如果下载失败，尝试直接提取
            if not success:
                print("下载失败，尝试直接提取表格...")
                success = direct_module.extract_table_from_url(url, file_name)
        
        if success:
            success_count += 1
        else:
            fail_count += 1
        
        # 防止请求过于频繁
        time.sleep(3)
    
    # 关闭浏览器
    if method in ["auto", "download"]:
        download_module.close_browser()
    
    print(f"\n处理完成! 成功: {success_count}, 失败: {fail_count}")

def main_menu():
    """主菜单"""
    while True:
        print("\n===== 海关数据提取工具 =====")
        print("1. 提取单个URL数据")
        print("2. 批量处理链接")
        print("3. 生成年份统计报告")
        print("0. 退出程序")
        
        choice = input("请输入选项 (0-3): ")
        
        if choice == '1':
            single_url_menu()
        elif choice == '2':
            batch_process_menu()
        elif choice == '3':
            generate_report_menu()
        elif choice == '0':
            print("程序已退出")
            break
        else:
            print("无效的选择，请重新输入")

def single_url_menu():
    """单个URL处理菜单"""
    print("\n=== 单个URL数据提取 ===")
    url = input("请输入要处理的URL: ")
    file_name = input("请输入保存的文件名 (例如: 2019年进出口商品收发货人所在地总值表.xlsx): ")
    
    if not file_name.endswith('.xlsx'):
        file_name += '.xlsx'
    
    print("\n请选择提取方式:")
    print("1. 自动选择最适合的方式")
    print("2. 使用下载按钮")
    print("3. 直接提取表格数据")
    
    method_choice = input("请输入选择 (1-3): ")
    
    method = "auto"
    if method_choice == '2':
        method = "download"
    elif method_choice == '3':
        method = "direct"
    
    print(f"\n开始处理URL: {url}")
    
    success = False
    if method == "download":
        # 动态导入模块
        spec = importlib.util.spec_from_file_location("module.name", get_file_path("下载表格数据.py"))
        download_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(download_module)
        
        # 初始化浏览器
        download_module.init_browser()
        success = download_module.download_and_save_excel(url, file_name)
        download_module.close_browser()
    elif method == "direct":
        # 动态导入模块
        spec = importlib.util.spec_from_file_location("module.name", get_file_path("直接提取表格数据.py"))
        direct_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(direct_module)
        
        success = direct_module.extract_table_from_url(url, file_name)
    else:  # auto
        # 先尝试下载
        spec = importlib.util.spec_from_file_location("module.name", get_file_path("下载表格数据.py"))
        download_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(download_module)
        
        download_module.init_browser()
        success = download_module.download_and_save_excel(url, file_name)
        
        # 如果下载失败，尝试直接提取
        if not success:
            spec = importlib.util.spec_from_file_location("module.name", get_file_path("直接提取表格数据.py"))
            direct_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(direct_module)
            
            success = direct_module.extract_table_from_url(url, file_name)
        
        download_module.close_browser()
    
    print(f"处理结果: {'成功' if success else '失败'}")

def batch_process_menu():
    """批量处理菜单"""
    print("\n=== 批量处理链接 ===")
    
    # 检查链接文件
    link_file = get_file_path('进出口商品收发货人所在地总值表_所有链接.txt')
    if not os.path.exists(link_file):
        link_file = input("未找到默认链接文件，请输入链接文件路径: ")
    
    # 读取链接
    links = extract_links_from_file(link_file)
    if not links:
        print("未找到有效链接或文件不存在")
        return
    
    print(f"从文件中读取了 {len(links)} 个链接")
    
    # 获取可用年份
    years = sorted(set(link["year"] for link in links))
    print(f"可用年份: {', '.join(years)}")
    
    # 选择年份
    print("\n请选择要处理的年份:")
    print("1. 处理所有年份")
    print("2. 选择特定年份")
    print("3. 选择年份范围")
    
    year_choice = input("请输入选择 (1-3): ")
    
    target_years = None
    if year_choice == '2':
        years_input = input("请输入要处理的年份（用逗号分隔，例如: 2014,2015,2016）: ")
        target_years = [year.strip() for year in years_input.split(',')]
    elif year_choice == '3':
        start_year = input("请输入起始年份: ")
        end_year = input("请输入结束年份: ")
        
        try:
            start = int(start_year)
            end = int(end_year)
            target_years = [str(year) for year in range(start, end+1)]
        except ValueError:
            print("年份输入格式错误，请输入有效的数字年份")
            return
    
    # 选择提取方式
    print("\n请选择数据提取方式:")
    print("1. 自动选择最适合的方式")
    print("2. 统一使用下载按钮")
    print("3. 统一使用直接提取表格")
    
    method_choice = input("请输入选择 (1-3): ")
    
    method = "auto"
    if method_choice == '2':
        method = "download"
    elif method_choice == '3':
        method = "direct"
    
    # 处理链接
    process_links(links, method, target_years)

def generate_report_menu():
    """生成统计报告菜单"""
    print("\n=== 生成年份统计报告 ===")
    
    # 确保目录存在
    report_dir = "统计报告"
    if not os.path.exists(report_dir):
        os.makedirs(report_dir)
    
    # 检查数据目录
    if not os.path.exists(output_dir) or not os.listdir(output_dir):
        print("未找到已提取的数据。请先提取数据。")
        return
    
    # 提取年份列表
    pattern = r'^(\d{4})_.*\.xlsx$'
    years = set()
    excel_files = []
    
    for file in os.listdir(output_dir):
        if file.endswith('.xlsx'):
            match = re.match(pattern, file)
            if match:
                year = match.group(1)
                years.add(year)
                excel_files.append((year, os.path.join(output_dir, file)))
    
    years = sorted(years)
    
    if not years:
        print("未找到有效的年份数据文件")
        return
    
    print(f"找到以下年份的数据: {', '.join(years)}")
    
    # 选择要分析的年份
    print("\n请选择要生成报告的年份:")
    print("1. 所有年份")
    print("2. 选择特定年份")
    print("3. 选择年份范围")
    
    year_choice = input("请输入选择 (1-3): ")
    
    target_years = years
    if year_choice == '2':
        years_input = input("请输入要处理的年份（用逗号分隔，例如: 2014,2015,2016）: ")
        target_years = [year.strip() for year in years_input.split(',')]
    elif year_choice == '3':
        start_year = input("请输入起始年份: ")
        end_year = input("请输入结束年份: ")
        
        try:
            start = int(start_year)
            end = int(end_year)
            target_years = [str(year) for year in range(start, end+1) if str(year) in years]
        except ValueError:
            print("年份输入格式错误，请输入有效的数字年份")
            return
    
    # 生成报告
    print(f"\n开始生成 {', '.join(target_years)} 年份的统计报告...")
    
    try:
        # 筛选目标年份的文件
        target_files = [(year, file) for year, file in excel_files if year in target_years]
        
        # 加载数据
        dfs = []
        for year, file in target_files:
            try:
                df = pd.read_excel(file)
                df['年份'] = year
                dfs.append(df)
                print(f"已加载 {year} 年数据")
            except Exception as e:
                print(f"加载 {year} 年数据时出错: {e}")
        
        if not dfs:
            print("未能加载任何数据")
            return
        
        # 合并数据
        try:
            combined_df = pd.concat(dfs, ignore_index=True)
            print(f"已合并 {len(dfs)} 个数据集，共 {len(combined_df)} 行数据")
            
            # 保存合并数据
            combined_file = os.path.join(report_dir, f"{min(target_years)}-{max(target_years)}年数据汇总.xlsx")
            combined_df.to_excel(combined_file, index=False)
            print(f"数据汇总已保存至: {combined_file}")
            
            # 生成年度统计
            yearly_stats_file = os.path.join(report_dir, f"{min(target_years)}-{max(target_years)}年度统计.xlsx")
            
            # 根据数据列名动态生成统计
            # 假设第一列是地区，其余列是统计数据
            if len(combined_df.columns) > 2:  # 至少有地区、年份和一个数值列
                # 获取可能的数值列（排除年份和第一列）
                # 第一列可能是省份/地区名
                possible_value_cols = combined_df.columns.tolist()[1:]
                possible_value_cols.remove('年份')
                
                # 生成透视表
                for col in possible_value_cols:
                    try:
                        if pd.api.types.is_numeric_dtype(combined_df[col]) or combined_df[col].str.replace(',', '').astype(float).notna().any():
                            # 如果是数值列或可以转换为数值
                            print(f"正在统计 {col} 列...")
                            
                            # 尝试清理数据
                            if not pd.api.types.is_numeric_dtype(combined_df[col]):
                                # 如果不是数值类型，尝试转换
                                combined_df[col] = combined_df[col].str.replace(',', '').astype(float)
                            
                            # 按年份和地区透视
                            pivot = combined_df.pivot_table(
                                index=combined_df.columns[0],  # 第一列作为地区
                                columns='年份',
                                values=col,
                                aggfunc='sum'
                            )
                            
                            # 计算增长率
                            growth_df = pd.DataFrame()
                            years_list = sorted(combined_df['年份'].unique())
                            
                            for i in range(1, len(years_list)):
                                prev_year = years_list[i-1]
                                curr_year = years_list[i]
                                
                                if prev_year in pivot.columns and curr_year in pivot.columns:
                                    col_name = f"{prev_year}-{curr_year}增长率"
                                    growth_df[col_name] = (pivot[curr_year] - pivot[prev_year]) / pivot[prev_year] * 100
                            
                            # 合并原始数据和增长率
                            result_df = pd.concat([pivot, growth_df], axis=1)
                            
                            # 添加合计行
                            result_df.loc['合计'] = result_df.sum(numeric_only=True)
                            
                            # 保存结果
                            with pd.ExcelWriter(yearly_stats_file, engine='openpyxl', mode='a' if os.path.exists(yearly_stats_file) else 'w') as writer:
                                result_df.to_excel(writer, sheet_name=f"{col}统计")
                    except Exception as e:
                        print(f"处理 {col} 列时出错: {e}")
                
                print(f"统计报告已保存至: {yearly_stats_file}")
            else:
                print("数据列不足，无法生成有意义的统计")
        except Exception as e:
            print(f"生成报告时出错: {e}")
    except Exception as e:
        print(f"处理数据时出错: {e}")

if __name__ == "__main__":
    main_menu() 