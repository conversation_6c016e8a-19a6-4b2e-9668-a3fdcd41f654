import pandas as pd
import os
import glob
import re
import numpy as np
from pathlib import Path

# --- 智能单位检测与转换功能 ---

def detect_unit_from_data(df, amount_columns):
    """从数据中检测单位（万元或亿元）"""
    for col in amount_columns:
        if col in df.columns:
            # 检查列中是否有非常大的数值，这通常意味着单位是“元”
            # 将非数字值替换为0，以便进行数值比较
            numeric_series = pd.to_numeric(df[col], errors='coerce').fillna(0)
            if (numeric_series > 10000000000).any():  # 数值大于100亿，很可能是“元”
                return 'yuan'
    return None

def detect_unit_from_markers(df):
    """从表头或文件名等标记中检测单位"""
    # 检查所有单元格内容
    for _, row in df.iterrows():
        for item in row:
            if isinstance(item, str):
                if '（亿元）' in item or '亿元' in item:
                    return 'yi_yuan'
                if '（万元）' in item or '万元' in item:
                    return 'wan_yuan'
    return None

def get_conversion_factor(unit):
    """根据单位获取转换因子，目标是“万元”"""
    if unit == 'yi_yuan':
        return 10000  # 亿元转万元
    if unit == 'yuan':
        return 1 / 10000  # 元转万元
    return 1  # 已经是万元或无法识别，不转换

# --- 数据处理核心功能 ---

def clean_column_names(df):
    """清理Pandas MultiIndex表头"""
    if isinstance(df.columns, pd.MultiIndex):
        # 将多级索引合并为单级，处理 NaN 和不需要的文本
        df.columns = ['_'.join(map(str, col)).strip() for col in df.columns.values]
        df = df.rename(columns=lambda x: re.sub(r'_nan|_Unnamed: \d+_level_\d', '', x).strip('_'))
    return df

def find_header_start(file_path):
    """智能查找表头的起始行"""
    # 关键字来识别表头行
    header_keywords = ['项目', '人民币', '美元', '国别', '贸易方式', '企业性质', '商品编码']
    
    # 预读少量行来定位表头
    try:
        temp_df = pd.read_excel(file_path, engine='xlrd', header=None, nrows=10)
        for i, row in temp_df.iterrows():
            row_str = ''.join(map(str, row.dropna().tolist()))
            if any(keyword in row_str for keyword in header_keywords):
                return i
    except Exception:
        # 如果预读失败，返回默认值
        return 0
    return 0 # 默认从第一行开始

def find_data_start_row(df, project_col_name):
    """找到有效数据开始的行索引"""
    for i, value in enumerate(df[project_col_name]):
        val_str = str(value)
        if val_str not in ['nan', ''] and not any(k in val_str for k in ['注：', '资料来源']):
            return i
    return 0
    
def get_stat_date_from_filename(filename):
    """从文件名中提取统计年月"""
    match = re.search(r'（(\d{4})年(\d{1,2}-\d{1,2})月）', filename)
    if match:
        year = match.group(1)
        # 取结束月份
        month_range = match.group(2)
        last_month = month_range.split('-')[-1]
        return f"{year}{int(last_month):02d}"
    return "202505" # Fallback

def get_col_by_keywords(df, keywords, exclude=None):
    """根据关键字查找列名"""
    exclude = exclude if exclude else []
    for col in df.columns:
        if all(kw in col for kw in keywords) and not any(ex in col for ex in exclude):
            return col
    return None


def process_total_value_data(df, stat_type, stat_name):
    """处理总值表B数据 - 使用新策略"""
    project_col = df.columns[0]
    data_rows = df.iloc[find_data_start_row(df, project_col):].copy()
    data_rows = data_rows.dropna(subset=[project_col])
    data_rows = data_rows[~data_rows[project_col].astype(str).str.contains('注：|资料来源')]
    
    # 单位检测
    unit = detect_unit_from_markers(df) or 'wan_yuan'
    conversion_factor = get_conversion_factor(unit)

    output_df = pd.DataFrame()
    output_df['STAT_DATE'] = data_rows[project_col]
    output_df['STAT_TYPE'] = stat_type
    output_df['STAT_NAME'] = stat_name
    output_df['STAT_CODE'] = 'B'
    output_df['STAT_CONTENT_RAW'] = '进出口总值'

    # 精准列映射
    output_df['ACC_A_CNY_AMOUNT'] = pd.to_numeric(data_rows[get_col_by_keywords(df, ['进出口', '人民币'], exclude=['同比'])], errors='coerce') * conversion_factor
    output_df['ACC_A_CNY_YOY'] = pd.to_numeric(data_rows[get_col_by_keywords(df, ['进出口', '人民币', '同比'])], errors='coerce')
    output_df['ACC_E_CNY_AMOUNT'] = pd.to_numeric(data_rows[get_col_by_keywords(df, ['出口', '人民币'], exclude=['同比'])], errors='coerce') * conversion_factor
    output_df['ACC_E_CNY_YOY'] = pd.to_numeric(data_rows[get_col_by_keywords(df, ['出口', '人民币', '同比'])], errors='coerce')
    output_df['ACC_I_CNY_AMOUNT'] = pd.to_numeric(data_rows[get_col_by_keywords(df, ['进口', '人民币'], exclude=['同比'])], errors='coerce') * conversion_factor
    output_df['ACC_I_CNY_YOY'] = pd.to_numeric(data_rows[get_col_by_keywords(df, ['进口', '人民币', '同比'])], errors='coerce')
        
    return output_df

def process_country_data(df, stat_type, stat_name, stat_date):
    """处理国别表数据"""
    project_col = df.columns[0]
    data_rows = df.iloc[find_data_start_row(df, project_col):].copy()
    data_rows = data_rows.dropna(subset=[project_col])
    data_rows = data_rows[~data_rows[project_col].astype(str).str.contains('注：|资料来源|洲|APEC')]
    data_rows[project_col] = data_rows[project_col].str.strip()

    unit = detect_unit_from_markers(df) or 'wan_yuan'
    conversion_factor = get_conversion_factor(unit)

    output_df = pd.DataFrame()
    output_df['STAT_DATE'] = stat_date
    output_df['STAT_TYPE'] = stat_type
    output_df['STAT_NAME'] = stat_name
    output_df['STAT_CODE'] = data_rows[project_col]
    output_df['STAT_CONTENT_RAW'] = data_rows[project_col]

    # 累计
    output_df['ACC_A_CNY_AMOUNT'] = pd.to_numeric(data_rows[get_col_by_keywords(df, ['进出口', '2025年01月', '人民币'], exclude=['同比'])], errors='coerce') * conversion_factor
    output_df['ACC_E_CNY_AMOUNT'] = pd.to_numeric(data_rows[get_col_by_keywords(df, ['出口', '2025年01月', '人民币'], exclude=['同比'])], errors='coerce') * conversion_factor
    output_df['ACC_I_CNY_AMOUNT'] = pd.to_numeric(data_rows[get_col_by_keywords(df, ['进口', '2025年01月', '人民币'], exclude=['同比'])], errors='coerce') * conversion_factor
    # 当月
    output_df['MON_A_CNY_AMOUNT'] = pd.to_numeric(data_rows[get_col_by_keywords(df, ['进出口', '2025年05月', '人民币'], exclude=['同比'])], errors='coerce') * conversion_factor
    output_df['MON_E_CNY_AMOUNT'] = pd.to_numeric(data_rows[get_col_by_keywords(df, ['出口', '2025年05月', '人民币'], exclude=['同比'])], errors='coerce') * conversion_factor
    output_df['MON_I_CNY_AMOUNT'] = pd.to_numeric(data_rows[get_col_by_keywords(df, ['进口', '2025年05月', '人民币'], exclude=['同比'])], errors='coerce') * conversion_factor
        
    return output_df

def process_trade_mode_data(df, stat_type, stat_name, stat_date):
    """处理贸易方式表数据"""
    project_col = df.columns[0]
    data_rows = df.iloc[find_data_start_row(df, project_col):].copy()
    data_rows = data_rows.dropna(subset=[project_col])
    data_rows = data_rows[~data_rows[project_col].astype(str).str.contains('注：|资料来源')]
    
    unit = detect_unit_from_markers(df) or 'wan_yuan'
    conversion_factor = get_conversion_factor(unit)

    output_df = pd.DataFrame()
    output_df['STAT_DATE'] = stat_date
    output_df['STAT_TYPE'] = stat_type
    output_df['STAT_NAME'] = stat_name
    output_df['STAT_CODE'] = data_rows[project_col]
    output_df['STAT_CONTENT_RAW'] = data_rows[project_col]
    
    output_df['ACC_A_CNY_AMOUNT'] = pd.to_numeric(data_rows[get_col_by_keywords(df, ['进出口', '2025年01月', '人民币'], exclude=['同比'])], errors='coerce') * conversion_factor
    output_df['ACC_E_CNY_AMOUNT'] = pd.to_numeric(data_rows[get_col_by_keywords(df, ['出口', '2025年01月', '人民币'], exclude=['同比'])], errors='coerce') * conversion_factor
    output_df['ACC_I_CNY_AMOUNT'] = pd.to_numeric(data_rows[get_col_by_keywords(df, ['进口', '2025年01月', '人民币'], exclude=['同比'])], errors='coerce') * conversion_factor
        
    return output_df

def process_enterprise_data(df, stat_type, stat_name, stat_date, direction):
    """处理企业性质表数据"""
    enterprise_col = df.columns[0]
    tradeway_col = df.columns[1]
    data_rows = df.iloc[find_data_start_row(df, enterprise_col):].copy()
    data_rows = data_rows.dropna(how='all')
    data_rows = data_rows[~data_rows[enterprise_col].astype(str).str.contains('注：|资料来源')]

    # 填充企业性质列
    data_rows[enterprise_col] = data_rows[enterprise_col].str.strip().fillna(method='ffill')
    data_rows = data_rows[~data_rows[enterprise_col].str.contains('#合计')]
    
    unit = detect_unit_from_markers(df) or 'wan_yuan'
    conversion_factor = get_conversion_factor(unit)

    output_df = pd.DataFrame()
    output_df['STAT_DATE'] = stat_date
    output_df['STAT_TYPE'] = stat_type
    output_df['STAT_NAME'] = stat_name + "_" + data_rows[enterprise_col]
    output_df['STAT_CODE'] = data_rows[tradeway_col]
    output_df['STAT_CONTENT_RAW'] = data_rows[tradeway_col]
    
    amount_col = get_col_by_keywords(df, ['人民币'], exclude=['同比'])
    if direction == '出口':
        output_df['ACC_E_CNY_AMOUNT'] = pd.to_numeric(data_rows[amount_col], errors='coerce') * conversion_factor
    else: # 进口
        output_df['ACC_I_CNY_AMOUNT'] = pd.to_numeric(data_rows[amount_col], errors='coerce') * conversion_factor
    
    return output_df

def process_commodity_data(df, stat_type, stat_name, stat_date, direction, file_path):
    """处理进出口商品量值表数据"""
    code_col = df.columns[0]
    data_rows = df.iloc[find_data_start_row(df, code_col):].copy()
    data_rows = data_rows.dropna(subset=[code_col])
    data_rows = data_rows[~data_rows[code_col].astype(str).str.contains('注：|资料来源')]
    data_rows[code_col] = data_rows[code_col].str.strip()

    unit = detect_unit_from_markers(df) or 'wan_yuan'
    conversion_factor = get_conversion_factor(unit)

    output_df = pd.DataFrame()
    output_df['STAT_DATE'] = stat_date
    output_df['STAT_TYPE'] = stat_type
    output_df['STAT_NAME'] = stat_name
    output_df['STAT_CODE'] = data_rows[code_col]
    output_df['STAT_CONTENT_RAW'] = '商品' # Simplified

    # 累计
    amount_col = get_col_by_keywords(df, ['2025年01月', '人民币'], exclude=['同比'])
    if direction == '出口':
        output_df['ACC_E_CNY_AMOUNT'] = pd.to_numeric(data_rows[amount_col], errors='coerce') * conversion_factor
        output_df['ACC_E_AMOUNT'] = pd.to_numeric(data_rows[get_col_by_keywords(df, ['2025年01月', '第一数量'], exclude=['同比'])], errors='coerce')
        output_df['ACC_E_AMOUNT_UNIT'] = data_rows[get_col_by_keywords(df, ['第一数量单位'])]
    else: # 进口
        output_df['ACC_I_CNY_AMOUNT'] = pd.to_numeric(data_rows[amount_col], errors='coerce') * conversion_factor
        output_df['ACC_I_AMOUNT'] = pd.to_numeric(data_rows[get_col_by_keywords(df, ['2025年01月', '第一数量'], exclude=['同比'])], errors='coerce')
        output_df['ACC_I_AMOUNT_UNIT'] = data_rows[get_col_by_keywords(df, ['第一数量单位'])]
        
    return output_df

def transform_beijing_data(input_folder, output_csv_path):
    """
    重构后的北京数据转换函数，能够处理多级表头和不同文件结构。
    """
    xls_files = glob.glob(os.path.join(input_folder, "*.xls"))
    all_dfs = []

    stat_mapping = {
        '（1）': {'type': '06', 'name': '总值表', 'func': process_total_value_data},
        '（2）': {'type': '03', 'name': '国别（地区）', 'func': process_country_data},
        '（5）': {'type': '01', 'name': '贸易方式', 'func': process_trade_mode_data},
        '（6）': {'type': '02', 'name': '企业性质', 'func': process_enterprise_data, 'direction': '出口'},
        '（7）': {'type': '02', 'name': '企业性质', 'func': process_enterprise_data, 'direction': '进口'},
        '（8）': {'type': '04', 'name': '出口商品', 'func': process_commodity_data, 'direction': '出口'},
        '（9）': {'type': '05', 'name': '进口商品', 'func': process_commodity_data, 'direction': '进口'},
    }
    
    final_columns = [
        "STAT_DATE", "STAT_TYPE", "STAT_NAME", "STAT_CODE", "STAT_CONTENT_RAW",
        "ACC_A_CNY_AMOUNT", "ACC_A_CNY_YOY", "ACC_E_CNY_AMOUNT", "ACC_E_CNY_YOY",
        "ACC_I_CNY_AMOUNT", "ACC_I_CNY_YOY", "MON_A_CNY_AMOUNT", "MON_E_CNY_AMOUNT",
        "MON_I_CNY_AMOUNT", "ACC_E_AMOUNT", "ACC_E_AMOUNT_UNIT", "ACC_I_AMOUNT", "ACC_I_AMOUNT_UNIT",
        "DATA_SOURCE"
    ]

    for xls_file in xls_files:
        filename = os.path.basename(xls_file)
        print(f"--- 正在处理文件: {filename} ---")

        key = next((k for k in stat_mapping if filename.startswith(k)), None)
        if not key:
            print(f"跳过文件（无匹配处理规则）: {filename}")
            continue

        try:
            stat_info = stat_mapping[key]
            stat_date = get_stat_date_from_filename(filename)
            
            # 对于非硬编码的函数，预先读取和清理df
            df = None
            if stat_info['func'] not in [process_total_value_data, process_commodity_data]:
                header_start = find_header_start(xls_file)
                try:
                    df = pd.read_excel(xls_file, engine='xlrd', header=[i for i in range(header_start, header_start + 4)])
                except Exception:
                    try:
                        df = pd.read_excel(xls_file, engine='xlrd', header=[i for i in range(header_start, header_start + 3)])
                    except Exception:
                        df = pd.read_excel(xls_file, engine='xlrd', header=[i for i in range(header_start, header_start + 2)])
                df = clean_column_names(df)

            # 构造参数
            params = {
                'df': df,
                'stat_type': stat_info['type'],
                'stat_name': stat_info['name'],
            }
            if stat_info['func'] in [process_total_value_data, process_commodity_data]:
                params['file_path'] = xls_file
            if stat_info['func'] != process_total_value_data:
                params['stat_date'] = stat_date
            if 'direction' in stat_info:
                params['direction'] = stat_info['direction']

            processed_df = stat_info['func'](**params)
            
            if processed_df is not None and not processed_df.empty:
                processed_df['DATA_SOURCE'] = '北京海关'
                all_dfs.append(processed_df)
                print(f"成功处理 {len(processed_df)} 条记录。")

        except Exception as e:
            print(f"处理文件 {filename} 时发生错误: {e}")

    if all_dfs:
        final_df = pd.concat(all_dfs, ignore_index=True)
        
        # 统一列结构
        for col in final_columns:
            if col not in final_df.columns:
                final_df[col] = np.nan
        
        final_df = final_df[final_columns]
        
        final_df.to_csv(output_csv_path, index=False, encoding='utf-8-sig')
        print(f"\n所有文件处理完成。总共生成 {len(final_df)} 条记录。")
        print(f"结果已保存到: {output_csv_path}")
    else:
        print("没有处理任何数据。")

def main():
    input_folder = '北京单月的'
    output_csv = 'T_STATISTICAL_CUS_TOTAL_BEIJING_updated.csv'
    transform_beijing_data(input_folder, output_csv)

    # 检查生成的文件
    try:
        df = pd.read_csv(output_csv)
        print("\n生成文件的前5行预览:")
        print(df.head())
        print(f"\n文件列名: {df.columns.tolist()}")
    except Exception as e:
        print(f"读取结果文件时出错: {e}")

if __name__ == '__main__':
    main()
