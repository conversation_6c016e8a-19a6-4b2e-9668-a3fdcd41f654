-- =====================================================
-- 海关统计数据查询视图和示例
-- 为数据人员提供便捷的查询接口
-- =====================================================

-- 1. 总值统计视图
CREATE OR REPLACE VIEW V_CUS_TOTAL_STATISTICS AS
SELECT 
    STAT_DATE,
    CURRENCY_TYPE,
    UNIT,
    MONTH_IE_AMOUNT as 当月进出口金额,
    MONTH_EXP_AMOUNT as 当月出口金额,
    MONTH_IMP_AMOUNT as 当月进口金额,
    MONTH_TB_AMOUNT as 当月贸易差额,
    YTD_IE_AMOUNT as 累计进出口金额,
    YTD_EXP_AMOUNT as 累计出口金额,
    YTD_IMP_AMOUNT as 累计进口金额,
    YTD_TB_AMOUNT as 累计贸易差额,
    MONTH_IE_YOY as 当月进出口同比,
    MONTH_EXP_YOY as 当月出口同比,
    MONTH_IMP_YOY as 当月进口同比,
    YTD_IE_YOY as 累计进出口同比,
    YTD_EXP_YOY as 累计出口同比,
    YTD_IMP_YOY as 累计进口同比
FROM CUS_TRADE_UNIFIED_STATISTICS
WHERE STAT_TYPE = '01'
ORDER BY STAT_DATE DESC, CURRENCY_TYPE;

COMMENT ON VIEW V_CUS_TOTAL_STATISTICS IS '总值统计视图 - 进出口商品总值表数据';

-- 2. 国别地区统计视图
CREATE OR REPLACE VIEW V_CUS_COUNTRY_STATISTICS AS
SELECT 
    STAT_DATE,
    DIMENSION_1 as 国家地区,
    CURRENCY_TYPE,
    UNIT,
    MONTH_IE_AMOUNT as 当月进出口金额,
    MONTH_EXP_AMOUNT as 当月出口金额,
    MONTH_IMP_AMOUNT as 当月进口金额,
    YTD_IE_AMOUNT as 累计进出口金额,
    YTD_EXP_AMOUNT as 累计出口金额,
    YTD_IMP_AMOUNT as 累计进口金额,
    YTD_IE_YOY as 累计进出口同比,
    YTD_EXP_YOY as 累计出口同比,
    YTD_IMP_YOY as 累计进口同比
FROM CUS_TRADE_UNIFIED_STATISTICS
WHERE STAT_TYPE = '02'
ORDER BY STAT_DATE DESC, YTD_IE_AMOUNT DESC NULLS LAST;

COMMENT ON VIEW V_CUS_COUNTRY_STATISTICS IS '国别地区统计视图 - 按国家地区分类的贸易数据';

-- 3. 贸易方式统计视图
CREATE OR REPLACE VIEW V_CUS_TRADE_MODE_STATISTICS AS
SELECT 
    STAT_DATE,
    DIMENSION_1 as 贸易方式,
    CURRENCY_TYPE,
    UNIT,
    MONTH_IE_AMOUNT as 当月进出口金额,
    MONTH_EXP_AMOUNT as 当月出口金额,
    MONTH_IMP_AMOUNT as 当月进口金额,
    YTD_IE_AMOUNT as 累计进出口金额,
    YTD_EXP_AMOUNT as 累计出口金额,
    YTD_IMP_AMOUNT as 累计进口金额,
    MONTH_IE_YOY as 当月进出口同比,
    MONTH_EXP_YOY as 当月出口同比,
    MONTH_IMP_YOY as 当月进口同比,
    YTD_IE_YOY as 累计进出口同比,
    YTD_EXP_YOY as 累计出口同比,
    YTD_IMP_YOY as 累计进口同比
FROM CUS_TRADE_UNIFIED_STATISTICS
WHERE STAT_TYPE = '05'
ORDER BY STAT_DATE DESC, YTD_IE_AMOUNT DESC NULLS LAST;

COMMENT ON VIEW V_CUS_TRADE_MODE_STATISTICS IS '贸易方式统计视图 - 按贸易方式分类的数据';

-- 4. 主要商品统计视图
CREATE OR REPLACE VIEW V_CUS_MAJOR_COMMODITY_STATISTICS AS
SELECT 
    STAT_DATE,
    DIMENSION_1 as 商品名称,
    TRADE_DIRECTION as 贸易方向,
    CURRENCY_TYPE,
    UNIT,
    QUANTITY_UNIT as 数量单位,
    MONTH_IE_AMOUNT as 当月金额,
    YTD_IE_AMOUNT as 累计金额,
    MONTH_QUANTITY as 当月数量,
    YTD_QUANTITY as 累计数量,
    MONTH_IE_YOY as 当月金额同比,
    YTD_IE_YOY as 累计金额同比,
    MONTH_QUANTITY_YOY as 当月数量同比,
    YTD_QUANTITY_YOY as 累计数量同比
FROM CUS_TRADE_UNIFIED_STATISTICS
WHERE STAT_TYPE = '12'
ORDER BY STAT_DATE DESC, TRADE_DIRECTION, YTD_IE_AMOUNT DESC NULLS LAST;

COMMENT ON VIEW V_CUS_MAJOR_COMMODITY_STATISTICS IS '主要商品统计视图 - 主要商品量值数据';

-- 5. 国家×商品交叉分析视图
CREATE OR REPLACE VIEW V_CUS_COUNTRY_COMMODITY_CROSS AS
SELECT 
    STAT_DATE,
    DIMENSION_1 as 国家,
    DIMENSION_2 as 商品描述,
    TRADE_DIRECTION as 贸易方向,
    CURRENCY_TYPE,
    UNIT,
    MONTH_IE_AMOUNT as 当月金额,
    YTD_IE_AMOUNT as 累计金额
FROM CUS_TRADE_UNIFIED_STATISTICS
WHERE STAT_TYPE = '13'
ORDER BY STAT_DATE DESC, TRADE_DIRECTION, YTD_IE_AMOUNT DESC NULLS LAST;

COMMENT ON VIEW V_CUS_COUNTRY_COMMODITY_CROSS IS '国家商品交叉分析视图 - 国家与商品的交叉数据';

-- 6. 综合统计汇总视图
CREATE OR REPLACE VIEW V_CUS_COMPREHENSIVE_SUMMARY AS
SELECT 
    STAT_DATE,
    STAT_TYPE,
    STAT_TYPE_NAME,
    CURRENCY_TYPE,
    COUNT(*) as 记录数,
    SUM(MONTH_IE_AMOUNT) as 当月进出口总额,
    SUM(MONTH_EXP_AMOUNT) as 当月出口总额,
    SUM(MONTH_IMP_AMOUNT) as 当月进口总额,
    SUM(YTD_IE_AMOUNT) as 累计进出口总额,
    SUM(YTD_EXP_AMOUNT) as 累计出口总额,
    SUM(YTD_IMP_AMOUNT) as 累计进口总额,
    AVG(MONTH_IE_YOY) as 平均当月进出口同比,
    AVG(YTD_IE_YOY) as 平均累计进出口同比
FROM CUS_TRADE_UNIFIED_STATISTICS
GROUP BY STAT_DATE, STAT_TYPE, STAT_TYPE_NAME, CURRENCY_TYPE
ORDER BY STAT_DATE DESC, STAT_TYPE;

COMMENT ON VIEW V_CUS_COMPREHENSIVE_SUMMARY IS '综合统计汇总视图 - 按统计类型汇总的数据';

-- 7. 月度趋势分析视图
CREATE OR REPLACE VIEW V_CUS_MONTHLY_TREND AS
SELECT 
    TO_CHAR(STAT_DATE, 'YYYY') as 年份,
    TO_CHAR(STAT_DATE, 'MM') as 月份,
    TO_CHAR(STAT_DATE, 'YYYY-MM') as 年月,
    STAT_TYPE,
    STAT_TYPE_NAME,
    CURRENCY_TYPE,
    SUM(MONTH_IE_AMOUNT) as 当月进出口总额,
    SUM(MONTH_EXP_AMOUNT) as 当月出口总额,
    SUM(MONTH_IMP_AMOUNT) as 当月进口总额,
    AVG(MONTH_IE_YOY) as 平均进出口同比,
    AVG(MONTH_EXP_YOY) as 平均出口同比,
    AVG(MONTH_IMP_YOY) as 平均进口同比
FROM CUS_TRADE_UNIFIED_STATISTICS
WHERE MONTH_IE_AMOUNT IS NOT NULL OR MONTH_EXP_AMOUNT IS NOT NULL OR MONTH_IMP_AMOUNT IS NOT NULL
GROUP BY TO_CHAR(STAT_DATE, 'YYYY'), TO_CHAR(STAT_DATE, 'MM'), TO_CHAR(STAT_DATE, 'YYYY-MM'), 
         STAT_TYPE, STAT_TYPE_NAME, CURRENCY_TYPE
ORDER BY 年月 DESC, STAT_TYPE;

COMMENT ON VIEW V_CUS_MONTHLY_TREND IS '月度趋势分析视图 - 按月汇总的趋势数据';

-- 创建数据字典视图
CREATE OR REPLACE VIEW V_CUS_DATA_DICTIONARY AS
SELECT 
    STAT_TYPE as 统计类型编码,
    STAT_TYPE_NAME as 统计类型名称,
    DIMENSION_1_NAME as 主维度名称,
    DIMENSION_2_NAME as 次维度名称,
    DIMENSION_3_NAME as 第三维度名称,
    DESCRIPTION as 描述,
    IS_ACTIVE as 是否启用
FROM CUS_STAT_TYPE_CODE
ORDER BY STAT_TYPE;

COMMENT ON VIEW V_CUS_DATA_DICTIONARY IS '数据字典视图 - 统计类型说明';

COMMIT;

-- 显示创建结果
SELECT 'Views created successfully!' as STATUS FROM DUAL;
SELECT VIEW_NAME, COMMENTS FROM USER_TAB_COMMENTS WHERE TABLE_TYPE = 'VIEW' AND VIEW_NAME LIKE 'V_CUS%';
