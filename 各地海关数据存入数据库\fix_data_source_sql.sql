-- 海关统计数据DATA_SOURCE字段修正脚本
-- 根据表注释，正确的DATA_SOURCE编码应该是：
-- 01：全国
-- 02：天津  
-- 03：上海
-- 04：南京
-- 05：杭州
-- 06：宁波
-- 07：浙江
-- 08：北京（新增，需要确认）

-- 1. 首先查看当前数据源分布情况
SELECT DATA_SOURCE, COUNT(*) as 记录数, 
       MIN(CREATE_TIME) as 最早时间,
       MAX(CREATE_TIME) as 最新时间
FROM T_STATISTICAL_CUS_TOTAL_CS 
GROUP BY DATA_SOURCE 
ORDER BY DATA_SOURCE;

-- 2. 查看DATA_SOURCE='02'的数据（可能是浙江数据被错误标记为天津）
SELECT DISTINCT STAT_DATE, STAT_TYPE, STAT_NAME, COUNT(*) as 记录数
FROM T_STATISTICAL_CUS_TOTAL_CS 
WHERE DATA_SOURCE = '02'
GROUP BY STAT_DATE, STAT_TYPE, STAT_NAME
ORDER BY STAT_DATE DESC
FETCH FIRST 20 ROWS ONLY;

-- 3. 查看DATA_SOURCE='01'的数据（可能是北京数据被错误标记为全国）
SELECT DISTINCT STAT_DATE, STAT_TYPE, STAT_NAME, COUNT(*) as 记录数
FROM T_STATISTICAL_CUS_TOTAL_CS 
WHERE DATA_SOURCE = '01'
GROUP BY STAT_DATE, STAT_TYPE, STAT_NAME
ORDER BY STAT_DATE DESC
FETCH FIRST 20 ROWS ONLY;

-- 4. 检查是否有其他DATA_SOURCE值
SELECT DISTINCT DATA_SOURCE 
FROM T_STATISTICAL_CUS_TOTAL_CS;

-- 5. 修正浙江数据：从'02'（天津）改为'07'（浙江）
-- 注意：执行前请确认这些确实是浙江数据
-- UPDATE T_STATISTICAL_CUS_TOTAL_CS 
-- SET DATA_SOURCE = '07'
-- WHERE DATA_SOURCE = '02'
--   AND CREATE_TIME < '2025/08/04 10:00:00';  -- 浙江数据的导入时间

-- 6. 为北京数据添加新的编码'08'
-- 注意：执行前请确认这些确实是北京数据
-- UPDATE T_STATISTICAL_CUS_TOTAL_CS 
-- SET DATA_SOURCE = '08'
-- WHERE DATA_SOURCE = '01'
--   AND CREATE_TIME >= '2025/08/04 10:00:00';  -- 北京数据的导入时间

-- 7. 验证修正结果
-- SELECT DATA_SOURCE, COUNT(*) as 记录数
-- FROM T_STATISTICAL_CUS_TOTAL_CS 
-- GROUP BY DATA_SOURCE 
-- ORDER BY DATA_SOURCE;