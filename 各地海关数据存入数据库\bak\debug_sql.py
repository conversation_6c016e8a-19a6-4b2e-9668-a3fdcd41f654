def analyze_sql_bindings():
    """分析SQL语句中的绑定变量"""
    
    # 从batch_import_to_oracle_fixed.py中复制的SQL语句
    table_name = "T_STATISTICAL_CUS_TOTAL_CS"
    
    insert_sql = f"""
        INSERT INTO {table_name} (
            STAT_DATE, STAT_TYPE, STAT_NAME, STAT_CODE, STAT_CONTENT_RAW, STAT_CONTENT_CLEANSE,
            ACC_A_CNY_AMOUNT, ACC_A_CNY_YOY, ACC_A_USD_AMOUNT, ACC_A_USD_YOY,
            MON_A_CNY_AMOUNT, MON_A_CNY_YOY, MON_A_USD_AMOUNT, MON_A_USD_YOY,
            ACC_E_CNY_AMOUNT, ACC_E_CNY_YOY, ACC_E_USD_AMOUNT, ACC_E_USD_YOY,
            MON_E_CNY_AMOUNT, MON_E_CNY_YOY, MON_E_USD_AMOUNT, MON_E_USD_YOY,
            ACC_I_CNY_AMOUNT, ACC_I_CNY_YOY, ACC_I_USD_AMOUNT, ACC_I_USD_YOY,
            MON_I_CNY_AMOUNT, MON_I_CNY_YOY, MON_I_USD_AMOUNT, MON_I_USD_YOY,
            ACC_E_AMOUNT, ACC_E_AMOUNT_UNIT, ACC_E_AMOUNT_YOY,
            MON_E_AMOUNT, MON_E_AMOUNT_UNIT, MON_E_AMOUNT_YOY,
            ACC_I_AMOUNT, ACC_I_AMOUNT_UNIT, ACC_I_AMOUNT_YOY,
            MON_I_AMOUNT, MON_I_AMOUNT_UNIT, MON_I_AMOUNT_YOY,
            RANK_MARKERS, DATA_SOURCE, EMPHASIS_OR_EMERGING_MARK, CREATE_TIME
        ) VALUES (
            CASE WHEN :1 IS NOT NULL THEN TO_DATE(:1, 'YYYY-MM-DD') ELSE NULL END,
            :2, :3, :4, :5, :6,
            :7, :8, :9, :10,
            :11, :12, :13, :14,
            :15, :16, :17, :18,
            :19, :20, :21, :22,
            :23, :24, :25, :26,
            :27, :28, :29, :30,
            :31, :32, :33,
            :34, :35, :36,
            :37, :38, :39,
            :40, :41, :42,
            :43, :44, :45,
            CASE WHEN :46 IS NOT NULL THEN TO_DATE(:46, 'YYYY-MM-DD HH24:MI:SS') ELSE NULL END
        )
        """
    
    print("=== SQL绑定变量分析 ===")
    print(f"完整SQL语句:")
    print(insert_sql)
    
    # 计算绑定变量
    import re
    
    # 查找所有的 :数字 模式
    bind_vars = re.findall(r':(\d+)', insert_sql)
    unique_bind_vars = sorted(set(int(var) for var in bind_vars))
    
    print(f"\n绑定变量统计:")
    print(f"总绑定变量引用次数: {len(bind_vars)}")
    print(f"唯一绑定变量数量: {len(unique_bind_vars)}")
    print(f"绑定变量范围: {min(unique_bind_vars)} - {max(unique_bind_vars)}")
    
    print(f"\n绑定变量详情:")
    for var in unique_bind_vars:
        count = bind_vars.count(str(var))
        print(f"  :{var} - 使用 {count} 次")
    
    # 检查是否有缺失的绑定变量
    expected_vars = set(range(1, max(unique_bind_vars) + 1))
    missing_vars = expected_vars - set(unique_bind_vars)
    
    if missing_vars:
        print(f"\n❌ 缺失的绑定变量: {sorted(missing_vars)}")
    else:
        print(f"\n✅ 绑定变量连续完整")
    
    # 特别检查CASE WHEN语句
    case_when_patterns = re.findall(r'CASE WHEN :(\d+) IS NOT NULL THEN.*?:(\d+).*?END', insert_sql, re.DOTALL)
    print(f"\nCASE WHEN语句分析:")
    for i, (var1, var2) in enumerate(case_when_patterns):
        print(f"  CASE WHEN #{i+1}: 使用绑定变量 :{var1} 和 :{var2}")
        if var1 != var2:
            print(f"    ❌ 警告: CASE WHEN中使用了不同的绑定变量 (:{var1} != :{var2})")
        else:
            print(f"    ✅ CASE WHEN中正确使用了相同的绑定变量 :{var1}")

def create_fixed_sql():
    """创建修复后的SQL语句"""
    
    table_name = "T_STATISTICAL_CUS_TOTAL_CS"
    
    # 修复后的SQL - 确保CASE WHEN中使用相同的绑定变量
    fixed_sql = f"""
        INSERT INTO {table_name} (
            STAT_DATE, STAT_TYPE, STAT_NAME, STAT_CODE, STAT_CONTENT_RAW, STAT_CONTENT_CLEANSE,
            ACC_A_CNY_AMOUNT, ACC_A_CNY_YOY, ACC_A_USD_AMOUNT, ACC_A_USD_YOY,
            MON_A_CNY_AMOUNT, MON_A_CNY_YOY, MON_A_USD_AMOUNT, MON_A_USD_YOY,
            ACC_E_CNY_AMOUNT, ACC_E_CNY_YOY, ACC_E_USD_AMOUNT, ACC_E_USD_YOY,
            MON_E_CNY_AMOUNT, MON_E_CNY_YOY, MON_E_USD_AMOUNT, MON_E_USD_YOY,
            ACC_I_CNY_AMOUNT, ACC_I_CNY_YOY, ACC_I_USD_AMOUNT, ACC_I_USD_YOY,
            MON_I_CNY_AMOUNT, MON_I_CNY_YOY, MON_I_USD_AMOUNT, MON_I_USD_YOY,
            ACC_E_AMOUNT, ACC_E_AMOUNT_UNIT, ACC_E_AMOUNT_YOY,
            MON_E_AMOUNT, MON_E_AMOUNT_UNIT, MON_E_AMOUNT_YOY,
            ACC_I_AMOUNT, ACC_I_AMOUNT_UNIT, ACC_I_AMOUNT_YOY,
            MON_I_AMOUNT, MON_I_AMOUNT_UNIT, MON_I_AMOUNT_YOY,
            RANK_MARKERS, DATA_SOURCE, EMPHASIS_OR_EMERGING_MARK, CREATE_TIME
        ) VALUES (
            CASE WHEN :1 IS NOT NULL THEN TO_DATE(:1, 'YYYY-MM-DD') ELSE NULL END,
            :2, :3, :4, :5, :6,
            :7, :8, :9, :10,
            :11, :12, :13, :14,
            :15, :16, :17, :18,
            :19, :20, :21, :22,
            :23, :24, :25, :26,
            :27, :28, :29, :30,
            :31, :32, :33,
            :34, :35, :36,
            :37, :38, :39,
            :40, :41, :42,
            :43, :44, :45,
            CASE WHEN :46 IS NOT NULL THEN TO_DATE(:46, 'YYYY-MM-DD HH24:MI:SS') ELSE NULL END
        )
        """
    
    print("\n=== 修复后的SQL ===")
    
    # 分析修复后的SQL
    import re
    bind_vars = re.findall(r':(\d+)', fixed_sql)
    unique_bind_vars = sorted(set(int(var) for var in bind_vars))
    
    print(f"修复后绑定变量统计:")
    print(f"总绑定变量引用次数: {len(bind_vars)}")
    print(f"唯一绑定变量数量: {len(unique_bind_vars)}")
    print(f"绑定变量范围: {min(unique_bind_vars)} - {max(unique_bind_vars)}")
    
    return fixed_sql

if __name__ == "__main__":
    analyze_sql_bindings()
    create_fixed_sql()
