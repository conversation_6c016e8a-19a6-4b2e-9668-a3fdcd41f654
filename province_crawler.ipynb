{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from DrissionPage import ChromiumPage,SessionPage\n", "import pandas as pd\n", "import time\n", "import os\n", "import re\n", "from datetime import datetime\n", "# 初始化浏览器\n", "page = ChromiumPage()\n", "\n", "# 地方海关列表\n", "LOCAL_CUSTOMS = [\n", "    '广州', '深圳', '北京', '江苏', '上海',\n", "    '浙江', '大连', '宁波', '青岛', '厦门'\n", "]\n", "\n", "\n", "\n", "def sanitize_filename(filename):\n", "    \"\"\"\n", "    去除文件名中的非法字符\n", "    :param filename: 原始文件名\n", "    :return: 处理后的文件名\n", "    \"\"\"\n", "    INVALID_CHARS_REGEX = r'[\\\\/*?:\"<>|]'\n", "\n", "    return re.sub(INVALID_CHARS_REGEX, '', filename)\n", "\n", "def download_file(url, save_path):\n", "    # 这里假设 SessionPage 是已经定义好的类\n", "    page = SessionPage()\n", "    res = page.download(url, save_path)\n", "    print(res)\n", "    return res\n", "\n", "\n", "# 各海关定制处理函数\n", "def get_guangzhou_data():\n", "    try:\n", "        # 访问指定URL\n", "        page.get('http://guangzhou.customs.gov.cn/guangzhou_customs/381558/fdzdgknr33/381638/381572/381573/index.html')\n", "        time.sleep(3)\n", "        \n", "        # 点击'缉私信息'\n", "        page.ele('text:缉私信息').click()\n", "        time.sleep(2)\n", "        \n", "        # 点击'本关统计'\n", "        page.ele('text:本关统计').click()\n", "        time.sleep(1)\n", "        \n", "        # 获取'广州关区所辖7地市进出口综合统计资料'链接并跳转\n", "        link = page.eles('text:广州关区所辖7地市进出口综合统计资料')[0].attr('href')\n", "        page.get(link)\n", "        time.sleep(3)\n", "        # 定位id为easysiteText的元素并获取最后一个a标签\n", "        container = page.ele('#easysiteText')\n", "        last_link = container.eles('tag:a')[-1]\n", "        \n", "        filename = sanitize_filename(last_link.text)\n", "\n", "        page.set.download_path(base_path)  # 设置文件保存路径\n", "        page.set.download_file_name(filename)  # 设置重命名文件名\n", "        last_link.click()  # 点击一个会触发下载的链接\n", "        page.wait.download_begin()  # 等待下载开始\n", "        page.wait.downloads_done()  # 等待下载结束\n", "        file_name  = os.path.join(base_path, filename)\n", "        return file_name\n", "        # 提取数据\n", "\n", "    except Exception as e:\n", "        print(f'获取广州海关数据时出错:', e)\n", "        return None\n", "\n", "\n", "def get_shanghai_data():\n", "    pass\n", "        \n", "def read_shanghai_excel(file_path):\n", "    if not os.path.exists(file_path):\n", "        print(f'文件不存在: {file_path}')\n", "        return None\n", "        \n", "    # 从文件名中提取年份和月份信息\n", "    filename = os.path.basename(file_path)\n", "    year_month_match = re.search(r'(\\d{4})年1至(\\d+)月', filename)\n", "    \n", "    if year_month_match:\n", "        year = year_month_match.group(1)\n", "        end_month = int(year_month_match.group(2))\n", "        is_february = (end_month == 2)\n", "    else:\n", "        # 如果无法从文件名解析，尝试检查文件内容\n", "        print(\"无法从文件名解析年份和月份，尝试从内容判断\")\n", "        is_february = True  # 默认为二月数据格式\n", "        year = datetime.now().year\n", "        end_month = 2\n", "        \n", "    print(f\"识别到文件对应月份: {end_month}月, 是否为2月数据: {is_february}\")\n", "        \n", "    # 读取Excel文件\n", "    try:\n", "        df = pd.read_excel(file_path, header=None)\n", "        print(\"表格结构:\", df.shape)\n", "        print(\"前几行:\")\n", "        for i in range(min(10, len(df))):\n", "            if i < len(df):\n", "                print(f\"行 {i}:\", df.iloc[i].tolist())\n", "    except Exception as e:\n", "        print(f\"读取Excel文件时出错: {e}\")\n", "        return None\n", "    \n", "    # 解析数据\n", "    result = {}\n", "    \n", "    # 找出表头行 (通常包含\"进出口\", \"出口\", \"进口\")\n", "    header_row = None\n", "    for i in range(min(10, len(df))):\n", "        if i >= len(df):\n", "            break\n", "        row_values = [str(x).strip() for x in df.iloc[i].tolist() if pd.notna(x)]\n", "        if len(row_values) >= 3 and '进出口' in row_values and '出口' in row_values and '进口' in row_values:\n", "            header_row = i\n", "            break\n", "    \n", "    if header_row is None:\n", "        print(\"无法在Excel中找到表头\")\n", "        return None\n", "    \n", "    print(f\"找到表头在第 {header_row} 行\")\n", "    \n", "    # 查找单位行 (包含\"人民币(亿)\"和\"同比\"等关键词)\n", "    unit_row = None\n", "    for i in range(header_row + 1, min(header_row + 5, len(df))):\n", "        if i >= len(df):\n", "            break\n", "        row_text = ' '.join([str(x) for x in df.iloc[i].tolist() if pd.notna(x)])\n", "        if '人民币(亿)' in row_text and '同比' in row_text:\n", "            unit_row = i\n", "            break\n", "    \n", "    if unit_row is None:\n", "        print(\"无法找到单位行\")\n", "        return None\n", "    \n", "    print(f\"找到单位行在第 {unit_row} 行\")\n", "    \n", "    # 查找数值行 (通常是单位行下一行)\n", "    value_row = unit_row + 1\n", "    if value_row >= len(df):\n", "        print(\"数值行超出表格范围\")\n", "        return None\n", "    \n", "    print(f\"数值行在第 {value_row} 行\")\n", "    \n", "    # 查找日期行 (通常在表头行和单位行之间)\n", "    date_row = header_row + 1\n", "    if date_row == unit_row:  # 如果表头和单位行相邻，可能没有专门的日期行\n", "        date_row = None\n", "    \n", "    if date_row is not None:\n", "        print(f\"日期行在第 {date_row} 行\")\n", "    else:\n", "        print(\"未找到日期行\")\n", "    \n", "    # 查找数据列 (包含进出口、出口、进口的列)\n", "    data_columns = []\n", "    for col in range(df.shape[1]):\n", "        header_cell = str(df.iloc[header_row, col]).strip()\n", "        if header_cell in ['进出口', '出口', '进口']:\n", "            if col < df.shape[1] and '人民币(亿)' in str(df.iloc[unit_row, col]):\n", "                data_columns.append({\n", "                    'type': header_cell,\n", "                    'column': col,\n", "                    'month_data': {},\n", "                    'total_data': {}\n", "                })\n", "    \n", "    if not data_columns:\n", "        print(\"未找到有效的数据列\")\n", "        return None\n", "    \n", "    print(f\"找到的数据列: {[col['type'] for col in data_columns]}\")\n", "    \n", "    # 对于2月数据，当月数据和累计数据相同\n", "    if is_february:\n", "        for col_info in data_columns:\n", "            col = col_info['column']\n", "            # 找到对应的同比列\n", "            growth_col = None\n", "            for c in range(col, min(col + 3, df.shape[1])):\n", "                if c < df.shape[1] and '同比' in str(df.iloc[unit_row, c]):\n", "                    growth_col = c\n", "                    break\n", "            \n", "            if growth_col is None:\n", "                print(f\"未找到 {col_info['type']} 的同比列\")\n", "                continue\n", "            \n", "            try:\n", "                # 提取数值\n", "                value = df.iloc[value_row, col]\n", "                growth = df.iloc[value_row, growth_col]\n", "                \n", "                # 确保值是数字\n", "                if isinstance(value, str):\n", "                    value = float(value.replace(',', ''))\n", "                if isinstance(growth, str):\n", "                    growth = float(growth.replace('%', '').replace(',', ''))\n", "                \n", "                # 创建数据字典\n", "                data = {\n", "                    \"日期\": f\"{year}年1-{end_month}月\",\n", "                    \"人民币(亿)\": value,\n", "                    \"人民币同比(%)\": growth\n", "                }\n", "                \n", "                # 对于2月数据，月度和累计相同\n", "                col_info['month_data'] = data\n", "                col_info['total_data'] = data\n", "                \n", "                print(f\"提取到 {col_info['type']} 数据: 值={value}, 增长率={growth}\")\n", "            except Exception as e:\n", "                print(f\"处理 {col_info['type']} 数据时出错: {e}\")\n", "    else:\n", "        # 对于非2月数据，需要分别提取月度和累计数据\n", "        for col_info in data_columns:\n", "            header_type = col_info['type']\n", "            base_col = col_info['column']\n", "            \n", "            # 根据Excel结构，确定月度和累计列的位置\n", "            # 通常布局：第1、2列是当月数据，第3、4列是累计数据\n", "            if header_type == '进出口':\n", "                month_value_col = 0  # 当月值\n", "                month_growth_col = 1  # 当月增长率\n", "                total_value_col = 2  # 累计值\n", "                total_growth_col = 3  # 累计增长率\n", "            elif header_type == '出口':\n", "                month_value_col = 4  # 当月值\n", "                month_growth_col = 5  # 当月增长率\n", "                total_value_col = 6  # 累计值\n", "                total_growth_col = 7  # 累计增长率\n", "            elif header_type == '进口':\n", "                month_value_col = 8  # 当月值\n", "                month_growth_col = 9  # 当月增长率\n", "                total_value_col = 10  # 累计值\n", "                total_growth_col = 11  # 累计增长率\n", "                \n", "            print(f\"{header_type} - 月度列: {month_value_col}, {month_growth_col} | 累计列: {total_value_col}, {total_growth_col}\")\n", "            \n", "            # 提取月度数据\n", "            if month_value_col is not None and month_growth_col is not None:\n", "                try:\n", "                    # 提取数值\n", "                    value = df.iloc[value_row, month_value_col]\n", "                    growth = df.iloc[value_row, month_growth_col]\n", "                    \n", "                    # 确保值是数字\n", "                    if isinstance(value, str):\n", "                        value = float(value.replace(',', ''))\n", "                    if isinstance(growth, str):\n", "                        growth = float(growth.replace('%', '').replace(',', ''))\n", "                    \n", "                    # 创建数据字典\n", "                    col_info['month_data'] = {\n", "                        \"日期\": f\"{year}年{end_month}月\",\n", "                        \"人民币(亿)\": value,\n", "                        \"人民币同比(%)\": growth\n", "                    }\n", "                    \n", "                    print(f\"提取到 {header_type} 月度数据: 值={value}, 增长率={growth}\")\n", "                except Exception as e:\n", "                    print(f\"处理 {header_type} 月度数据时出错: {e}\")\n", "                    col_info['month_data'] = {\"日期\": f\"{year}年{end_month}月\", \"人民币(亿)\": 0, \"人民币同比(%)\": 0}\n", "            \n", "            # 提取累计数据\n", "            if total_value_col is not None and total_growth_col is not None:\n", "                try:\n", "                    # 提取数值\n", "                    value = df.iloc[value_row, total_value_col]\n", "                    growth = df.iloc[value_row, total_growth_col]\n", "                    \n", "                    # 确保值是数字\n", "                    if isinstance(value, str):\n", "                        value = float(value.replace(',', ''))\n", "                    if isinstance(growth, str):\n", "                        growth = float(growth.replace('%', '').replace(',', ''))\n", "                    \n", "                    # 创建数据字典\n", "                    col_info['total_data'] = {\n", "                        \"日期\": f\"{year}年1-{end_month}月\",\n", "                        \"人民币(亿)\": value,\n", "                        \"人民币同比(%)\": growth\n", "                    }\n", "                    \n", "                    print(f\"提取到 {header_type} 累计数据: 值={value}, 增长率={growth}\")\n", "                except Exception as e:\n", "                    print(f\"处理 {header_type} 累计数据时出错: {e}\")\n", "                    col_info['total_data'] = {\"日期\": f\"{year}年1-{end_month}月\", \"人民币(亿)\": 0, \"人民币同比(%)\": 0}\n", "            \n", "            # 如果只有累计数据，用累计数据填充月度数据\n", "            if not col_info['month_data'] and col_info['total_data']:\n", "                col_info['month_data'] = col_info['total_data']\n", "                print(f\"{header_type} 使用累计数据作为月度数据\")\n", "            \n", "            # 如果只有月度数据，用月度数据填充累计数据\n", "            if not col_info['total_data'] and col_info['month_data']:\n", "                col_info['total_data'] = col_info['month_data']\n", "                print(f\"{header_type} 使用月度数据作为累计数据\")\n", "    \n", "    # 按类型整理数据\n", "    for col_info in data_columns:\n", "        result[col_info['type']] = {\n", "            \"当月\": col_info['month_data'],\n", "            \"累计\": col_info['total_data']\n", "        }\n", "    \n", "    print(\"解析结果:\", result)\n", "    \n", "    # 转换为指定格式的两行数据\n", "    data_rows = pd.DataFrame({\n", "        '类型': ['累计', '当月'],\n", "        '进出口额（人民币）': [\n", "            round(float(result.get('进出口', {}).get('累计', {}).get('人民币(亿)', 0) or 0), 1),\n", "            round(float(result.get('进出口', {}).get('当月', {}).get('人民币(亿)', 0) or 0), 1)\n", "        ],\n", "        '出口额（人民币）': [\n", "            round(float(result.get('出口', {}).get('累计', {}).get('人民币(亿)', 0) or 0), 1),\n", "            round(float(result.get('出口', {}).get('当月', {}).get('人民币(亿)', 0) or 0), 1)\n", "        ],\n", "        '进口额（人民币）': [\n", "            round(float(result.get('进口', {}).get('累计', {}).get('人民币(亿)', 0) or 0), 1),\n", "            round(float(result.get('进口', {}).get('当月', {}).get('人民币(亿)', 0) or 0), 1)\n", "        ],\n", "        '进出口同比(%)': [\n", "            round(float(result.get('进出口', {}).get('累计', {}).get('人民币同比(%)', 0) or 0), 1),\n", "            round(float(result.get('进出口', {}).get('当月', {}).get('人民币同比(%)', 0) or 0), 1)\n", "        ],\n", "        '出口同比(%)': [\n", "            round(float(result.get('出口', {}).get('累计', {}).get('人民币同比(%)', 0) or 0), 1),\n", "            round(float(result.get('出口', {}).get('当月', {}).get('人民币同比(%)', 0) or 0), 1)\n", "        ],\n", "        '进口同比(%)': [\n", "            round(float(result.get('进口', {}).get('累计', {}).get('人民币同比(%)', 0) or 0), 1),\n", "            round(float(result.get('进口', {}).get('当月', {}).get('人民币同比(%)', 0) or 0), 1)\n", "        ]\n", "    })\n", "    \n", "    # 打印完整结果\n", "    print(\"\\n最终数据:\")\n", "    with pd.option_context('display.max_rows', None, 'display.max_columns', None, 'display.width', 1000):\n", "        print(data_rows)\n", "    return data_rows\n", "\n", "    \n", "def read_guangzhou_excel(file_path):\n", "    try:\n", "        if not os.path.exists(file_path):\n", "            print(f'文件不存在: {file_path}')\n", "            return None\n", "            \n", "        df = pd.read_excel(file_path)\n", "        # 筛选包含'广东省广州市'的行\n", "        result = df[df.apply(lambda row: row.astype(str).str.contains('广东省广州市').any(), axis=1)]\n", "        \n", "        # 提取累计数据\n", "        total_import_export = round(float(result.iloc[0]['进出口']) / 10000, 1)\n", "        total_export = round(float(result.iloc[0]['出口']) / 10000, 1)\n", "        total_import = round(float(result.iloc[0]['进口']) / 10000, 1)\n", "        \n", "        # 提取累计同比数据\n", "        total_import_export_yoy = round(float(result.iloc[0]['进出口.1']), 1)\n", "        total_export_yoy = round(float(result.iloc[0]['出口.1']), 1)\n", "        total_import_yoy = round(float(result.iloc[0]['进口.1']), 1)\n", "        \n", "        # 获取当前月份\n", "        current_month = datetime.now().month -1\n", "        \n", "        # 1月和2月时当月数据等于累计数据\n", "        if current_month in [2]:\n", "            monthly_import_export = total_import_export\n", "            monthly_export = total_export\n", "            monthly_import = total_import\n", "            monthly_import_export_yoy = total_import_export_yoy\n", "            monthly_export_yoy = total_export_yoy\n", "            monthly_import_yoy = total_import_yoy\n", "        else:\n", "            # 提取当月数据\n", "            monthly_import_export = round(float(result.iloc[0]['进出口.4']) / 10000, 1)\n", "            monthly_export = round(float(result.iloc[0]['出口.4']) / 10000, 1)\n", "            monthly_import = round(float(result.iloc[0]['进口.4']) / 10000, 1)\n", "            \n", "            # 提取当月同比数据\n", "            monthly_import_export_yoy = round(float(result.iloc[0]['进出口.5']), 1)\n", "            monthly_export_yoy = round(float(result.iloc[0]['出口.5']), 1)\n", "            monthly_import_yoy = round(float(result.iloc[0]['进口.5']), 1)\n", "        \n", "        # 将数据整理成DataFrame\n", "        data_rows = pd.DataFrame({\n", "            '类型': ['累计', '当月'],\n", "            '进出口额(万元)': [total_import_export, monthly_import_export],\n", "            '出口额(万元)': [total_export, monthly_export],\n", "            '进口额(万元)': [total_import, monthly_import],\n", "            '进出口同比(%)': [total_import_export_yoy, monthly_import_export_yoy],\n", "            '出口同比(%)': [total_export_yoy, monthly_export_yoy],\n", "            '进口同比(%)': [total_import_yoy, monthly_import_yoy]\n", "        })\n", "        \n", "        print(data_rows)\n", "        return data_rows\n", "    except Exception as e:\n", "        print(f'读取广州海关Excel文件时出错:', e)\n", "        return None\n", "\n", "\n", "def get_beijing_data():\n", "    try:\n", "        # 访问指定URL\n", "        page.get('http://beijing.customs.gov.cn/beijing_customs/434756/434804/2941702/434773/434774/index.html')\n", "        time.sleep(3)\n", "        \n", "        # 查找'北京地区进出口商品总值表B：月度表'链接\n", "        table_link = None\n", "        for link in page.eles('tag:a'):\n", "            if '北京地区进出口商品总值表B：月度表' in link.text:\n", "                table_link = link.attr('href')\n", "                break\n", "        \n", "        # 如果没有找到，点击下一页继续查找\n", "        if not table_link:\n", "            next_page = page.ele('text:下一页')\n", "            if next_page:\n", "                next_page.click()\n", "                time.sleep(3)\n", "                for link in page.eles('tag:a'):\n", "                    if '北京地区进出口商品总值表B：月度表' in link.text:\n", "                        table_link = link.attr('href')\n", "                        break\n", "        \n", "        # 如果找到链接则跳转\n", "        if table_link:\n", "            page.get(table_link)\n", "            time.sleep(1)\n", "            container = page.ele('#easysiteText')\n", "            last_link = container.eles('tag:a')[-1]\n", "            \n", "            filename = sanitize_filename(last_link.text)\n", "\n", "            page.set.download_path(base_path)  # 设置文件保存路径\n", "            page.set.download_file_name(filename)  # 设置重命名文件名\n", "            last_link.click()  # 点击一个会触发下载的链接\n", "            page.wait.download_begin()  # 等待下载开始\n", "            page.wait.downloads_done()  # 等待下载结束\n", "            file_name  = os.path.join(base_path, filename)\n", "\n", "            return file_name\n", "        else:\n", "            print('未找到北京地区进出口商品总值表B：月度表')\n", "            return None\n", "            \n", "    except Exception as e:\n", "        print(f'获取北京海关数据时出错:', e)\n", "        return None\n", "\n", "def get_jiangsu_data():\n", "    page.get('http://nj12360.nj-customs.com/njsdq/DateIndex.aspx')\n", "    \n", "    # 获取累计数据\n", "    table = page.ele('#tblMonth')\n", "    active_inputs = table.eles('tag:input@!disabled')\n", "    print(len(active_inputs))\n", "    active_inputs[-1].click()\n", "    time.sleep(2)\n", "\n", "    # 提取累计表格数据\n", "    total_data = {\n", "        '进出口总值': float(page.ele('xpath://tr[2]/td[2]').text),\n", "        '进出口同比': float(page.ele('xpath://tr[2]/td[3]').text),\n", "        '出口值': float(page.ele('xpath://tr[3]/td[2]').text),\n", "        '出口同比': float(page.ele('xpath://tr[3]/td[3]').text),\n", "        '进口值': float(page.ele('xpath://tr[4]/td[2]').text),\n", "        '进口同比': float(page.ele('xpath://tr[4]/td[3]').text)\n", "    }\n", "    \n", "    # 执行JS切换下拉菜单获取当月数据\n", "    js_code = \"\"\"\n", "    const select = document.getElementById('ddlTableList');\n", "    select.value = 'T_NJWX_IE_CUR';\n", "    select.dispatchEvent(new Event('change'));\n", "    \"\"\"\n", "    page.run_js(js_code)\n", "    time.sleep(3)\n", "    \n", "    # 提取当月表格数据\n", "    monthly_data = {\n", "        '进出口总值': float(page.ele('xpath://tr[2]/td[2]').text),\n", "        '进出口同比': float(page.ele('xpath://tr[2]/td[3]').text),\n", "        '出口值': float(page.ele('xpath://tr[3]/td[2]').text),\n", "        '出口同比': float(page.ele('xpath://tr[3]/td[3]').text),\n", "        '进口值': float(page.ele('xpath://tr[4]/td[2]').text),\n", "        '进口同比': float(page.ele('xpath://tr[4]/td[3]').text)\n", "    }\n", "    \n", "    # 如果是2月数据（active_inputs长度为2），当月数据使用累计数据\n", "    if len(active_inputs) == 2:\n", "        monthly_data = total_data\n", "        \n", "    # 整理成DataFrame\n", "    df = pd.DataFrame({\n", "        '类型': ['累计', '当月'],\n", "        '进出口额(万元)': [total_data['进出口总值'], monthly_data['进出口总值']],\n", "        '出口额(万元)': [total_data['出口值'], monthly_data['出口值']],\n", "        '进口额(万元)': [total_data['进口值'], monthly_data['进口值']],\n", "        '进出口同比(%)': [total_data['进出口同比'], monthly_data['进出口同比']],\n", "        '出口同比(%)': [total_data['出口同比'], monthly_data['出口同比']],\n", "        '进口同比(%)': [total_data['进口同比'], monthly_data['进口同比']]\n", "    })\n", "    print(df)\n", "    return df\n", "\n", "\n", "    \n", "\n", "def get_zhejiang_data():\n", "    try:\n", "        page.get('http://zhejiang.customs.gov.cn')\n", "        time.sleep(3)\n", "        table = page.ele('tag:table@class:zhejiang_table')\n", "        return pd.read_html(table.html)[0]\n", "    except Exception as e:\n", "        print(f'获取浙江海关数据时出错:', e)\n", "        return None\n", "        \n", "def read_national_excel(file_path):\n", "    \"\"\"\n", "    读取全国进出口数据Excel文件\n", "    :param file_path: Excel文件路径\n", "    :return: 包含累计和当月数据的DataFrame\n", "    \"\"\"\n", "    if not os.path.exists(file_path):\n", "        print(f'文件不存在: {file_path}')\n", "        return None\n", "        \n", "    # 读取Excel文件，指定读取范围B4:J54\n", "    df = pd.read_excel(file_path, header=None, skiprows=4, usecols='B:J', nrows=50)\n", "\n", "    df.columns = ['年月', '进出口', '出口', '进口', '贸易差额',\n", "                 '1至当月累计进出口', '1至当月累计出口','1至当月累计进口','1至当月累计贸易差额']\n", "    print('columns  df',df)\n", "    # 找到年月列中最后一个有数值的行\n", "    valid_rows = df[df['年月'].notna() & ~df['年月'].astype(str).str.contains('---')]\n", "    if len(valid_rows) == 0:\n", "        print('未找到有效数据行')\n", "        return None\n", "        \n", "    last_row = valid_rows.iloc[-1]\n", "    next_row = df.iloc[last_row.name + 1] if last_row.name + 1 < len(df) else last_row\n", "    print('last_row',last_row)\n", "    print('next_row',next_row)\n", "    \n", "    # 解析年月格式(如2025.02)判断是否为2月\n", "    is_february = False\n", "    if '.' in str(last_row['年月']):\n", "        month_part = str(last_row['年月']).split('.')[1]\n", "        if month_part.startswith('02') or month_part == '2':\n", "            is_february = True\n", "    \n", "    # 从last_row和next_row直接提取数据并四舍五入\n", "    total_import_export = round(float(last_row['1至当月累计进出口']), 1)\n", "    total_export = round(float(last_row['1至当月累计出口']), 1)\n", "    total_import = round(float(last_row['1至当月累计进口']), 1)\n", "    \n", "    total_import_export_yoy = round(float(next_row['1至当月累计进出口']), 1)\n", "    total_export_yoy = round(float(next_row['1至当月累计出口']), 1)\n", "    total_import_yoy = round(float(next_row['1至当月累计进口']), 1)\n", "    \n", "    monthly_import_export = round(float(last_row['进出口']), 1)\n", "    monthly_export = round(float(last_row['出口']), 1)\n", "    monthly_import = round(float(last_row['进口']), 1)\n", "    \n", "    monthly_import_export_yoy = round(float(next_row['进出口']), 1)\n", "    monthly_export_yoy = round(float(next_row['出口']), 1)\n", "    monthly_import_yoy = round(float(next_row['进口']), 1)\n", "    \n", "    # 如果是2月，当月数据使用累计数据\n", "    if is_february:\n", "        print('是2月')\n", "        monthly_import_export = total_import_export\n", "        monthly_export = total_export\n", "        monthly_import = total_import\n", "        monthly_import_export_yoy = total_import_export_yoy\n", "        monthly_export_yoy = total_export_yoy\n", "        monthly_import_yoy = total_import_yoy\n", "    \n", "    # 整理成DataFrame\n", "    data_rows = pd.DataFrame({\n", "        '类型': ['累计', '当月'],\n", "        '进出口额(万元)': [total_import_export, monthly_import_export],\n", "        '出口额(万元)': [total_export, monthly_export],\n", "        '进口额(万元)': [total_import, monthly_import],\n", "        '进出口同比(%)': [total_import_export_yoy, monthly_import_export_yoy],\n", "        '出口同比(%)': [total_export_yoy, monthly_export_yoy],\n", "        '进口同比(%)': [total_import_yoy, monthly_import_yoy]\n", "    })\n", "    \n", "    print(data_rows)\n", "    return data_rows\n", "\n", "\n", "def read_beijing_excel(file_path):\n", "    if not os.path.exists(file_path):\n", "        print(f'文件不存在: {file_path}')\n", "        return None\n", "        \n", "    # 获取当前日期信息\n", "    now = datetime.now()\n", "    current_year = now.year\n", "    current_month = now.month - 2\n", "    \n", "    # 读取Excel文件\n", "    df = pd.read_excel(file_path)\n", "    \n", "    # 筛选当前年份的所有数据\n", "    year_data = df[df.apply(lambda row: row.astype(str).str.contains(f'{current_year}').any(), axis=1)]\n", "    # 添加列名\n", "    year_data.columns = ['年月日', '人民币(万)', '人民币同比(%)', '美元值(千)', '美元值同比(%)', \n", "                      '人民币(万)', '人民币同比(%)', '美元值(千)', '美元值同比(%)', \n", "                      '人民币(万)', '人民币同比(%)', '美元值(千)', '美元值同比(%)']\n", "    print(year_data)\n", "    \n", "    if len(year_data) == 0:\n", "        print('未找到当前年份的北京地区相关数据')\n", "        return None\n", "        \n", "    # 计算今年累计值（1月至当前月份）\n", "    total_import_export = round(year_data.iloc[:,1].sum() / 10000, 1)  # 第一组人民币(万)是进出口\n", "    total_export = round(year_data.iloc[:,5].sum() / 10000, 1)  # 第二组人民币(万)是出口\n", "    total_import = round(year_data.iloc[:,9].sum() / 10000, 1)  # 第三组人民币(万)是进口\n", "    \n", "    print(total_import_export, total_export, total_import)\n", "    # 计算去年累计同比值（去年1月至去年的当前月份）\n", "    last_year_data = df[df.apply(lambda row: row.astype(str).str.contains(f'{current_year-1}').any(), axis=1)]\n", "    last_year_data = last_year_data[:len(year_data)]\n", "    # 添加列名\n", "    last_year_data.columns = ['年月日', '人民币(万)', '人民币同比(%)', '美元值(千)', '美元值同比(%)', \n", "                      '人民币(万)', '人民币同比(%)', '美元值(千)', '美元值同比(%)', \n", "                      '人民币(万)', '人民币同比(%)', '美元值(千)', '美元值同比(%)']\n", "    # 计算去年累计值（1月至当前月份）\n", "    last_total_import_export = round(last_year_data.iloc[:,1].sum() / 10000, 1)  # 第一组人民币(万)是进出口\n", "    last_total_export = round(last_year_data.iloc[:,5].sum() / 10000, 1)  # 第二组人民币(万)是出口\n", "    last_total_import = round(last_year_data.iloc[:,9].sum() / 10000, 1)  # 第三组人民币(万)是进口\n", "    print(last_total_import_export, last_total_export, last_total_import)\n", "\n", "    # 计算累计的同比值\n", "    total_import_export_yoy = round((total_import_export - last_total_import_export) / last_total_import_export * 100, 1)\n", "    total_export_yoy = round((total_export - last_total_export) / last_total_export * 100, 1)\n", "    total_import_yoy = round((total_import - last_total_import) / last_total_import * 100, 1)\n", "    # 去除小数点后的零\n", "    total_import_export_yoy = int(total_import_export_yoy) if total_import_export_yoy == int(total_import_export_yoy) else total_import_export_yoy\n", "    total_export_yoy = int(total_export_yoy) if total_export_yoy == int(total_export_yoy) else total_export_yoy\n", "    total_import_yoy = int(total_import_yoy) if total_import_yoy == int(total_import_yoy) else total_import_yoy\n", "    print(total_import_export_yoy, total_export_yoy, total_import_yoy)\n", "\n", "    \n", "    # 获取最后一个月数据\n", "    last_month_data = year_data.iloc[-1]\n", "    monthly_import_export = round(float(last_month_data[1]) / 10000, 1)  # 进出口\n", "    monthly_export = round(float(last_month_data[5]) / 10000, 1)  # 出口\n", "    monthly_import = round(float(last_month_data[9]) / 10000, 1)  # 进口\n", "    \n", "    # 获取同比数据\n", "    monthly_import_export_yoy = round(float(last_month_data[2]), 1)\n", "    monthly_export_yoy = round(float(last_month_data[6]), 1)\n", "    monthly_import_yoy = round(float(last_month_data[10]), 1)\n", "    \n", "    # 去除小数点后的零\n", "    monthly_import_export_yoy = int(monthly_import_export_yoy) if monthly_import_export_yoy == int(monthly_import_export_yoy) else monthly_import_export_yoy\n", "    monthly_export_yoy = int(monthly_export_yoy) if monthly_export_yoy == int(monthly_export_yoy) else monthly_export_yoy\n", "    monthly_import_yoy = int(monthly_import_yoy) if monthly_import_yoy == int(monthly_import_yoy) else monthly_import_yoy\n", "        \n", "\n", "    \n", "    # 如果是---或2月，累计数据是当月数据\n", "    if current_month in [2]:\n", "        print('累计和当月一样的')\n", "        monthly_import_export = total_import_export\n", "        monthly_export = total_export\n", "        monthly_import = total_import\n", "        monthly_import_export_yoy = total_import_export_yoy\n", "        monthly_export_yoy = total_export_yoy\n", "        monthly_import_yoy = total_import_yoy\n", "    else:\n", "        # 其他月份需要累加1月至当前月份的数据来计算累计值\n", "        print('累计和当月不一样的')\n", "        # total_import_export = total_import_export\n", "        # total_export = total_export\n", "        # total_import = total_import\n", "        # total_import_export_yoy = total_import_export_yoy\n", "        # total_export_yoy = total_export_yoy\n", "        # total_import_yoy = total_import_yoy\n", "    \n", "    # 将数据整理成DataFrame\n", "    data_rows = pd.DataFrame({\n", "        '类型': ['累计', '当月'],\n", "        '进出口额(万元)': [total_import_export, monthly_import_export],\n", "        '出口额(万元)': [total_export, monthly_export],\n", "        '进口额(万元)': [total_import, monthly_import],\n", "        '进出口同比(%)': [total_import_export_yoy, monthly_import_export_yoy],\n", "        '出口同比(%)': [total_export_yoy, monthly_export_yoy],\n", "        '进口同比(%)': [total_import_yoy, monthly_import_yoy]\n", "    })\n", "    \n", "    print(data_rows)\n", "    return data_rows\n", "        \n", "\n", "# 获取海关总署数据\n", "def get_headquarters_data():\n", "    # 访问总署网站\n", "    page.get('http://www.customs.gov.cn/customs/302249/zfxxgk/2799825/302274/302277/6348926/index.html')\n", "    time.sleep(3)\n", "    \n", "    # 定位'年进出口商品总值表 B:月度表'元素\n", "    monthly_table = page.ele('text:年进出口商品总值表 B:月度表')\n", "    \n", "    # 获取后一个元素中的a标签\n", "    next_element = monthly_table.next()\n", "    print(next_element)\n", "    links = next_element.eles('tag:a')\n", "    \n", "    # 获取所有包含href的链接并选择最后一个跳转\n", "    href_links = [link for link in links if link.attr('href')]\n", "    if href_links:\n", "        last_link = href_links[-1].attr('href')\n", "        page.get(last_link)\n", "\n", "        # 点击'下载'按钮\n", "        download_links = page.eles('text:下载')\n", "        if download_links:\n", "            last_link = download_links[-1]\n", "            \n", "            # 获取标题作为文件名\n", "            title_element = page.ele('xpath:/html/body/div[4]/div/div[2]/div/div/div[1]/h2')\n", "            if title_element:\n", "                filename = sanitize_filename(title_element.text)\n", "                page.set.download_path(base_path)\n", "                page.set.download_file_name(filename)\n", "                last_link.click()\n", "                page.wait.download_begin()\n", "                page.wait.downloads_done()\n", "                file_name = os.path.join(base_path, filename)\n", "                print(file_name)\n", "                return file_name\n", "    \n", "    \n", "\n", "\n", "# 执行爬取\n", "base_path  = os.getcwd()\n", "# 今天的日期\n", "today = datetime.now().strftime(\"%Y%m%d\")\n", "base_path = os.path.join(base_path, today)\n", "if not os.path.exists(base_path):\n", "    os.mkdir(base_path)\n", "\n", "if __name__ == '__main__':\n", "    # 广州的数据\n", "    #gz_data = get_guangzhou_data()\n", "    #read_guangzhou_excel(r'C:\\Users\\<USER>\\Desktop\\海关数据\\小超海关统计\\全国进出口\\20250416\\2025年2月广州关区所辖7地市进出口综合统计资料.xls')\n", "    pass\n", "\n", "\n", "    # 北京的数据\n", "    #bj_data = get_beijing_data()\n", "    # read_beijing_excel(r'C:\\Users\\<USER>\\Desktop\\海关数据\\小超海关统计\\全国进出口\\20250416\\（1）北京地区进出口商品总值表B：月度表（2025年1-2月）.xls')\n", "    \n", "    # 江苏的数据\n", "    #js_data = get_jiangsu_data()    \n", "\n", "    # 全国数据\n", "    # file_name = get_headquarters_data()\n", "    # file_name = r'C:\\Users\\<USER>\\Desktop\\海关数据\\小超海关统计\\全国进出口\\20250416\\（1）进出口商品总值表(人民币值) B月度表.xls'\n", "    # read_national_excel(file_name)\n", "\n", "    # 上海\n", "    # shanghai_data = get_shanghai_data()\n", "    # page.get('http://shanghai.customs.gov.cn/shanghai_customs/423405/fdzdgknr8/423468/1879297/6348726/6348730/index.html')\n", "    # table_link = None\n", "    # for link in page.eles('tag:a'):\n", "    #     if '进出口商品总值表（人民币值）' in link.text:\n", "    #         table_link = link.attr('href')\n", "    #         break\n", "    \n", "    # # 如果没有找到，点击下一页继续查找\n", "    # if not table_link:\n", "    #     next_page = page.eles('text:下一页')[-1]\n", "    #     if next_page:\n", "    #         next_page.click()\n", "    #         time.sleep(3)\n", "    #         for link in page.eles('tag:a'):\n", "    #             if '进出口商品总值表（人民币值）' in link.text:\n", "                    \n", "    #                 filename = sanitize_filename(link.text)\n", "    #                 page.set.download_path(base_path)\n", "    #                 page.set.download_file_name(filename)\n", "    #                 link.click()\n", "                    # break\n", "    # read_shanghai_excel(r'C:\\Users\\<USER>\\Desktop\\海关数据\\小超海关统计\\全国进出口\\20250416\\上海本市：2025年1至2月进出口商品总值表（人民币值）.xls')\n", "    \n", "\n", "    \n", "    # # 获取地方海关数据\n", "    # local_data = get_local_customs_data()\n", "    # print('地方海关数据已保存到data目录')\n", "    \n", "    # # 获取总署数据\n", "    # hq_data = get_headquarters_data()\n", "    # hq_data['current_month'].to_excel('data/headquarters_current_month.xlsx')\n", "    # hq_data['last_year'].to_excel('data/headquarters_last_year.xlsx')\n", "    # print('海关总署数据已保存到data目录')\n", "    "]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["找到的链接: http://hangzhou.customs.gov.cn/hangzhou_customs/575609/zlbd/575612/575612/6430241/6430315/index.html\n", "已成功跳转到统计数据页面\n"]}], "source": ["# 现在写 浙江的爬虫逻辑\n", "page.get('http://hangzhou.customs.gov.cn/hangzhou_customs/575609/zlbd/575612/575613/index.html')\n", "time.sleep(3)\n", "stats_link = page.ele('text:统计数据').attr('href')\n", "print(f\"找到的链接: {stats_link}\")\n", "page.get(stats_link)\n", "\n", "# 可以继续进行后续操作\n", "time.sleep(2)\n", "print(\"已成功跳转到统计数据页面\")\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "\n", "\n", "\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}