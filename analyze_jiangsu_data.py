#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
江苏省进出口贸易方式数据分析脚本
分析江苏省的一般贸易、加工贸易和保税贸易数据
"""

import pandas as pd
import os
from datetime import datetime

def analyze_jiangsu_trade_data(file_path):
    """
    分析江苏省贸易方式数据
    
    Args:
        file_path: Excel文件路径
    
    Returns:
        dict: 包含分析结果的字典
    """
    try:
        # 读取贸易方式sheet
        df = pd.read_excel(file_path, sheet_name='贸易方式', header=None)
        print(f"数据形状: {df.shape}")
        
        # 根据用户提供的结构，数据从第4行开始（索引3）
        # 列结构：
        # 0: 贸易方式2位
        # 1-6: 当月数据（进出口、出口、进口的人民币和美元值）
        # 7-18: 累计数据（进出口、出口、进口的人民币、同比、美元、同比）
        
        results = {
            '当月': {},
            '累计': {}
        }
        
        # 查找关键贸易方式行
        general_trade_row = None
        processing_trade_row = None
        bonded_trade_row = None
        total_row = None
        
        for i in range(len(df)):
            cell_value = str(df.iloc[i, 0]).strip()
            
            if '总值' in cell_value and not cell_value.startswith('　'):
                total_row = i
                print(f"找到总值行: {i} - {cell_value}")
            elif '一般贸易' in cell_value:
                general_trade_row = i
                print(f"找到一般贸易行: {i} - {cell_value}")
            elif '加工贸易' in cell_value and not '来料' in cell_value and not '进料' in cell_value and not '出料' in cell_value and not '设备' in cell_value:
                processing_trade_row = i
                print(f"找到加工贸易行: {i} - {cell_value}")
            elif '保税物流' in cell_value:
                bonded_trade_row = i
                print(f"找到保税物流行: {i} - {cell_value}")
        
        def extract_trade_data(row_index, trade_name):
            """提取贸易数据"""
            if row_index is None:
                return None
                
            row_data = df.iloc[row_index]
            
            # 当月数据（人民币亿元）
            monthly_import_export = safe_float(row_data.iloc[1])  # 进出口
            monthly_export = safe_float(row_data.iloc[3])         # 出口
            monthly_import = safe_float(row_data.iloc[5])         # 进口
            
            # 累计数据（人民币亿元和同比）
            cumulative_import_export = safe_float(row_data.iloc[7])  # 累计进出口
            cumulative_import_export_yoy = safe_float(row_data.iloc[8])  # 累计进出口同比
            cumulative_export = safe_float(row_data.iloc[11])        # 累计出口
            cumulative_export_yoy = safe_float(row_data.iloc[12])    # 累计出口同比
            cumulative_import = safe_float(row_data.iloc[15])        # 累计进口
            cumulative_import_yoy = safe_float(row_data.iloc[16])    # 累计进口同比
            
            return {
                '当月': {
                    '进出口额': monthly_import_export,
                    '出口额': monthly_export,
                    '进口额': monthly_import
                },
                '累计': {
                    '进出口额': cumulative_import_export,
                    '进出口同比': cumulative_import_export_yoy,
                    '出口额': cumulative_export,
                    '出口同比': cumulative_export_yoy,
                    '进口额': cumulative_import,
                    '进口同比': cumulative_import_yoy
                }
            }
        
        def safe_float(value):
            """安全转换为浮点数"""
            try:
                if pd.isna(value) or value == '' or value == '--':
                    return 0.0
                if isinstance(value, str):
                    value = value.replace(',', '').replace('--', '0')
                return float(value)
            except (ValueError, TypeError):
                return 0.0
        
        # 提取各贸易方式数据
        total_data = extract_trade_data(total_row, '总值')
        general_data = extract_trade_data(general_trade_row, '一般贸易')
        processing_data = extract_trade_data(processing_trade_row, '加工贸易')
        bonded_data = extract_trade_data(bonded_trade_row, '保税贸易')
        
        # 计算占比
        def calculate_proportion(trade_data, total_data, period):
            """计算占比"""
            if trade_data and total_data and total_data[period]['进出口额'] > 0:
                return round((trade_data[period]['进出口额'] / total_data[period]['进出口额']) * 100, 1)
            return 0.0
        
        # 组织结果
        def merge_data_with_proportion(trade_data, period, total_data):
            """合并贸易数据和占比"""
            if trade_data and trade_data.get(period):
                result = trade_data[period].copy()
                result['占比'] = calculate_proportion(trade_data, total_data, period)
                return result
            else:
                return {'占比': 0.0}
        
        results = {
            '当月': {
                '总计': total_data['当月'] if total_data else None,
                '一般贸易': merge_data_with_proportion(general_data, '当月', total_data),
                '加工贸易': merge_data_with_proportion(processing_data, '当月', total_data),
                '保税贸易': merge_data_with_proportion(bonded_data, '当月', total_data)
            },
            '累计': {
                '总计': total_data['累计'] if total_data else None,
                '一般贸易': merge_data_with_proportion(general_data, '累计', total_data),
                '加工贸易': merge_data_with_proportion(processing_data, '累计', total_data),
                '保税贸易': merge_data_with_proportion(bonded_data, '累计', total_data)
            }
        }
        
        return results
        
    except Exception as e:
        print(f"分析江苏数据时出错: {e}")
        return None

def format_output(results, region_name="江苏"):
    """
    格式化输出结果
    """
    if not results:
        print(f"{region_name}数据分析失败")
        return
    
    print(f"\n=== {region_name}省贸易方式数据分析 ===")
    
    for period in ['累计', '当月']:
        print(f"\n{period}数据:")
        period_data = results[period]
        
        if period_data.get('总计'):
            total = period_data['总计']
            print(f"总计进出口额: {total.get('进出口额', 0):.1f}亿元")
            if period == '累计' and '进出口同比' in total:
                print(f"总计同比: {total.get('进出口同比', 0):.1f}%")
        
        for trade_type in ['一般贸易', '加工贸易', '保税贸易']:
            data = period_data.get(trade_type, {})
            if data and data.get('进出口额', 0) > 0:
                print(f"{trade_type}: {data.get('进出口额', 0):.1f}亿元", end="")
                if period == '累计' and '进出口同比' in data:
                    print(f", 同比{data.get('进出口同比', 0):+.1f}%", end="")
                print(f", 占比{data.get('占比', 0):.1f}%")

def main():
    """
    主函数
    """
    # 江苏省数据文件路径
    base_path = r"C:\Users\<USER>\Desktop\海关数据\小超海关统计\全国进出口\20250530\2025年1-4月 - 江苏"
    
    # 查找最新的数据文件
    files = []
    for file in os.listdir(base_path):
        if file.endswith('.xls') and '江苏省主要进出口数据' in file:
            files.append(os.path.join(base_path, file))
    
    if not files:
        print("未找到江苏省数据文件")
        return
    
    # 使用最新的文件
    latest_file = max(files, key=os.path.getmtime)
    print(f"分析文件: {os.path.basename(latest_file)}")
    
    # 分析数据
    results = analyze_jiangsu_trade_data(latest_file)
    
    # 输出结果
    format_output(results, "江苏")
    
    # 生成简化的表格输出
    if results:
        print("\n=== 表格格式输出 ===")
        print("地区\t\t一般贸易\t\t\t\t加工贸易\t\t\t\t保税贸易")
        print("\t\t进出口额\t同比/%\t占比/%\t进出口额\t同比/%\t占比/%\t进出口额\t同比/%\t占比/%")
        
        # 累计数据
        cumulative = results['累计']
        general = cumulative.get('一般贸易', {})
        processing = cumulative.get('加工贸易', {})
        bonded = cumulative.get('保税贸易', {})
        
        print(f"江苏\t\t{general.get('进出口额', 0):.1f}\t\t{general.get('进出口同比', 0):.1f}\t{general.get('占比', 0):.1f}\t"
              f"{processing.get('进出口额', 0):.1f}\t\t{processing.get('进出口同比', 0):.1f}\t{processing.get('占比', 0):.1f}\t"
              f"{bonded.get('进出口额', 0):.1f}\t\t{bonded.get('进出口同比', 0):.1f}\t{bonded.get('占比', 0):.1f}")

if __name__ == "__main__":
    main()