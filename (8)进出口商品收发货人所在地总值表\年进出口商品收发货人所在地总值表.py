import pandas as pd
import os
import re
import cx_Oracle

# 设置pandas显示选项，确保数据完整显示
pd.set_option('display.max_columns', None)
pd.set_option('display.width', 1000)
pd.set_option('display.max_colwidth', None)

# 函数：处理值为"-"的情况
def convert_to_float_or_none(value):
    if pd.isna(value):
        return None
    if isinstance(value, str) and value.strip() == '-':
        return None
    try:
        return float(value)
    except (ValueError, TypeError):
        return None

def process_trade_statistics(file_path):
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return False
    
    try:
        # 读取XLS文件，只读取B列到K列（索引1-10）
        original_df = pd.read_excel(file_path, usecols=range(1, 11))
        
        # 提取当前月份信息
        # 先读取原始Excel获取标题信息
        title_df = pd.read_excel(file_path, usecols=[1], nrows=1)
        title_text = title_df.iloc[0, 0] if not title_df.empty and not title_df.iloc[0, 0] is None else ""
        
        print(f"标题文本: {title_text}")
        
        # 从标题中提取年月信息
        current_month = ""
        if isinstance(title_text, str):
            year_month_match = re.search(r'(\d{4})年(\d{1,2})月', title_text)
            if year_month_match:
                year = year_month_match.group(1)
                month = year_month_match.group(2).zfill(2)
                current_month = f"{year}{month}01"  # 设置为当月第一天
                print(f"提取的年月: {current_month}")
            else:
                print("无法从标题中提取年月信息")
        else:
            print("标题不是文本格式")
        
        # 显示原始总行数
        print(f"原始总行数: {len(original_df)}")
        
        # 查找以"总值"开头的行
        first_col = original_df.columns[0]
        total_value_idx = -1
        
        for i, row in original_df.iterrows():
            if isinstance(row[first_col], str) and row[first_col].strip() == "总值":
                total_value_idx = i
                break
        
        if total_value_idx != -1:
            print(f"找到'总值'行，索引为: {total_value_idx}")
            
            # 创建新的数据框，从'总值'行开始
            df = original_df.iloc[total_value_idx:].reset_index(drop=True)
            
            # 检查最后一行的第一个值是否以"注"开头
            last_row = df.iloc[-1]
            
            if isinstance(last_row[first_col], str) and last_row[first_col].strip().startswith("注"):
                print(f"删除最后一行，因为它以'注'开头: {last_row[first_col]}")
                # 删除最后一行
                df = df.iloc[:-1]
            
            # 设置列名
            df.columns = [
                "收发货人所在地",
                "当月进出口金额",
                "累计进出口金额",
                "当月出口金额",
                "累计出口金额",
                "当月进口金额",
                "累计进口金额",
                "累计比去年同期±%进出口",
                "累计比去年同期±%出口",
                "累计比去年同期±%进口"
            ]
            
            # 添加当前月份列
            df['当前月份'] = current_month
            
            print(f"\n处理后的数据框行数: {len(df)}")
            print("\n处理后的数据框前5行:")
            print(df.head())
            
            print("\n处理后的数据框最后5行:")
            print(df.tail())
            
            # 保存处理后的数据到新的Excel文件
            output_file = "处理后的进出口商品收发货人所在地总值表.xlsx"
            df.to_excel(output_file, index=False)
            print(f"\n已保存处理后的数据到文件: {output_file}")
            
            # 将数据写入Oracle数据库
            try:
                # 连接到Oracle数据库
                print("\n正在连接到Oracle数据库...")
                conn = cx_Oracle.connect("manifest_dcb/manifest_dcb@192.168.1.151/TEST")
                cursor = conn.cursor()
                print("数据库连接成功")
                
                # 准备插入数据
                insert_query = """
                INSERT INTO TEMP_TRADE_STATISTICS (
                    LOCATION, 
                    MONTH_IMPORT_EXPORT, 
                    MONTH_EXPORT, 
                    MONTH_IMPORT, 
                    YTD_IMPORT_EXPORT, 
                    YTD_EXPORT, 
                    YTD_IMPORT, 
                    YOY_IMPORT_EXPORT, 
                    YOY_EXPORT, 
                    YOY_IMPORT, 
                    CURRENT_MONTH
                ) VALUES (
                    :1, :2, :3, :4, :5, :6, :7, :8, :9, :10, TO_DATE(:11, 'YYYYMMDD')
                )
                """
                
                # 准备数据
                data_to_insert = []
                for _, row in df.iterrows():
                    # 跳过空行或不适合插入的行
                    if pd.isna(row['收发货人所在地']) or row['收发货人所在地'] == '':
                        continue
                    
                    # 将空值转换为None，将数值类型转换为float，处理"-"值
                    location = str(row['收发货人所在地']) if not pd.isna(row['收发货人所在地']) else None
                    month_import_export = convert_to_float_or_none(row['当月进出口金额'])
                    month_export = convert_to_float_or_none(row['当月出口金额'])
                    month_import = convert_to_float_or_none(row['当月进口金额'])
                    ytd_import_export = convert_to_float_or_none(row['累计进出口金额'])
                    ytd_export = convert_to_float_or_none(row['累计出口金额'])
                    ytd_import = convert_to_float_or_none(row['累计进口金额'])
                    yoy_import_export = convert_to_float_or_none(row['累计比去年同期±%进出口'])
                    yoy_export = convert_to_float_or_none(row['累计比去年同期±%出口'])
                    yoy_import = convert_to_float_or_none(row['累计比去年同期±%进口'])
                    
                    # 日期格式
                    current_month = str(row['当前月份']) if not pd.isna(row['当前月份']) else None
                    
                    data_to_insert.append((
                        location, 
                        month_import_export, 
                        month_export, 
                        month_import, 
                        ytd_import_export, 
                        ytd_export, 
                        ytd_import, 
                        yoy_import_export, 
                        yoy_export, 
                        yoy_import, 
                        current_month
                    ))
                
                # 执行批量插入
                print(f"准备插入 {len(data_to_insert)} 条数据...")
                cursor.executemany(insert_query, data_to_insert)
                conn.commit()
                print(f"成功插入 {cursor.rowcount} 条数据到Oracle数据库")
                
                # 关闭连接
                cursor.close()
                conn.close()
                print("数据库连接已关闭")
                return True
                
            except cx_Oracle.Error as error:
                print(f"Oracle数据库错误: {error}")
                return False
            except Exception as e:
                print(f"插入数据时发生错误: {e}")
                return False
            
        else:
            print("未找到'总值'行")
            return False
        
    except Exception as e:
        print(f"读取文件时发生错误: {e}")
        return False

# 示例使用
if __name__ == "__main__":
    file_path = r"C:\Users\<USER>\Desktop\海关数据\小超海关统计\全国进出口\年进出口商品收发货人所在地总值表\2025年进出口商品收发货人所在地总值表\2025031811361419128.xls"
    process_trade_statistics(file_path)