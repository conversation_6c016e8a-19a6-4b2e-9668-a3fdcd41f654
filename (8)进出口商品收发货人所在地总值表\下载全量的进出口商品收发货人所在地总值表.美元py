from DrissionPage import ChromiumPage, SessionPage
from DrissionPage import Chromium, ChromiumOptions

import time
import os
import re
import shutil
from datetime import datetime

# co = ChromiumOptions().auto_port()

# page = Chromium(addr_or_opts=co).latest_tab



# 创建一个ChromiumPage实例，并设置下载路径
download_dir = "进出口商品收发货人所在地总值表_下载文件美元"
if not os.path.exists(download_dir):
    os.makedirs(download_dir)
    print(f"创建下载目录: {download_dir}")

# 设置ChromiumPage下载路径
# 创建临时下载目录用于存放原始下载文件
temp_download_dir = "temp_download"
if not os.path.exists(temp_download_dir):
    os.makedirs(temp_download_dir)
    print(f"创建临时下载目录: {temp_download_dir}")

# 设置ChromiumPage下载路径到临时目录
page = ChromiumPage(timeout=20)
page.set.download_path(os.path.abspath(temp_download_dir))
print(f"设置下载目录: {temp_download_dir}")

# # 定义基础URL
# base_url = "http://www.customs.gov.cn"

# # 访问初始URL
# start_url = "http://www.customs.gov.cn/customs/302249/zfxxgk/2799825/302274/302277/6348926/index.html"
# page.get(start_url)

# # 打印页面标题，确认访问成功
# print(f"页面标题: {page.title}")

# # 等待页面完全加载
# time.sleep(2)

# # 创建一个大列表来存储所有年份的链接
# all_years_links = []

# # 找到所有年份的链接
# year_links_div = page.ele('.tjYear')
# if year_links_div:
#     year_links = year_links_div.eles('tag:a')
#     year_urls = []
    
#     # 提取所有年份的URL
#     for year_link in year_links:
#         href = year_link.attr('href')
#         if href:
#             if href.startswith('/'):
#                 full_year_url = base_url + href
#             else:
#                 full_year_url = href
#             year_text = year_link.text
#             year_urls.append((year_text, full_year_url))
    
#     print(f"找到 {len(year_urls)} 个年份链接")
    
#     # 遍历每个年份的链接
#     for year_text, year_url in year_urls:
#         print(f"\n正在处理 {year_text} 年份数据: {year_url}")
        
#         # 访问年份页面
#         page.get(year_url)
#         time.sleep(2)  # 等待页面加载
        
#         # 查找包含"进出口商品收发货人所在地总值表"的元素
#         target_ele = page.eles("进出口商品收发货人所在地总值表", timeout=5)[1]
#         if target_ele:
#             print(f"找到目标元素: {target_ele.text}")
            
#             # 获取下一个元素
#             next_ele = target_ele.next()
#             if next_ele:
#                 print(f"找到下一个元素: {next_ele.tag}")
                
#                 # 查找该元素中的所有a标签
#                 links = next_ele.eles('tag:a')
                
#                 # 该年份的链接列表
#                 year_links_list = []
                
#                 # 遍历所有a标签并获取完整超链接地址
#                 for link in links:
#                     href = link.attr('href')
#                     if href:
#                         # 如果是相对路径，转换为完整URL
#                         if not href.startswith('http'):
#                             if href.startswith('/'):
#                                 full_url = base_url + href
#                             else:
#                                 # 从当前URL构建完整路径
#                                 current_url = page.url
#                                 base_path = '/'.join(current_url.split('/')[:-1]) + '/'
#                                 full_url = base_path + href
#                         else:
#                             full_url = href
                        
#                         # 添加到该年份的链接列表
#                         link_info = {
#                             "year": year_text,
#                             "text": link.text,
#                             "url": full_url
#                         }
#                         year_links_list.append(link_info)
#                         print(f"链接文本: {link.text}, URL: {full_url}")
                
#                 # 将该年份的链接添加到总列表中
#                 all_years_links.extend(year_links_list)
#                 print(f"{year_text}年共找到 {len(year_links_list)} 个链接")
#             else:
#                 print(f"{year_text}年未找到下一个元素")
#         else:
#             print(f"{year_text}年未找到包含'进出口商品收发货人所在地总值表'的元素")

# # 打印所有年份的链接总数
# print(f"\n总共找到 {len(all_years_links)} 个链接")
# print("所有年份的链接已收集完成")

# # 可以将结果保存到文件中
# with open('进出口商品收发货人所在地总值表_所有链接美元.txt', 'w', encoding='utf-8') as f:
#     for link_info in all_years_links:
#         f.write(f"{link_info['year']},{link_info['text']},{link_info['url']}\n")

# print("链接已保存到文件: 进出口商品收发货人所在地总值表_所有链接美元.txt")

# 从本地文件读取链接
all_years_links = []
base_url = "http://www.customs.gov.cn"
link_file = '进出口商品收发货人所在地总值表_所有链接美元.txt'

print(f"从本地文件读取链接: {link_file}")
with open(link_file, 'r', encoding='utf-8') as f:
    for line in f:
        parts = line.strip().split(',', 2)  # 最多分成3部分（年份,文本,URL）
        if len(parts) == 3:
            year, text, url = parts
            link_info = {
                "year": year,
                "text": text,
                "url": url
            }
            all_years_links.append(link_info)

print(f"从本地文件读取了 {len(all_years_links)} 个链接")

# 遍历所有链接并下载数据
print("\n开始下载文件...")
download_count = 0
error_count = 0

for link_info in all_years_links:
    try:
        year = link_info['year']
        text = link_info['text']
        url = link_info['url']
        
        print(f"\n正在处理: {year} - {text}")
        print(f"访问URL: {url}")
        
        # 访问详情页
        page.get(url)
        time.sleep(2)  # 等待页面加载
        
        # 查找文件名元素（包含"进出口商品收发货人所在地总值表"的最后一个元素）
        title_elements = page.eles("进出口商品收发货人所在地总值表")
        if title_elements and len(title_elements) > 0:
            title_element = title_elements[-1]  # 取最后一个标题元素
            file_title = title_element.text.strip()
            
            # 清理文件名（移除不允许的字符）
            file_title = re.sub(r'[\\/*?:"<>|]', '', file_title)
            
            # 构造文件名
            file_name = f"{year}_{file_title}.xls"
        else:
            print("未找到包含'进出口商品收发货人所在地总值表'的标题元素")
            # 使用链接文本作为备用文件名
            file_name = f"{year}_{text}.xls"
        
        # 构造完整的文件保存路径
        file_path = os.path.join(download_dir, file_name)
        print(f"准备下载文件: {file_name}")
        
        # 查找"下载"按钮
        download_links = page.eles("下载")
        if download_links and len(download_links) > 0:
            download_link_ele = download_links[-1]  # 取最后一个下载按钮
            
            # 清空临时下载目录中的文件
            for file in os.listdir(temp_download_dir):
                file_path_to_remove = os.path.join(temp_download_dir, file)
                try:
                    if os.path.isfile(file_path_to_remove):
                        os.remove(file_path_to_remove)
                except Exception as e:
                    print(f"清理临时文件时出错: {e}")
            
            # 点击下载按钮
            print("点击下载按钮...")
            download_link_ele.click()
            
            # 等待下载完成
            downloaded = False
            start_time = time.time()
            timeout = 60  # 设置超时时间为60秒
            
            while not downloaded and time.time() - start_time < timeout:
                time.sleep(3)  # 每3秒检查一次
                
                # 检查临时目录中是否有新文件
                files = os.listdir(temp_download_dir)
                if files:
                    for file in files:
                        if file.endswith('.xls') or file.endswith('.xlsx') or file.endswith('.crdownload'):
                            # 如果还在下载中(有.crdownload后缀)，继续等待
                            if file.endswith('.crdownload'):
                                print(f"文件正在下载中: {file}")
                                continue
                            
                            # 找到下载的文件
                            temp_file_path = os.path.join(temp_download_dir, file)
                            print(f"文件已下载到临时位置: {temp_file_path}")
                            
                            # 移动并重命名文件
                            try:
                                shutil.copy2(temp_file_path, file_path)
                                print(f"文件已复制到最终位置: {file_path}")
                                downloaded = True
                                download_count += 1
                                break
                            except Exception as e:
                                print(f"移动文件时出错: {e}")
                                error_count += 1
            
            if not downloaded:
                print("下载超时或失败")
                error_count += 1
        else:
            print("未找到下载按钮")
            error_count += 1
    except Exception as e:
        print(f"处理链接时出错: {e}")
        error_count += 1
    
    # 防止请求过于频繁
    time.sleep(2)

print(f"\n下载完成! 成功: {download_count}, 失败: {error_count}")

# 清理临时下载目录
try:
    shutil.rmtree(temp_download_dir)
    print(f"临时下载目录已清理: {temp_download_dir}")
except Exception as e:
    print(f"清理临时下载目录时出错: {e}")

# 关闭浏览器
page.quit()



