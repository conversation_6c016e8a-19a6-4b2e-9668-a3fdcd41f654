from DrissionPage import SessionPage, ChromiumPage
import pandas as pd
import os
import time

# 创建存储目录
output_dir = "提取的表格数据"
if not os.path.exists(output_dir):
    os.makedirs(output_dir)
    print(f"创建输出目录: {output_dir}")

# 初始化浏览器页面对象，使用ChromiumPage来处理可能的动态加载内容
page = ChromiumPage()

def extract_table_from_url(url, file_name):
    """从指定URL提取表格数据并保存为Excel"""
    try:
        print(f"访问URL: {url}")
        page.get(url)
        time.sleep(2)  # 等待页面完全加载
        
        # 查找页面上的所有表格
        table_elements = page.eles('tag:table')
        
        if not table_elements:
            print("未找到表格元素")
            return False
        
        print(f"找到 {len(table_elements)} 个表格元素")
        
        # 找到最有可能是目标表格的元素（通常是拥有最多行的表格）
        target_table = None
        max_rows = 0
        
        for i, table in enumerate(table_elements):
            rows = table.eles('tag:tr')
            if len(rows) > max_rows:
                max_rows = len(rows)
                target_table = table
            print(f"表格 {i+1}: {len(rows)} 行")
        
        if not target_table:
            print("未找到合适的表格")
            return False
        
        print(f"选择了具有 {max_rows} 行的表格")
        
        # 获取表格HTML
        html_content = target_table.html
        
        try:
            # 尝试使用不同表头配置解析表格
            try:
                # 先尝试双行表头
                dfs = pd.read_html(html_content, header=[0,1], flavor='bs4')
                df = dfs[0]
                # 合并多级表头
                df.columns = [' '.join(str(col) for col in cols if str(col) != 'nan').strip() for cols in df.columns.values]
            except Exception as e:
                print(f"尝试双行表头失败: {e}")
                try:
                    # 尝试单行表头
                    dfs = pd.read_html(html_content, header=0, flavor='bs4')
                    df = dfs[0]
                except Exception as e:
                    print(f"尝试单行表头失败: {e}")
                    # 无表头
                    dfs = pd.read_html(html_content, header=None, flavor='bs4')
                    df = dfs[0]
            
            # 保存为Excel
            file_path = os.path.join(output_dir, file_name)
            df.to_excel(file_path, index=False)
            print(f"表格数据已保存到: {file_path}")
            
            # 同时保存HTML版本以备查看
            html_file_path = os.path.join(output_dir, f"{os.path.splitext(file_name)[0]}_原始表格.html")
            with open(html_file_path, 'w', encoding='utf-8') as html_file:
                html_file.write(f"<html><body>{html_content}</body></html>")
            print(f"原始HTML已保存到: {html_file_path}")
            
            return True
        except Exception as e:
            print(f"解析表格数据时出错: {e}")
            
            # 保存原始HTML到文件
            html_file_path = os.path.join(output_dir, f"{os.path.splitext(file_name)[0]}_原始表格.html")
            with open(html_file_path, 'w', encoding='utf-8') as html_file:
                html_file.write(f"<html><body>{html_content}</body></html>")
            print(f"无法解析表格，原始HTML已保存到: {html_file_path}")
            
            return False
    except Exception as e:
        print(f"处理URL时出错: {e}")
        return False

# 示例用法
if __name__ == "__main__":
    # 请替换为您需要提取表格的URL列表
    urls_to_process = [
        # 以下是示例，请替换为实际URL
        # ("http://www.customs.gov.cn/example/url1", "2019年进出口商品收发货人所在地总值表.xlsx"),
        # ("http://www.customs.gov.cn/example/url2", "2018年进出口商品收发货人所在地总值表.xlsx"),
    ]
    
    # 如果没有预定义的URL，可以手动输入
    if not urls_to_process:
        while True:
            url = input("请输入要处理的URL (输入 'q' 退出): ")
            if url.lower() == 'q':
                break
                
            file_name = input("请输入保存的文件名 (例如: 2019年进出口商品收发货人所在地总值表.xlsx): ")
            if not file_name.endswith('.xlsx'):
                file_name += '.xlsx'
                
            print(f"开始处理URL: {url}")
            success = extract_table_from_url(url, file_name)
            print(f"处理结果: {'成功' if success else '失败'}\n")
    else:
        # 处理预定义的URL列表
        success_count = 0
        fail_count = 0
        
        for url, file_name in urls_to_process:
            print(f"\n开始处理: {file_name}")
            result = extract_table_from_url(url, file_name)
            if result:
                success_count += 1
            else:
                fail_count += 1
            
            # 防止请求过于频繁
            time.sleep(3)
        
        print(f"\n处理完成! 成功: {success_count}, 失败: {fail_count}")
    
    # 关闭浏览器
    page.quit() 