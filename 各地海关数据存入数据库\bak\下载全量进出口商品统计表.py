from DrissionPage import ChromiumPage, SessionPage
import time
import os
import re
import shutil
import pandas as pd
from datetime import datetime

# 定义要查找的所有表格种类基础名称
TARGET_TABLES_CONFIG = {
    "(1)A": {"name": "(1){}年进出口商品总值表 A:年度表", "is_annual": True, "is_cumulative": False},
    "(1)B": {"name": "(1){}年进出口商品总值表 B:月度表", "is_annual": False, "is_cumulative": False}, # 修正：恢复为月度表，采用双重检查
    "(2)": {"name": "(2){}年进出口商品国别(地区)总值表", "is_annual": False, "is_cumulative": False},
    "(3)": {"name": "(3){}年进出口商品构成表", "is_annual": False, "is_cumulative": False},
    "(4)": {"name": "(4){}年进出口商品类章总值表", "is_annual": False, "is_cumulative": False},
    "(5)": {"name": "(5){}年进出口商品贸易方式总值表", "is_annual": False, "is_cumulative": False},
    "(6)": {"name": "(6){}年出口商品贸易方式企业性质总值表", "is_annual": False, "is_cumulative": True},
    "(7)": {"name": "(7){}年进口商品贸易方式企业性质总值表", "is_annual": False, "is_cumulative": True},
    "(8)": {"name": "(8){}年进出口商品收发货人所在地总值表", "is_annual": False, "is_cumulative": False},
    "(9)": {"name": "(9){}年进出口商品境内目的地/货源地总值表", "is_annual": False, "is_cumulative": False},
    "(10)": {"name": "(10){}年进出口商品关别总值表", "is_annual": False, "is_cumulative": False},
    "(11)": {"name": "(11){}年特定地区进出口总值表", "is_annual": False, "is_cumulative": False},
    "(12)": {"name": "(12){}年外商投资企业进出口总值表", "is_annual": False, "is_cumulative": False},
    "(13)": {"name": "(13){}年出口主要商品量值表", "is_annual": False, "is_cumulative": False},
    "(14)": {"name": "(14){}年进口主要商品量值表", "is_annual": False, "is_cumulative": False},
    "(15)": {"name": "(15){}年对部分国家(地区)出口商品类章金额表", "is_annual": False, "is_cumulative": False},
    "(16)": {"name": "(16){}年自部分国家(地区)进口商品类章金额表", "is_annual": False, "is_cumulative": False},
    "(17)": {"name": "(17){}年部分出口商品主要贸易方式量值表", "is_annual": False, "is_cumulative": True},
    "(18)": {"name": "(18){}年部分进口商品主要贸易方式量值表", "is_annual": False, "is_cumulative": True}
}

# 定义表格类型对应的目录名称
TABLE_DIR_NAMES = {
    "(1)A": "(1)A进出口商品总值表_A年度表",
    "(1)B": "(1)进出口商品总值表_B月度表",
    "(2)": "(2)进出口商品国别(地区)总值表",
    "(3)": "(3)进出口商品构成表",
    "(4)": "(4)进出口商品类章总值表",
    "(5)": "(5)进出口商品贸易方式总值表",
    "(6)": "(6)出口商品贸易方式企业性质总值表",
    "(7)": "(7)进口商品贸易方式企业性质总值表",
    "(8)": "(8)进出口商品收发货人所在地总值表",
    "(9)": "(9)进出口商品境内目的地货源地总值表",
    "(10)": "(10)进出口商品关别总值表",
    "(11)": "(11)特定地区进出口总值表",
    "(12)": "(12)外商投资企业进出口总值表",
    "(13)": "(13)出口主要商品量值表",
    "(14)": "(14)进口主要商品量值表",
    "(15)": "(15)对部分国家(地区)出口商品类章金额表",
    "(16)": "(16)自部分国家(地区)进口商品类章金额表",
    "(17)": "(17)部分出口商品主要贸易方式量值表",
    "(18)": "(18)部分进口商品主要贸易方式量值表"
}


# 下载文件的函数
def download_files(all_years_links, base_download_dir, page, temp_download_dir):
    download_count = 0
    scrape_count = 0
    error_count = 0
    
    for link_info in all_years_links:
        try:
            year = link_info['year']
            table_key = link_info['table_key']
            config = TARGET_TABLES_CONFIG[table_key]
            currency_type = link_info['currency_type']
            text = link_info['text']
            url = link_info['url']
            
            # 跳过2019年及以前的数据
            if int(year) < 2020:
                print(f"跳过{year}年的数据，只处理2020年及以后的数据")
                continue

            print(f"\n正在处理: {year} - {text} ({config['name'].format(year)}, {currency_type})")
            print(f"访问URL: {url}")
            
            # --- 新逻辑: 先访问页面获取真实标题, 再判断是否存在 ---

            # 1. 访问详情页
            page.get(url)
            time.sleep(2)  # 等待页面加载
            
            # 2. 获取真实文件名
            file_name = ""
            news_title_div = page.ele('.easysite-news-title')
            if news_title_div:
                title_element = news_title_div.ele('tag:h2')
                if title_element:
                    file_title = title_element.text.strip()
                    print(f"从h2标签获取真实标题: {file_title}")
                    # 清理文件名（移除不允许的字符）
                    file_title = re.sub(r'[\\/*?:"<>|]', '', file_title)
                    file_name = f"{year}_{file_title}.xlsx"
            
            # 如果主要方法失败，使用备用方法
            if not file_name:
                print("未找到h2标题，尝试使用备用方法")
                search_pattern = re.sub(r'\{\}年', '[0-9]+年', config['name'])
                title_elements = page.eles(search_pattern)
                if title_elements and len(title_elements) > 0:
                    title_element = title_elements[-1]
                    file_title = title_element.text.strip()
                    file_title = re.sub(r'[\\/*?:"<>|]', '', file_title)
                    file_name = f"{year}_{file_title}.xlsx"

            # 如果所有方法都失败，则基于链接文本生成一个备用文件名
            if not file_name:
                print(f"无法从页面获取标题，使用链接文本作为备用")
                clean_text = re.sub(r'[\\/*?:"<>|]', '', text)
                file_name = f"{year}_{config['name'].format(year)}_{clean_text}_{currency_type}.xlsx"

            # 3. 构建完整路径
            table_dir_name = TABLE_DIR_NAMES[table_key]
            final_dir_name = f"{table_dir_name}_{currency_type}"
            download_dir = os.path.join(base_download_dir, final_dir_name)
            if not os.path.exists(download_dir):
                os.makedirs(download_dir)
                print(f"创建目录: {download_dir}")
            
            file_path = os.path.join(download_dir, file_name)

            # 4. 检查文件是否存在
            if os.path.exists(file_path):
                print(f"文件已存在，跳过: {file_name}")
                continue
            
            # --- 如果文件不存在，开始下载流程 ---
            print(f"准备下载文件: {file_name}")

            # 查找"下载"按钮
            download_links = page.eles("下载")
            
            if not download_links or len(download_links) == 0:
                print("未找到下载按钮，尝试直接提取表格数据...")
                # 查找表格元素
                table_elements = page.eles('tag:table')
                
                if table_elements and len(table_elements) > 0:
                    print(f"找到{len(table_elements)}个表格元素")
                    
                    # 找到最有可能是目标表格的元素（通常是较大的表格）
                    target_table = None
                    max_rows = 0
                    
                    for i, table in enumerate(table_elements):
                        rows = table.eles('tag:tr')
                        if len(rows) > max_rows:
                            max_rows = len(rows)
                            target_table = table
                        print(f"表格 {i+1}: {len(rows)} 行")
                    
                    if target_table:
                        print(f"选择了具有 {max_rows} 行的表格")
                        
                        # 获取表格HTML
                        html_content = target_table.html
                        
                        try:
                            # 尝试使用pandas解析表格
                            # 根据表格结构确定表头行数
                            try:
                                # 先尝试双行表头
                                dfs = pd.read_html(html_content, header=[0,1], flavor='bs4')
                                df = dfs[0]
                                # 合并多级表头
                                df.columns = [' '.join(str(col) for col in cols if str(col) != 'nan').strip() for cols in df.columns.values]
                            except Exception:
                                try:
                                    # 尝试单行表头
                                    dfs = pd.read_html(html_content, header=0, flavor='bs4')
                                    df = dfs[0]
                                except:
                                    # 无表头
                                    dfs = pd.read_html(html_content, header=None, flavor='bs4')
                                    df = dfs[0]
                            
                            # 保存为Excel
                            df.to_excel(file_path, index=False)
                            print(f"表格数据已保存到: {file_path}")
                            scrape_count += 1
                        except Exception as e:
                            print(f"解析表格数据时出错: {e}")
                            
                            # 备用方案：直接保存HTML到文件
                            html_file_path = os.path.join(download_dir, f"{year}_{text}_原始表格.html")
                            with open(html_file_path, 'w', encoding='utf-8') as html_file:
                                html_file.write(f"<html><body>{html_content}</body></html>")
                            print(f"无法解析表格，原始HTML已保存到: {html_file_path}")
                            error_count += 1
                    else:
                        print("未找到合适的表格")
                        error_count += 1
                else:
                    print("页面上未找到表格元素")
                    error_count += 1
            else:
                download_link_ele = download_links[-1]  # 取最后一个下载按钮
                
                # 清空临时下载目录中的文件
                for file in os.listdir(temp_download_dir):
                    file_path_to_remove = os.path.join(temp_download_dir, file)
                    try:
                        if os.path.isfile(file_path_to_remove):
                            os.remove(file_path_to_remove)
                    except Exception as e:
                        print(f"清理临时文件时出错: {e}")
                
                # 点击下载按钮
                print("点击下载按钮...")
                download_link_ele.click()

                # 等待下载完成
                downloaded = False
                start_time = time.time()
                timeout = 60  # 设置超时时间为60秒
                
                while not downloaded and time.time() - start_time < timeout:
                    time.sleep(3)  # 每3秒检查一次
                    
                    # 检查临时目录中是否有新文件
                    files = os.listdir(temp_download_dir)
                    if files:
                        for file in files:
                            if file.endswith('.xls') or file.endswith('.xlsx') or file.endswith('.crdownload'):
                                # 如果还在下载中(有.crdownload后缀)，继续等待
                                if file.endswith('.crdownload'):
                                    print(f"文件正在下载中: {file}")
                                    continue
                                
                                # 找到下载的文件
                                temp_file_path = os.path.join(temp_download_dir, file)
                                print(f"文件已下载到临时位置: {temp_file_path}")
                                
                                # 移动并重命名文件
                                try:
                                    shutil.copy2(temp_file_path, file_path)
                                    print(f"文件已复制到最终位置: {file_path}")
                                    downloaded = True
                                    download_count += 1
                                    break
                                except Exception as e:
                                    print(f"移动文件时出错: {e}")
                                    error_count += 1
                
                if not downloaded:
                    print("下载超时或失败")
                    error_count += 1
        except Exception as e:
            print(f"处理链接时出错: {e}")
            error_count += 1
        
        # 防止请求过于频繁
        time.sleep(2)
    
    print(f"\n基础目录 {base_download_dir} 处理完成! 下载成功: {download_count}, 爬取成功: {scrape_count}, 失败: {error_count}")

# 创建基础下载目录（人民币值和美元值各一个）
rmb_base_dir = "进出口商品统计表_人民币值"
usd_base_dir = "进出口商品统计表_美元值"

for base_dir in [rmb_base_dir, usd_base_dir]:
    if not os.path.exists(base_dir):
        os.makedirs(base_dir)
        print(f"创建基础下载目录: {base_dir}")

# 创建临时下载目录用于存放原始下载文件
temp_download_dir = "temp_download"
if not os.path.exists(temp_download_dir):
    os.makedirs(temp_download_dir)
    print(f"创建临时下载目录: {temp_download_dir}")

# 设置ChromiumPage下载路径到临时目录
page = ChromiumPage(timeout=20)
page.set.download_path(os.path.abspath(temp_download_dir))
print(f"设置下载目录: {temp_download_dir}")

# 定义基础URL
base_url = "http://www.customs.gov.cn"

# 访问初始URL
start_url = "http://www.customs.gov.cn/customs/302249/zfxxgk/2799825/302274/302277/6348926/index.html"
page.get(start_url)

# 打印页面标题，确认访问成功
print(f"页面标题: {page.title}")

# 等待页面完全加载
time.sleep(2)

# 创建列表来存储所有年份的链接（人民币值和美元值）
rmb_all_years_links = []
usd_all_years_links = []

# 找到所有年份的链接
year_links_div = page.ele('.tjYear')
if year_links_div:
    year_links = year_links_div.eles('tag:a')
    year_urls = []
    
    # 提取所有年份的URL
    for year_link in year_links:
        href = year_link.attr('href')
        if href:
            if href.startswith('/'):
                full_year_url = base_url + href
            else:
                full_year_url = href
            year_text = year_link.text  # 提取年份文本，如"2025年"
            year_value = year_text.strip('年')  # 移除"年"字，得到如"2025"的年份值
            
            # 只处理2020年及以后的数据
            if int(year_value) >= 2020:
                year_urls.append((year_text, year_value, full_year_url))
                print(f"添加年份: {year_text}")
            else:
                print(f"跳过年份: {year_text}，只处理2020年及以前的数据")
    
    print(f"找到 {len(year_urls)} 个符合条件的年份链接(2020年及以后)")
    
    # 找出网站上的最新年份
    latest_year_on_site = 0
    if year_urls:
        latest_year_on_site = max([int(y[1]) for y in year_urls])
    else:
        latest_year_on_site = datetime.now().year # 备用
    print(f"网站上的最新年份是: {latest_year_on_site}")
    
    # 遍历每个年份的链接
    for year_text, year_value, year_url in year_urls:
        # 新增: 只处理最新年份的数据
        if int(year_value) < latest_year_on_site:
            print(f"跳过往年数据: {year_text}")
            continue

        print(f"\n正在处理 {year_text} 年份数据: {year_url}")
        
        # 访问年份页面
        page.get(year_url)
        time.sleep(2)  # 等待页面加载
        
        # 遍历所有目标表格类型
        for table_key, config in TARGET_TABLES_CONFIG.items():
            year_specific_table_name = config['name'].format(year_value)
            print(f"查找表格: {year_specific_table_name}")
            
            # 使用eles查找所有符合条件的元素（包含目标表格名称的元素）
            target_elements = page.eles(year_specific_table_name, timeout=5)
            
            if target_elements and len(target_elements) > 0:
                print(f"找到 {len(target_elements)} 个包含 '{year_specific_table_name}' 的元素")
                
                # 遍历找到的元素（通常第一个是人民币值，第二个是美元值）
                for idx, target_ele in enumerate(target_elements):
                    element_text = target_ele.text.strip()
                    print(f"目标元素 {idx+1}: {element_text}")
                    
                    # 判断是人民币值还是美元值 (通常从文本中判断)
                    is_rmb = "人民币" in element_text or (idx == 0 and "美元" not in element_text)
                    is_usd = "美元" in element_text or (idx == 1 and "人民币" not in element_text)
                    
                    if is_rmb or is_usd:
                        currency_type = "人民币值" if is_rmb else "美元值"
                        print(f"货币类型: {currency_type}")
                        
                        # 获取下一个元素（通常包含链接）
                        next_ele = target_ele.next()
                        if next_ele:
                            print(f"找到下一个元素: {next_ele.tag}")
                            
                            # 查找该元素中的所有a标签
                            links = next_ele.eles('tag:a')
                            
                            # 移除往年逻辑

                            # 遍历所有a标签并获取完整超链接地址
                            for link in links:
                                href = link.attr('href')
                                if href:
                                    # 如果是相对路径，转换为完整URL
                                    if not href.startswith('http'):
                                        if href.startswith('/'):
                                            full_url = base_url + href
                                        else:
                                            # 从当前URL构建完整路径
                                            current_url = page.url
                                            base_path = '/'.join(current_url.split('/')[:-1]) + '/'
                                            full_url = base_path + href
                                    else:
                                        full_url = href
                                    
                                    # 创建链接信息
                                    link_info = {
                                        "year": year_value,
                                        "text": link.text,
                                        "url": full_url,
                                        "table_key": table_key,
                                        "table_type": year_specific_table_name, # 保留旧格式以供调试
                                        "currency_type": currency_type
                                    }
                                    
                                    # 添加到相应的列表
                                    if is_rmb:
                                        rmb_all_years_links.append(link_info)
                                    else:
                                        usd_all_years_links.append(link_info)
                                    
                                    print(f"链接文本: {link.text}, URL: {full_url}")
                        else:
                            print(f"未找到包含链接的下一个元素")
                    else:
                        print(f"无法确定货币类型，跳过此元素")
            else:
                print(f"未找到包含'{year_specific_table_name}'的元素")

# 打印所有年份的链接总数
print(f"\n总共找到 {len(rmb_all_years_links)} 个人民币值链接")
print(f"总共找到 {len(usd_all_years_links)} 个美元值链接")
print("所有年份的链接已收集完成")

# 将结果保存到文件中（人民币值和美元值分别保存）
with open('进出口商品统计表_人民币值_所有链接.txt', 'w', encoding='utf-8') as f:
    for link_info in rmb_all_years_links:
        f.write(f"{link_info['year']},{link_info['text']},{link_info['url']},{link_info['table_key']},{link_info['table_type']},{link_info['currency_type']}\n")

with open('进出口商品统计表_美元值_所有链接.txt', 'w', encoding='utf-8') as f:
    for link_info in usd_all_years_links:
        f.write(f"{link_info['year']},{link_info['text']},{link_info['url']},{link_info['table_key']},{link_info['table_type']},{link_info['currency_type']}\n")

print("链接已保存到文件: 进出口商品统计表_人民币值_所有链接.txt 和 进出口商品统计表_美元值_所有链接.txt")

# 下载人民币值文件
print("\n开始下载人民币值文件...")
download_files(rmb_all_years_links, rmb_base_dir, page, temp_download_dir)

# 下载美元值文件
print("\n开始下载美元值文件...")
download_files(usd_all_years_links, usd_base_dir, page, temp_download_dir)

# 清理临时下载目录
try:
    shutil.rmtree(temp_download_dir)
    print(f"临时下载目录已清理: {temp_download_dir}")
except Exception as e:
    print(f"清理临时下载目录时出错: {e}")

# 关闭浏览器
page.quit() 