# 海关数据提取工具

这是一个用于提取海关数据的工具集，专门针对进出口商品收发货人所在地总值表等数据的提取和分析。

## 功能特点

- 支持从单个URL提取数据
- 支持批量处理多年份链接
- 两种数据提取模式：下载按钮模式和直接解析模式
- 自动智能选择最合适的提取方式
- 数据清洗与转换功能
- 年度统计报告生成
- 依赖包自动检查与安装

## 文件说明

本工具包含以下核心文件：

- `海关数据提取助手.py` - 主程序，综合了所有功能
- `提取链接列表.py` - 从海关网站提取链接列表
- `下载表格数据.py` - 使用下载按钮提取数据的模块
- `直接提取表格数据.py` - 直接从网页解析数据的模块
- `按年份批量提取表格数据.py` - 针对年份批量提取数据的脚本
- `进出口商品收发货人所在地总值表_所有链接.txt` - 存储提取的链接列表

## 使用前准备

1. 确保已安装Python 3.6或更高版本
2. 首次运行时，程序会检查并提示安装必要的依赖包：
   - pandas - 数据处理
   - DrissionPage - 网页操作
   - openpyxl - Excel文件处理
   - lxml, beautifulsoup4, html5lib - 网页解析

## 使用方法

### 方法一：使用综合工具

运行`海关数据提取助手.py`，根据菜单选择操作：

```
python 海关数据提取助手.py
```

主菜单选项：
1. 提取单个URL数据 - 处理单个链接
2. 批量处理链接 - 处理多个链接
3. 生成年份统计报告 - 对已提取数据进行统计分析
0. 退出程序

### 方法二：单独使用各功能模块

#### 1. 提取链接列表

```
python 提取链接列表.py
```

该脚本会访问海关网站，自动提取所有年份的链接，并保存到文本文件。

#### 2. 使用下载按钮提取数据

```
python 下载表格数据.py
```

该脚本使用网站自带的下载按钮提取数据，适用于有下载功能的页面。

#### 3. 直接从网页提取表格数据

```
python 直接提取表格数据.py
```

该脚本直接解析网页中的表格，适用于下载按钮不可用的情况。

#### 4. 按年份批量提取数据

```
python 按年份批量提取表格数据.py
```

该脚本读取链接文件，并允许按年份选择性地批量提取数据。

## 数据输出

- 提取的表格数据保存在`提取的表格数据`目录下
- 统计报告保存在`统计报告`目录下
- 文件命名格式为：`年份_数据名称.xlsx`

## 常见问题

1. **网页加载失败**
   - 检查网络连接
   - 增加等待时间（修改代码中的`time.sleep`值）
   - 尝试使用另一种提取方式

2. **数据提取不完整**
   - 检查网页结构是否有变化
   - 尝试使用另一种提取方式
   - 检查是否被网站限制访问频率

3. **浏览器自动关闭**
   - 程序设计会在完成任务后自动关闭浏览器
   - 若需观察过程，可修改代码移除`close_browser()`调用

## 注意事项

- 数据提取设置了时间间隔，避免请求过于频繁被限制
- 建议先提取少量数据测试功能是否正常
- 海关网站结构可能随时间变化，若提取失败，请尝试更新程序

## 开发说明

本工具基于Python的DrissionPage库开发，提供了两种提取数据的核心方法：

1. **下载按钮方式**：模拟用户点击下载按钮，获取Excel文件
2. **直接解析方式**：直接从网页DOM中解析表格数据

可根据具体需求选择适合的方法，或使用自动模式由程序智能选择。 