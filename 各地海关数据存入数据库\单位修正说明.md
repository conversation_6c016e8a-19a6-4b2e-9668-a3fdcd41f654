# 单位修正说明

## 修正内容

原始Excel文件中的金额单位是**万元**，而Oracle数据库字段要求的是**亿元**，因此在数据转换过程中进行了单位转换。

## 转换规则

```
亿元 = 万元 ÷ 10000
```

## 修正位置

在 `zhejiang_data_transform.py` 脚本中，对所有金额字段都进行了单位转换：

### 修正的字段：
- `MON_A_CNY_AMOUNT` - 当月进出口金额（人民币）
- `MON_E_CNY_AMOUNT` - 当月出口金额（人民币）
- `MON_I_CNY_AMOUNT` - 当月进口金额（人民币）

### 修正的代码：
```python
# 贸易方式等工作表
temp_df['MON_A_CNY_AMOUNT'] = pd.to_numeric(clean_df['当期进出口'], errors='coerce') / 10000
temp_df['MON_E_CNY_AMOUNT'] = pd.to_numeric(clean_df['当期出口'], errors='coerce') / 10000
temp_df['MON_I_CNY_AMOUNT'] = pd.to_numeric(clean_df['当期进口'], errors='coerce') / 10000

# 主出商品、主进商品工作表
temp_df['MON_E_CNY_AMOUNT'] = pd.to_numeric(clean_df['当期'], errors='coerce') / 10000
temp_df['MON_I_CNY_AMOUNT'] = pd.to_numeric(clean_df['当期'], errors='coerce') / 10000
```

## 数据验证

### 修正前示例：
- 一般贸易当月进出口：209142154.4853 万元
- 来料加工装配贸易：2834728.1868 万元

### 修正后示例：
- 一般贸易当月进出口：20914.2154 亿元
- 来料加工装配贸易：283.4728 亿元

## 数据合理性检查

修正后的数据范围：
- 当月进出口金额：0.0 - 27326.9025 亿元
- 当月出口金额：0.0 - 20734.2415 亿元
- 当月进口金额：0.0 - 6592.6610 亿元

这些数值都在合理范围内，符合浙江省月度海关统计数据的实际情况。

## 影响范围

此修正影响了所有347条记录中的金额字段，确保了数据与Oracle数据库字段定义的一致性。