"""
执行正确的海关数据导入流程
先清空表，然后依次导入浙江（07）和北京（08）数据
"""

import subprocess
import sys
import os
from datetime import datetime

def run_command(command, description):
    """
    执行命令并显示结果
    """
    print(f"\n{'='*80}")
    print(f"执行: {description}")
    print(f"命令: {command}")
    print(f"{'='*80}")
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, encoding='utf-8')
        
        if result.stdout:
            print("输出:")
            print(result.stdout)
        
        if result.stderr:
            print("错误:")
            print(result.stderr)
        
        if result.returncode == 0:
            print(f"✅ {description} 完成")
            return True
        else:
            print(f"❌ {description} 失败")
            return False
            
    except Exception as e:
        print(f"❌ 执行 {description} 时出错: {e}")
        return False

def main():
    """
    主执行流程
    """
    print("海关数据正确导入流程")
    print("=" * 80)
    print("执行顺序：")
    print("1. 浙江数据转换和导入（DATA_SOURCE = '07'）")
    print("2. 北京数据转换和导入（DATA_SOURCE = '02'）")
    print("=" * 80)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查必要文件是否存在
    required_files = [
        'batch_transform_excel_smart.py',  # 浙江转换脚本
        'batch_import_to_oracle.py',      # 浙江导入脚本
        'beijing_customs_comprehensive_import.py',  # 北京转换脚本
        'beijing_import_to_oracle_complete.py'     # 北京导入脚本
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件:")
        for file in missing_files:
            print(f"  - {file}")
        return
    
    # 第一步：浙江数据处理
    print(f"\n🇨🇳 第一步：处理浙江数据（DATA_SOURCE = '07'）")
    print("-" * 50)
    
    # 浙江数据转换（如果需要）
    zj_transform_success = run_command(
        "python batch_transform_excel_smart.py",
        "浙江数据Excel转CSV"
    )
    
    if not zj_transform_success:
        print("❌ 浙江数据转换失败，停止执行")
        return
    
    # 浙江数据导入
    zj_import_success = run_command(
        "python batch_import_to_oracle.py",
        "浙江数据导入Oracle"
    )
    
    if not zj_import_success:
        print("❌ 浙江数据导入失败，停止执行")
        return
    
    # 第二步：北京数据处理
    print(f"\n🏛️ 第二步：处理北京数据（DATA_SOURCE = '02'）")
    print("-" * 50)
    
    # 北京数据转换
    bj_transform_success = run_command(
        "python beijing_customs_comprehensive_import.py",
        "北京数据Excel转CSV"
    )
    
    if not bj_transform_success:
        print("❌ 北京数据转换失败")
        return
    
    # 北京数据导入
    bj_import_success = run_command(
        "python beijing_import_to_oracle_complete.py",
        "北京数据导入Oracle"
    )
    
    if not bj_import_success:
        print("❌ 北京数据导入失败")
        return
    
    # 完成总结
    print(f"\n{'='*80}")
    print("✅ 所有数据导入完成！")
    print(f"{'='*80}")
    print("数据源标识：")
    print("- 浙江数据：DATA_SOURCE = '07'")
    print("- 北京数据：DATA_SOURCE = '02'")
    print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)

if __name__ == '__main__':
    main()