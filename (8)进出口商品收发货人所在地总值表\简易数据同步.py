import cx_Oracle as oracle
import pandas as pd
import logging
import os
import configparser
import sys
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    filename='data_sync.log'
)
logger = logging.getLogger('data_sync')

# 数据库配置
DB_CONFIG = {
    'TEST': {
        'username': 'manifest',
        'password': 'manifest',
        'host': 'ip',
        'port': '1522',
        'service_name': 'TEST'
    },
    'PROD': {
        'username': 'manifest2',
        'password': 'manifest',
        'host': 'ip',
        'port': '1521',
        'service_name': 'test2'
    }
}

def get_connection_string(db_config):
    """根据配置生成数据库连接字符串"""
    return f"{db_config['username']}/{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['service_name']}"

def sync_data(source_table="CS_SINGLEWINDOW_PRODUCT_CODE", target_table="T_BC_SW_TS_CODE", 
              where_clause=None, is_first_sync=False):
    """同步数据从测试环境到生产环境"""
    print(f"开始同步数据: {source_table} -> {target_table}")
    print(f"条件: {where_clause if where_clause else '全量数据'}")
    
    start_time = datetime.now()
    
    # 连接数据库
    source_conn = None
    target_conn = None
    
    try:
        # 连接源数据库（测试环境）
        source_conn = oracle.connect(get_connection_string(DB_CONFIG['TEST']))
        print("已连接到测试环境数据库")
        
        # 连接目标数据库（生产环境）
        target_conn = oracle.connect(get_connection_string(DB_CONFIG['PROD']))
        print("已连接到生产环境数据库")
        
        # 检查目标表是否存在
        target_cursor = target_conn.cursor()
        try:
            # 尝试查询目标表，如果不存在会抛出异常
            target_cursor.execute(f"SELECT COUNT(*) FROM {target_table} WHERE ROWNUM=1")
            table_exists = True
        except:
            table_exists = False
        finally:
            target_cursor.close()
        
        # 如果表不存在，需要创建
        if not table_exists:
            print(f"目标表 {target_table} 不存在，正在创建...")
            
            # 获取源表结构
            desc_cursor = source_conn.cursor()
            try:
                # 先获取源表的列定义
                desc_cursor.execute(f"SELECT * FROM {source_table} WHERE 1=0")
                column_info = desc_cursor.description
                
                # 创建目标表
                create_columns = []
                for col in column_info:
                    col_name = col[0]
                    col_type = get_oracle_type(col)
                    create_columns.append(f"{col_name} {col_type}")
                
                create_sql = f"CREATE TABLE {target_table} ({', '.join(create_columns)})"
                
                # 执行创建表
                create_cursor = target_conn.cursor()
                try:
                    create_cursor.execute(create_sql)
                    target_conn.commit()
                    print(f"成功创建表 {target_table}")
                except Exception as e:
                    target_conn.rollback()
                    print(f"创建表失败: {str(e)}")
                    raise
                finally:
                    create_cursor.close()
            finally:
                desc_cursor.close()
        
        # 构建查询语句
        source_cursor = source_conn.cursor()
        target_cursor = target_conn.cursor()
        
        try:
            # 构建查询SQL
            query_sql = f"SELECT * FROM {source_table}"
            if where_clause:
                query_sql += f" WHERE {where_clause}"
                
            print(f"执行查询: {query_sql}")
            source_cursor.execute(query_sql)
            
            # 获取列信息
            columns = [col[0] for col in source_cursor.description]
            
            # 如果是第一次同步，先清空目标表
            if is_first_sync and table_exists:
                try:
                    target_cursor.execute(f"DELETE FROM {target_table}")
                    target_conn.commit()
                    print(f"已清空目标表 {target_table}")
                except Exception as e:
                    target_conn.rollback()
                    print(f"清空表失败: {str(e)}")
            
            # 准备批量插入
            placeholders = ', '.join([f':{i+1}' for i in range(len(columns))])
            insert_sql = f"INSERT INTO {target_table} ({', '.join(columns)}) VALUES ({placeholders})"
            
            # 批量拉取和插入数据
            batch_size = 1000
            total_rows = 0
            rows = source_cursor.fetchmany(batch_size)
            
            while rows:
                try:
                    target_cursor.executemany(insert_sql, rows)
                    target_conn.commit()
                    total_rows += len(rows)
                    print(f"已插入 {total_rows} 行数据")
                except Exception as e:
                    target_conn.rollback()
                    print(f"插入数据批次失败: {str(e)}")
                    logger.error(f"插入数据批次失败: {str(e)}")
                
                # 获取下一批数据
                rows = source_cursor.fetchmany(batch_size)
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            print(f"同步完成! 总共插入 {total_rows} 行数据")
            print(f"开始时间: {start_time}, 结束时间: {end_time}, 耗时: {duration} 秒")
            
        finally:
            source_cursor.close()
            target_cursor.close()
            
    except Exception as e:
        print(f"同步过程中发生错误: {str(e)}")
        logger.error(f"同步过程中发生错误: {str(e)}")
    finally:
        if source_conn:
            source_conn.close()
        if target_conn:
            target_conn.close()

def get_oracle_type(col_desc):
    """根据cx_Oracle列描述返回Oracle数据类型"""
    col_type = col_desc[1]
    col_size = col_desc[2]
    col_scale = col_desc[5]
    
    # Oracle NUMBER类型
    if col_type == oracle.NUMBER:
        if col_scale is not None and col_scale > 0:
            return f"NUMBER({col_size},{col_scale})"
        elif col_size is not None:
            return f"NUMBER({col_size})"
        else:
            return "NUMBER"
    
    # Oracle字符类型
    elif col_type == oracle.STRING:
        return f"VARCHAR2({col_size})"
    
    # Oracle日期类型
    elif col_type == oracle.DATETIME:
        return "DATE"
    
    # 其他类型简单处理
    else:
        return "VARCHAR2(4000)"

def print_usage():
    print("""
使用方法:
  python 简易数据同步.py [参数]

参数:
  --full              全量同步，会清空目标表 (默认增量同步)
  --source=表名       源表名称 (默认: CS_SINGLEWINDOW_PRODUCT_CODE)
  --target=表名       目标表名称 (默认: T_BC_SW_TS_CODE)
  --where=条件        过滤条件 (默认: createtime > sysdate - 0.5)

示例:
  python 简易数据同步.py --full                     # 全量同步
  python 简易数据同步.py                           # 增量同步
  python 简易数据同步.py --where="createtime > to_date('2023-01-01','yyyy-mm-dd')"   # 指定条件
  python 简易数据同步.py --source=表1 --target=表2  # 指定表名
    """)

if __name__ == "__main__":
    # 解析命令行参数
    is_full = False
    source_table = "CS_SINGLEWINDOW_PRODUCT_CODE"
    target_table = "T_BC_SW_TS_CODE"
    where_clause = "createtime > sysdate - 0.5"
    
    # 简单的参数解析
    if len(sys.argv) > 1:
        for arg in sys.argv[1:]:
            if arg == "--full":
                is_full = True
            elif arg.startswith("--source="):
                source_table = arg.split("=", 1)[1]
            elif arg.startswith("--target="):
                target_table = arg.split("=", 1)[1]
            elif arg.startswith("--where="):
                where_clause = arg.split("=", 1)[1]
            elif arg in ["--help", "-h"]:
                print_usage()
                sys.exit(0)
    
    # 如果是全量同步，不使用where子句
    if is_full:
        where_clause = None
    
    # 开始同步
    sync_data(source_table, target_table, where_clause, is_full) 