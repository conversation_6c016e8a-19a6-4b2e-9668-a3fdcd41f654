from DrissionPage import ChromiumPage
import time
import re
import os

def is_target_table(text):
    """检查链接文本是否是我们需要的目标表格"""
    if "北京地区" in text:
        # (1) 北京地区进出口商品总值表B：月度表
        if "（1）" in text and "总值表B" in text and "月度表" in text:
            return True, "(1)"
        # (2) 北京地区进出口商品国别（地区）总值表
        elif "（2）" in text and "国别" in text and "地区" in text and "总值表" in text:
            return True, "(2)"
        # (5) 北京地区进出口商品贸易方式总值表
        elif "（5）" in text and "贸易方式" in text and "总值表" in text and "企业性质" not in text:
            return True, "(5)"
        # (6) 北京地区出口商品贸易方式企业性质总值表
        elif "（6）" in text and "出口" in text and "贸易方式" in text and "企业性质" in text:
            return True, "(6)"
        # (7) 北京地区进口商品贸易方式企业性质总值表
        elif "（7）" in text and "进口" in text and "贸易方式" in text and "企业性质" in text:
            return True, "(7)"
        # (8) 北京地区出口主要商品量值表
        elif "（8）" in text and "出口" in text and "主要商品" in text and "量值表" in text:
            return True, "(8)"
        # (9) 北京地区进口主要商品量值表
        elif "（9）" in text and "进口" in text and "主要商品" in text and "量值表" in text:
            return True, "(9)"
    return False, None

def collect_more_pages():
    """完整收集北京海关数据，强制翻到第24页"""

    print("=== 完整收集北京海关数据（强制翻到第24页）===")

    page = ChromiumPage(timeout=30)

    try:
        all_target_links = []

        # 检查是否存在已有的链接文件，实现增量收集
        existing_links_file = "北京海关数据链接_完整版.txt"
        existing_links = set()
        start_page = 1

        if os.path.exists(existing_links_file):
            print("发现已有链接文件，进行增量收集...")
            with open(existing_links_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                max_page = 0
                for line in lines[1:]:  # 跳过标题行
                    line = line.strip()
                    if line:
                        parts = line.split('\t')
                        if len(parts) >= 5:
                            existing_links.add(parts[4])  # URL
                            page_num = int(parts[0])
                            max_page = max(max_page, page_num)

                            # 重新构建已有链接
                            link_info = {
                                'text': parts[3],
                                'url': parts[4],
                                'year': parts[1],
                                'table_key': parts[2],
                                'page': page_num
                            }
                            all_target_links.append(link_info)

                start_page = max_page + 1 if max_page > 0 else 1
                print(f"已有 {len(existing_links)} 个链接，从第 {start_page} 页开始继续收集")
        else:
            print("未发现已有链接文件，从第1页开始全新收集")

        # 根据起始页面决定访问策略
        if start_page == 1:
            # 从第一页开始
            start_url = "http://beijing.customs.gov.cn/beijing_customs/434756/434804/2941702/434773/434774/index.html"
            page.get(start_url)
            time.sleep(3)
            current_page_num = 1

            # 处理第1页
            print(f"\n=== 处理第 {current_page_num} 页 ===")
            page_target_links = []
            all_links = page.eles('tag:a')

            for link in all_links:
                link_text = link.text.strip()
                href = link.attr('href')

                if not href or not link_text:
                    continue

                # 构建完整URL
                if href.startswith('/'):
                    full_url = "http://beijing.customs.gov.cn" + href
                else:
                    full_url = href

                # 跳过已存在的链接
                if full_url in existing_links:
                    continue

                # 检查是否包含目标年份
                if any(year in link_text for year in ['2024', '2025', '2023']):
                    # 使用目标表格检查函数
                    is_target, table_key = is_target_table(link_text)

                    if is_target:
                        link_info = {
                            'text': link_text,
                            'url': full_url,
                            'year': '2023' if '2023' in link_text else ('2024' if '2024' in link_text else '2025'),
                            'table_key': table_key,
                            'page': current_page_num
                        }

                        page_target_links.append(link_info)
                        all_target_links.append(link_info)
                        existing_links.add(full_url)

                        print(f"找到目标: {table_key} - {link_text}")
            print(f"第 {current_page_num} 页找到 {len(page_target_links)} 个新目标链接")

            # 从第2页开始翻页
            start_page = 2
        else:
            # 从指定页面开始，直接访问该页面
            if start_page == 2:
                start_url = "http://beijing.customs.gov.cn/beijing_customs/434756/434804/2941702/434773/434774/index.html"
                page.get(start_url)
                time.sleep(3)
            else:
                start_url = f"http://beijing.customs.gov.cn/beijing_customs/434756/434804/2941702/434773/434774/16673809-{start_page}.html"
                page.get(start_url)
                time.sleep(3)
            current_page_num = start_page - 1  # 因为循环会先+1

        # 强制翻页到第24页，不管是否找到2023年数据
        while current_page_num < 24:
            current_page_num += 1

            print(f"\n=== 尝试翻到第 {current_page_num} 页 ===")

            try:
                # 查找"下一页"按钮
                next_page_buttons = page.eles("下一页")
                next_button_found = False

                for next_btn in next_page_buttons:
                    if next_btn.tag == 'a' and next_btn.attr('onclick'):
                        print(f"找到下一页按钮，点击翻页...")
                        try:
                            next_btn.click()
                            time.sleep(3)
                            next_button_found = True
                            break
                        except Exception as e:
                            print(f"点击下一页按钮失败: {e}")
                            continue

                if not next_button_found:
                    print(f"未找到可点击的下一页按钮，尝试直接访问URL")
                    url = f"http://beijing.customs.gov.cn/beijing_customs/434756/434804/2941702/434773/434774/16673809-{current_page_num}.html"
                    page.get(url)
                    time.sleep(3)

                print(f"页面标题: {page.title}")

                # 检查页面是否有效
                page_content = page.html
                if "404" in page.title or "未找到" in page_content or ("统计数据" not in page_content and "北京地区" not in page_content):
                    print(f"第 {current_page_num} 页无效或无数据，停止翻页")
                    break

                # 查找目标链接
                page_target_links = []
                all_links = page.eles('tag:a')

                for link in all_links:
                    link_text = link.text.strip()
                    href = link.attr('href')

                    if not href or not link_text:
                        continue

                    # 构建完整URL
                    if href.startswith('/'):
                        full_url = "http://beijing.customs.gov.cn" + href
                    else:
                        full_url = href

                    # 跳过已存在的链接
                    if full_url in existing_links:
                        continue

                    # 检查是否包含目标年份
                    if any(year in link_text for year in ['2024', '2025', '2023']):
                        # 使用目标表格检查函数
                        is_target, table_key = is_target_table(link_text)

                        if is_target:
                            link_info = {
                                'text': link_text,
                                'url': full_url,
                                'year': '2023' if '2023' in link_text else ('2024' if '2024' in link_text else '2025'),
                                'table_key': table_key,
                                'page': current_page_num
                            }

                            page_target_links.append(link_info)
                            all_target_links.append(link_info)
                            existing_links.add(full_url)

                            print(f"找到目标: {table_key} - {link_text}")

                print(f"第 {current_page_num} 页找到 {len(page_target_links)} 个新目标链接")

            except Exception as e:
                print(f"访问第 {current_page_num} 页时出错: {e}")
                # 继续尝试下一页，可能只是网络问题
                continue

            time.sleep(2)  # 防止请求过快

        print(f"\n=== 收集完成（强制翻到第24页）===")
        print(f"总共找到 {len(all_target_links)} 个目标链接")

        # 按年份统计
        year_2023_count = len([link for link in all_target_links if link['year'] == '2023'])
        year_2024_count = len([link for link in all_target_links if link['year'] == '2024'])
        year_2025_count = len([link for link in all_target_links if link['year'] == '2025'])

        print(f"2023年数据: {year_2023_count} 个")
        print(f"2024年数据: {year_2024_count} 个")
        print(f"2025年数据: {year_2025_count} 个")

        # 按表格类型统计
        table_stats = {}
        for link in all_target_links:
            table_key = link['table_key']
            if table_key not in table_stats:
                table_stats[table_key] = 0
            table_stats[table_key] += 1

        print("\n按表格类型统计:")
        for table_key, count in sorted(table_stats.items()):
            print(f"  {table_key}: {count} 个")



        # 保存完整结果
        with open('北京海关数据链接_完整版.txt', 'w', encoding='utf-8') as f:
            f.write("页码\t年份\t表格编号\t链接文本\t完整URL\n")
            for link in all_target_links:
                f.write(f"{link['page']}\t{link['year']}\t{link['table_key']}\t{link['text']}\t{link['url']}\n")

        print("完整结果已保存到: 北京海关数据链接_完整版.txt")

        return all_target_links

    except Exception as e:
        print(f"收集过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return []

    finally:
        page.quit()

if __name__ == "__main__":
    collect_more_pages()