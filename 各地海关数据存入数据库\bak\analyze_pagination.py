from DrissionPage import ChromiumPage
import time
import os
import re

def analyze_pagination():
    """分析北京海关网站的翻页功能"""

    print("=== 分析北京海关网站翻页功能 ===")

    page = ChromiumPage(timeout=30)

    try:
        # 访问北京海关统计数据页面
        beijing_url = "http://beijing.customs.gov.cn/beijing_customs/434756/434804/2941702/434773/434774/index.html"
        print(f"正在访问: {beijing_url}")

        page.get(beijing_url)
        time.sleep(5)

        print(f"页面标题: {page.title}")

        # 查找所有包含年份的链接
        all_links = page.eles('tag:a')
        year_links = []

        for link in all_links:
            link_text = link.text.strip()
            if any(year in link_text for year in ['2024', '2025', '2023', '2022']):
                year_links.append({
                    'text': link_text,
                    'href': link.attr('href')
                })

        print(f"\n找到 {len(year_links)} 个包含年份的链接:")
        for i, link in enumerate(year_links):
            print(f"  {i+1}. {link['text']}")

        # 查找翻页相关元素
        print("\n查找翻页相关元素:")
        pagination_keywords = ["下一页", "上一页", "页", "更多", "翻页", "next", "prev", "page"]

        for keyword in pagination_keywords:
            elements = page.eles(keyword)
            if elements:
                print(f"  包含'{keyword}'的元素: {len(elements)}个")
                for elem in elements[:3]:
                    if elem.tag == 'a':
                        print(f"    链接: {elem.text[:30]}... -> {elem.attr('href')}")

        # 查找数字页码
        print("\n查找数字页码:")
        for i in range(1, 10):
            page_elements = page.eles(str(i))
            if page_elements:
                for elem in page_elements:
                    if elem.tag == 'a' and len(elem.text.strip()) <= 2:
                        print(f"    可能的页码链接: '{elem.text}' -> {elem.attr('href')}")

        # 保存完整的页面源码用于进一步分析
        with open('北京海关主页完整源码.html', 'w', encoding='utf-8') as f:
            f.write(page.html)
        print("\n完整页面源码已保存到: 北京海关主页完整源码.html")

    except Exception as e:
        print(f"分析过程中出错: {e}")
        import traceback
        traceback.print_exc()

    finally:
        page.quit()

if __name__ == "__main__":
    analyze_pagination()