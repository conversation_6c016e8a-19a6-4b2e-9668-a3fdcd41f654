import pandas as pd

def count_sql_parameters():
    """计算SQL语句中的绑定变量数量"""
    
    # SQL语句中的字段列表
    sql_fields = [
        'STAT_DATE', 'STAT_TYPE', 'STAT_NAME', 'STAT_CODE', 'STAT_CONTENT_RAW', 'STAT_CONTENT_CLEANSE',
        'ACC_A_CNY_AMOUNT', 'ACC_A_CNY_YOY', 'ACC_A_USD_AMOUNT', 'ACC_A_USD_YOY',
        'MON_A_CNY_AMOUNT', 'MON_A_CNY_YOY', 'MON_A_USD_AMOUNT', 'MON_A_USD_YOY',
        'ACC_E_CNY_AMOUNT', 'ACC_E_CNY_YOY', 'ACC_E_USD_AMOUNT', 'ACC_E_USD_YOY',
        'MON_E_CNY_AMOUNT', 'MON_E_CNY_YOY', 'MON_E_USD_AMOUNT', 'MON_E_USD_YOY',
        'ACC_I_CNY_AMOUNT', 'ACC_I_CNY_YOY', 'ACC_I_USD_AMOUNT', 'ACC_I_USD_YOY',
        'MON_I_CNY_AMOUNT', 'MON_I_CNY_YOY', 'MON_I_USD_AMOUNT', 'MON_I_USD_YOY',
        'ACC_E_AMOUNT', 'ACC_E_AMOUNT_UNIT', 'ACC_E_AMOUNT_YOY',
        'MON_E_AMOUNT', 'MON_E_AMOUNT_UNIT', 'MON_E_AMOUNT_YOY',
        'ACC_I_AMOUNT', 'ACC_I_AMOUNT_UNIT', 'ACC_I_AMOUNT_YOY',
        'MON_I_AMOUNT', 'MON_I_AMOUNT_UNIT', 'MON_I_AMOUNT_YOY',
        'RANK_MARKERS', 'DATA_SOURCE', 'EMPHASIS_OR_EMERGING_MARK', 'CREATE_TIME'
    ]
    
    print(f"SQL字段数量: {len(sql_fields)}")
    print("SQL字段列表:")
    for i, field in enumerate(sql_fields):
        print(f"  {i+1:2d}. {field}")
    
    return sql_fields

def count_data_parameters():
    """计算数据行中的参数数量"""
    
    # 模拟数据行构建
    data_fields = [
        # 1-6: 基础信息字段
        'STAT_DATE', 'STAT_TYPE', 'STAT_NAME', 'STAT_CODE', 'STAT_CONTENT_RAW', 'STAT_CONTENT_CLEANSE',
        # 7-10: 累计进出口金额字段
        'ACC_A_CNY_AMOUNT', 'ACC_A_CNY_YOY', 'ACC_A_USD_AMOUNT', 'ACC_A_USD_YOY',
        # 11-14: 当月进出口金额字段
        'MON_A_CNY_AMOUNT', 'MON_A_CNY_YOY', 'MON_A_USD_AMOUNT', 'MON_A_USD_YOY',
        # 15-18: 累计出口金额字段
        'ACC_E_CNY_AMOUNT', 'ACC_E_CNY_YOY', 'ACC_E_USD_AMOUNT', 'ACC_E_USD_YOY',
        # 19-22: 当月出口金额字段
        'MON_E_CNY_AMOUNT', 'MON_E_CNY_YOY', 'MON_E_USD_AMOUNT', 'MON_E_USD_YOY',
        # 23-26: 累计进口金额字段
        'ACC_I_CNY_AMOUNT', 'ACC_I_CNY_YOY', 'ACC_I_USD_AMOUNT', 'ACC_I_USD_YOY',
        # 27-30: 当月进口金额字段
        'MON_I_CNY_AMOUNT', 'MON_I_CNY_YOY', 'MON_I_USD_AMOUNT', 'MON_I_USD_YOY',
        # 31-33: 累计出口数量字段
        'ACC_E_AMOUNT', 'ACC_E_AMOUNT_UNIT', 'ACC_E_AMOUNT_YOY',
        # 34-36: 当月出口数量字段
        'MON_E_AMOUNT', 'MON_E_AMOUNT_UNIT', 'MON_E_AMOUNT_YOY',
        # 37-39: 累计进口数量字段（包含ACC_I_AMOUNT）
        'ACC_I_AMOUNT', 'ACC_I_AMOUNT_UNIT', 'ACC_I_AMOUNT_YOY',
        # 40-42: 当月进口数量字段
        'MON_I_AMOUNT', 'MON_I_AMOUNT_UNIT', 'MON_I_AMOUNT_YOY',
        # 43-46: 其他字段
        'RANK_MARKERS', 'DATA_SOURCE', 'EMPHASIS_OR_EMERGING_MARK', 'CREATE_TIME'
    ]
    
    print(f"\n数据行参数数量: {len(data_fields)}")
    print("数据行参数列表:")
    for i, field in enumerate(data_fields):
        print(f"  {i+1:2d}. {field}")
    
    return data_fields

def compare_fields():
    """比较SQL字段和数据字段"""
    
    sql_fields = count_sql_parameters()
    data_fields = count_data_parameters()
    
    print(f"\n=== 字段对比 ===")
    print(f"SQL字段数量: {len(sql_fields)}")
    print(f"数据字段数量: {len(data_fields)}")
    
    if len(sql_fields) == len(data_fields):
        print("✅ 字段数量匹配")
        
        # 检查字段顺序
        all_match = True
        for i in range(len(sql_fields)):
            if sql_fields[i] != data_fields[i]:
                print(f"❌ 第{i+1}个字段不匹配: SQL={sql_fields[i]}, 数据={data_fields[i]}")
                all_match = False
        
        if all_match:
            print("✅ 字段顺序完全匹配")
        else:
            print("❌ 字段顺序不匹配")
    else:
        print("❌ 字段数量不匹配")
        
        # 找出差异
        sql_set = set(sql_fields)
        data_set = set(data_fields)
        
        missing_in_data = sql_set - data_set
        extra_in_data = data_set - sql_set
        
        if missing_in_data:
            print(f"数据中缺失的字段: {missing_in_data}")
        
        if extra_in_data:
            print(f"数据中多余的字段: {extra_in_data}")

def test_csv_fields():
    """测试CSV文件的字段"""
    
    csv_file = '北京海关数据_完整转换结果/T_STATISTICAL_CUS_TOTAL_BEIJING_2024_(5)_（5）北京地区进出口商品贸易方式总值表（2024年1-10月）.csv'
    
    try:
        df = pd.read_csv(csv_file, encoding='utf-8-sig')
        csv_fields = list(df.columns)
        
        print(f"\n=== CSV文件字段 ===")
        print(f"CSV字段数量: {len(csv_fields)}")
        print("CSV字段列表:")
        for i, field in enumerate(csv_fields):
            print(f"  {i+1:2d}. {field}")
        
        return csv_fields
        
    except Exception as e:
        print(f"读取CSV文件出错: {e}")
        return []

if __name__ == "__main__":
    print("=== 参数数量调试 ===")
    
    compare_fields()
    test_csv_fields()
