import pandas as pd
import cx_Oracle
import sys
import os
from datetime import datetime

def import_beijing_data_to_oracle():
    """
    将北京CSV数据导入Oracle数据库的T_STATISTICAL_CUS_TOTAL_CS表
    """
    
    # 数据库连接配置
    db_config = {
        'user': 'manifest_dcb',
        'password': 'manifest_dcb',
        'dsn': '192.168.1.151/TEST'
    }
    
    # CSV文件路径
    csv_file = 'T_STATISTICAL_CUS_TOTAL_BEIJING.csv'
    
    try:
        # 检查CSV文件是否存在
        if not os.path.exists(csv_file):
            print(f"错误：找不到CSV文件 {csv_file}")
            return False
        
        # 读取CSV文件
        print(f"正在读取CSV文件: {csv_file}")
        df = pd.read_csv(csv_file, encoding='utf-8-sig')
        
        print(f"CSV文件读取成功")
        print(f"总记录数: {len(df)}")
        print(f"字段数: {len(df.columns)}")
        
        # 连接Oracle数据库
        print(f"正在连接Oracle数据库...")
        conn = cx_Oracle.connect(**db_config)
        cursor = conn.cursor()
        
        print("数据库连接成功")
        
        # 清空表（可选）
        print("正在清空目标表...")
        cursor.execute("TRUNCATE TABLE T_STATISTICAL_CUS_TOTAL_CS")
        conn.commit()
        print("目标表已清空")
        
        # 准备插入SQL
        insert_sql = """
        INSERT INTO T_STATISTICAL_CUS_TOTAL_CS (
            STAT_DATE, STAT_TYPE, STAT_NAME, STAT_CODE, STAT_CONTENT_RAW, STAT_CONTENT_CLEANSE,
            ACC_A_CNY_AMOUNT, ACC_A_CNY_YOY, ACC_A_USD_AMOUNT, ACC_A_USD_YOY,
            MON_A_CNY_AMOUNT, MON_A_CNY_YOY, MON_A_USD_AMOUNT, MON_A_USD_YOY,
            ACC_E_CNY_AMOUNT, ACC_E_CNY_YOY, ACC_E_USD_AMOUNT, ACC_E_USD_YOY,
            MON_E_CNY_AMOUNT, MON_E_CNY_YOY, MON_E_USD_AMOUNT, MON_E_USD_YOY,
            ACC_I_CNY_AMOUNT, ACC_I_CNY_YOY, ACC_I_USD_AMOUNT, ACC_I_USD_YOY,
            MON_I_CNY_AMOUNT, MON_I_CNY_YOY, MON_I_USD_AMOUNT, MON_I_USD_YOY,
            ACC_E_AMOUNT, ACC_E_AMOUNT_UNIT, ACC_E_AMOUNT_YOY,
            MON_E_AMOUNT, MON_E_AMOUNT_UNIT, MON_E_AMOUNT_YOY,
            ACC_I_AMOUNT, ACC_I_AMOUNT_UNIT, ACC_I_AMOUNT_YOY,
            MON_I_AMOUNT, MON_I_AMOUNT_UNIT, MON_I_AMOUNT_YOY,
            RANK_MARKERS, DATA_SOURCE, EMPHASIS_OR_EMERGING_MARK, CREATE_TIME
        ) VALUES (
            :1, :2, :3, :4, :5, :6,
            :7, :8, :9, :10,
            :11, :12, :13, :14,
            :15, :16, :17, :18,
            :19, :20, :21, :22,
            :23, :24, :25, :26,
            :27, :28, :29, :30,
            :31, :32, :33,
            :34, :35, :36,
            :37, :38, :39,
            :40, :41, :42,
            :43, :44, :45, :46
        )
        """
        
        # 批量插入数据
        batch_size = 100
        total_rows = len(df)
        inserted_rows = 0
        
        print(f"开始插入数据（批量大小: {batch_size}）...")
        
        for start_idx in range(0, total_rows, batch_size):
            end_idx = min(start_idx + batch_size, total_rows)
            batch_df = df.iloc[start_idx:end_idx]
            
            # 准备批量数据
            batch_data = []
            for _, row in batch_df.iterrows():
                # 处理数值字段的空值
                def get_numeric_value(value):
                    if pd.isna(value) or value == '' or value == 'nan':
                        return None
                    try:
                        return float(value)
                    except (ValueError, TypeError):
                        return None
                
                # 处理字符串字段的空值
                def get_string_value(value):
                    if pd.isna(value) or value == '' or value == 'nan':
                        return None
                    return str(value)
                
                # 构建数据行
                data_row = (
                    get_string_value(row['STAT_DATE']),
                    get_string_value(row['STAT_TYPE']),
                    get_string_value(row['STAT_NAME']),
                    get_string_value(row['STAT_CODE']),
                    get_string_value(row['STAT_CONTENT_RAW']),
                    get_string_value(row['STAT_CONTENT_CLEANSE']),
                    get_numeric_value(row['ACC_A_CNY_AMOUNT']),
                    get_numeric_value(row['ACC_A_CNY_YOY']),
                    get_numeric_value(row['ACC_A_USD_AMOUNT']),
                    get_numeric_value(row['ACC_A_USD_YOY']),
                    get_numeric_value(row['MON_A_CNY_AMOUNT']),
                    get_numeric_value(row['MON_A_CNY_YOY']),
                    get_numeric_value(row['MON_A_USD_AMOUNT']),
                    get_numeric_value(row['MON_A_USD_YOY']),
                    get_numeric_value(row['ACC_E_CNY_AMOUNT']),
                    get_numeric_value(row['ACC_E_CNY_YOY']),
                    get_numeric_value(row['ACC_E_USD_AMOUNT']),
                    get_numeric_value(row['ACC_E_USD_YOY']),
                    get_numeric_value(row['MON_E_CNY_AMOUNT']),
                    get_numeric_value(row['MON_E_CNY_YOY']),
                    get_numeric_value(row['MON_E_USD_AMOUNT']),
                    get_numeric_value(row['MON_E_USD_YOY']),
                    get_numeric_value(row['ACC_I_CNY_AMOUNT']),
                    get_numeric_value(row['ACC_I_CNY_YOY']),
                    get_numeric_value(row['ACC_I_USD_AMOUNT']),
                    get_numeric_value(row['ACC_I_USD_YOY']),
                    get_numeric_value(row['MON_I_CNY_AMOUNT']),
                    get_numeric_value(row['MON_I_CNY_YOY']),
                    get_numeric_value(row['MON_I_USD_AMOUNT']),
                    get_numeric_value(row['MON_I_USD_YOY']),
                    get_numeric_value(row['ACC_E_AMOUNT']),
                    get_string_value(row['ACC_E_AMOUNT_UNIT']),
                    get_numeric_value(row['ACC_E_AMOUNT_YOY']),
                    get_numeric_value(row['MON_E_AMOUNT']),
                    get_string_value(row['MON_E_AMOUNT_UNIT']),
                    get_numeric_value(row['MON_E_AMOUNT_YOY']),
                    get_numeric_value(row['ACC_I_AMOUNT']),
                    get_string_value(row['ACC_I_AMOUNT_UNIT']),
                    get_numeric_value(row['ACC_I_AMOUNT_YOY']),
                    get_numeric_value(row['MON_I_AMOUNT']),
                    get_string_value(row['MON_I_AMOUNT_UNIT']),
                    get_numeric_value(row['MON_I_AMOUNT_YOY']),
                    get_string_value(row['RANK_MARKERS']),
                    get_string_value(row['DATA_SOURCE']),
                    get_string_value(row['EMPHASIS_OR_EMERGING_MARK']),
                    get_string_value(row['CREATE_TIME'])
                )
                batch_data.append(data_row)
            
            # 执行批量插入
            cursor.executemany(insert_sql, batch_data)
            conn.commit()
            
            inserted_rows += len(batch_data)
            progress = (inserted_rows / total_rows) * 100
            print(f"已插入 {inserted_rows}/{total_rows} 条记录 ({progress:.1f}%)")
        
        print(f"数据插入完成！共插入 {inserted_rows} 条记录")
        
        # 验证数据
        print("正在验证数据...")
        cursor.execute("SELECT COUNT(*) FROM T_STATISTICAL_CUS_TOTAL_CS")
        db_count = cursor.fetchone()[0]
        print(f"数据库中记录数: {db_count}")
        
        # 按统计类型分组统计
        cursor.execute("""
            SELECT STAT_TYPE, STAT_NAME, COUNT(*) 
            FROM T_STATISTICAL_CUS_TOTAL_CS 
            GROUP BY STAT_TYPE, STAT_NAME 
            ORDER BY STAT_TYPE
        """)
        
        print("\n按统计类型分组统计:")
        for row in cursor.fetchall():
            print(f"  {row[0]} ({row[1]}): {row[2]} 条记录")
        
        # 关闭连接
        cursor.close()
        conn.close()
        
        print("数据导入成功完成！")
        return True
        
    except cx_Oracle.DatabaseError as e:
        print(f"数据库错误: {e}")
        return False
    except Exception as e:
        print(f"错误: {e}")
        return False
    finally:
        try:
            if 'conn' in locals():
                conn.close()
        except:
            pass

if __name__ == '__main__':
    print("=== 北京海关统计数据导入Oracle数据库 ===")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    success = import_beijing_data_to_oracle()
    
    print()
    print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"导入结果: {'成功' if success else '失败'}")
    
    if success:
        print("您现在可以在Oracle数据库中查询表 T_STATISTICAL_CUS_TOTAL_CS")
    else:
        print("导入失败，请检查错误信息并重试")