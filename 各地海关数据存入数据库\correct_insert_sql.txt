字段顺序：
 1. ACC_A_CNY_AMOUNT
 2. ACC_A_CNY_YOY
 3. ACC_A_USD_AMOUNT
 4. ACC_A_USD_YOY
 5. ACC_E_AMOUNT
 6. ACC_E_AMOUNT_UNIT
 7. ACC_E_AMOUNT_YOY
 8. ACC_E_CNY_AMOUNT
 9. ACC_E_CNY_YOY
10. ACC_E_USD_AMOUNT
11. ACC_E_USD_YOY
12. ACC_I_AMOUNT
13. ACC_I_AMOUNT_UNIT
14. ACC_I_AMOUNT_YOY
15. ACC_I_CNY_AMOUNT
16. ACC_I_CNY_YOY
17. ACC_I_USD_AMOUNT
18. ACC_I_USD_YOY
19. CREATE_TIME
20. DATA_SOURCE
21. EMPHASIS_OR_EMERGING_MARK
22. MON_A_CNY_AMOUNT
23. MON_A_CNY_YOY
24. MON_A_USD_AMOUNT
25. MON_A_USD_YOY
26. MON_E_AMOUNT
27. MON_E_AMOUNT_UNIT
28. MON_E_AMOUNT_YOY
29. MON_E_CNY_AMOUNT
30. MON_E_CNY_YOY
31. MON_E_USD_AMOUNT
32. MON_E_USD_YOY
33. MON_I_AMOUNT
34. MON_I_AMOUNT_UNIT
35. MON_I_AMOUNT_YOY
36. MON_I_CNY_AMOUNT
37. MON_I_CNY_YOY
38. MON_I_USD_AMOUNT
39. MON_I_USD_YOY
40. RANK_MARKERS
41. STAT_CODE
42. STAT_CONTENT_CLEANSE
43. STAT_CONTENT_RAW
44. STAT_DATE
45. STAT_NAME
46. STAT_TYPE

INSERT SQL语句：
INSERT INTO T_STATISTICAL_CUS_TOTAL_CS (
    ACC_A_CNY_AMOUNT, ACC_A_CNY_YOY, ACC_A_USD_AMOUNT, ACC_A_USD_YOY, ACC_E_AMOUNT, ACC_E_AMOUNT_UNIT, ACC_E_AMOUNT_YOY, ACC_E_CNY_AMOUNT, ACC_E_CNY_YOY, ACC_E_USD_AMOUNT, ACC_E_USD_YOY, ACC_I_AMOUNT, ACC_I_AMOUNT_UNIT, ACC_I_AMOUNT_YOY, ACC_I_CNY_AMOUNT, ACC_I_CNY_YOY, ACC_I_USD_AMOUNT, ACC_I_USD_YOY, CREATE_TIME, DATA_SOURCE, EMPHASIS_OR_EMERGING_MARK, MON_A_CNY_AMOUNT, MON_A_CNY_YOY, MON_A_USD_AMOUNT, MON_A_USD_YOY, MON_E_AMOUNT, MON_E_AMOUNT_UNIT, MON_E_AMOUNT_YOY, MON_E_CNY_AMOUNT, MON_E_CNY_YOY, MON_E_USD_AMOUNT, MON_E_USD_YOY, MON_I_AMOUNT, MON_I_AMOUNT_UNIT, MON_I_AMOUNT_YOY, MON_I_CNY_AMOUNT, MON_I_CNY_YOY, MON_I_USD_AMOUNT, MON_I_USD_YOY, RANK_MARKERS, STAT_CODE, STAT_CONTENT_CLEANSE, STAT_CONTENT_RAW, STAT_DATE, STAT_NAME, STAT_TYPE
) VALUES (
    :1, :2, :3, :4, :5, :6, :7, :8, :9, :10, :11, :12, :13, :14, :15, :16, :17, :18, :19, :20, :21, :22, :23, :24, :25, :26, :27, :28, :29, :30, :31, :32, :33, :34, :35, :36, :37, :38, :39, :40, :41, :42, :43, :44, :45, :46
)