import json
import re

def is_target_table(text):
    """检查链接文本是否是我们需要的目标表格"""
    if "北京地区" in text:
        # (1) 北京地区进出口商品总值表B：月度表
        if "（1）" in text and "总值表B" in text and "月度表" in text:
            return True, "(1)"
        # (2) 北京地区进出口商品国别（地区）总值表
        elif "（2）" in text and "国别" in text and "地区" in text and "总值表" in text:
            return True, "(2)"
        # (5) 北京地区进出口商品贸易方式总值表
        elif "（5）" in text and "贸易方式" in text and "总值表" in text and "企业性质" not in text:
            return True, "(5)"
        # (6) 北京地区出口商品贸易方式企业性质总值表
        elif "（6）" in text and "出口" in text and "贸易方式" in text and "企业性质" in text:
            return True, "(6)"
        # (7) 北京地区进口商品贸易方式企业性质总值表
        elif "（7）" in text and "进口" in text and "贸易方式" in text and "企业性质" in text:
            return True, "(7)"
        # (8) 北京地区出口主要商品量值表
        elif "（8）" in text and "出口" in text and "主要商品" in text and "量值表" in text:
            return True, "(8)"
        # (9) 北京地区进口主要商品量值表
        elif "（9）" in text and "进口" in text and "主要商品" in text and "量值表" in text:
            return True, "(9)"
    return False, None

def filter_target_data():
    """从所有页面数据中筛选出目标数据"""
    
    print("=== 从所有页面数据中筛选目标数据 ===")
    
    # 读取所有页面数据
    try:
        with open('all_pages_data.json', 'r', encoding='utf-8') as f:
            all_pages_data = json.load(f)
    except FileNotFoundError:
        print("未找到 all_pages_data.json 文件，请先运行 save_all_pages_data.py")
        return
    
    target_links = []
    
    # 遍历所有页面数据
    for page_num, page_data in all_pages_data.items():
        page_target_count = 0
        
        for link_data in page_data:
            link_text = link_data['text']
            link_url = link_data['url']
            
            # 检查是否包含目标年份
            if any(year in link_text for year in ['2024', '2025']):
                # 使用目标表格检查函数
                is_target, table_key = is_target_table(link_text)
                
                if is_target:
                    # 提取年份
                    year = '2024' if '2024' in link_text else '2025'
                    
                    target_link = {
                        'page': int(page_num),
                        'year': year,
                        'table_key': table_key,
                        'text': link_text,
                        'url': link_url,
                        'date': link_data.get('date', '')
                    }
                    
                    target_links.append(target_link)
                    page_target_count += 1
                    
                    print(f"第{page_num}页 找到目标: {table_key} - {link_text}")
        
        if page_target_count > 0:
            print(f"第{page_num}页 共找到 {page_target_count} 个目标链接")
    
    # 按页码排序
    target_links.sort(key=lambda x: x['page'])
    
    print(f"\n=== 筛选完成 ===")
    print(f"总共找到 {len(target_links)} 个目标链接")
    
    # 按年份统计
    year_2024_count = len([link for link in target_links if link['year'] == '2024'])
    year_2025_count = len([link for link in target_links if link['year'] == '2025'])
    
    print(f"2024年数据: {year_2024_count} 个")
    print(f"2025年数据: {year_2025_count} 个")
    
    # 按表格类型统计
    table_stats = {}
    for link in target_links:
        table_key = link['table_key']
        if table_key not in table_stats:
            table_stats[table_key] = 0
        table_stats[table_key] += 1
    
    print("\n按表格类型统计:")
    for table_key, count in sorted(table_stats.items()):
        print(f"  {table_key}: {count} 个")
    
    # 保存筛选后的目标数据
    with open('北京海关数据链接_最终版.txt', 'w', encoding='utf-8') as f:
        f.write("页码\t年份\t表格编号\t链接文本\t完整URL\t发布日期\n")
        for link in target_links:
            f.write(f"{link['page']}\t{link['year']}\t{link['table_key']}\t{link['text']}\t{link['url']}\t{link['date']}\n")
    
    print("筛选结果已保存到: 北京海关数据链接_最终版.txt")
    
    # 检查是否找到了2024年1-2月数据
    early_2024_data = [link for link in target_links if '2024年1-2月' in link['text']]
    if early_2024_data:
        print(f"\n✅ 成功找到2024年1-2月数据: {len(early_2024_data)} 个")
        for link in early_2024_data:
            print(f"  {link['table_key']} - {link['text']}")
    else:
        print("\n❌ 未找到2024年1-2月数据")
    
    # 检查2024年数据的时间范围
    print(f"\n2024年数据时间范围分析:")
    time_periods = set()
    for link in target_links:
        if link['year'] == '2024':
            # 提取时间段
            match = re.search(r'2024年(\d+-\d+月|\d+月)', link['text'])
            if match:
                time_periods.add(match.group(1))
    
    for period in sorted(time_periods):
        count = len([link for link in target_links if period in link['text']])
        print(f"  {period}: {count} 个文件")
    
    return target_links

if __name__ == "__main__":
    filter_target_data()
