import pandas as pd
import os

def debug_jiangsu_data():
    file_path = r"C:\Users\<USER>\Desktop\海关数据\小超海关统计\全国进出口\20250530\2025年1-4月 - 江苏\2025年4月江苏省主要进出口数据.xls"
    
    # 读取数据
    df = pd.read_excel(file_path, sheet_name='贸易方式')
    print(f"数据形状: {df.shape}")
    
    # 显示列标题信息
    print("\n列标题信息:")
    for j in range(min(20, df.shape[1])):
        print(f"  列{j}: {df.columns[j]}")
    
    # 查找总值行
    total_row_index = None
    for i in range(len(df)):
        if '总值' in str(df.iloc[i, 0]):
            total_row_index = i
            print(f"\n找到总值行 {i}:")
            print(f"  完整数据: {[str(df.iloc[i, j]) for j in range(min(20, df.shape[1]))]}")
            print(f"  单月进出口额（列1）: {df.iloc[i, 1]}")
            print(f"  累计进出口额（列7）: {df.iloc[i, 7]}")
            print(f"  累计同比（列8）: {df.iloc[i, 8]}")
            break
    
    # 查找一般贸易行
    for i in range(len(df)):
        if '一般贸易' in str(df.iloc[i, 0]):
            print(f"\n找到一般贸易行 {i}:")
            print(f"  完整数据: {[str(df.iloc[i, j]) for j in range(min(10, df.shape[1]))]}") 
            print(f"  单月进出口额（列1）: {df.iloc[i, 1]}")
            print(f"  累计进出口额（列7）: {df.iloc[i, 7]}")
            break
    
    # 查找加工贸易行
    for i in range(len(df)):
        if '加工贸易' in str(df.iloc[i, 0]) and '来料' not in str(df.iloc[i, 0]) and '进料' not in str(df.iloc[i, 0]):
            print(f"\n找到加工贸易行 {i}:")
            print(f"  完整数据: {[str(df.iloc[i, j]) for j in range(min(10, df.shape[1]))]}") 
            print(f"  单月进出口额（列1）: {df.iloc[i, 1]}")
            print(f"  累计进出口额（列7）: {df.iloc[i, 7]}")
            break
    
    # 查找保税贸易行
    for i in range(len(df)):
        if '保税贸易' in str(df.iloc[i, 0]):
            print(f"\n找到保税贸易行 {i}:")
            print(f"  完整数据: {[str(df.iloc[i, j]) for j in range(min(10, df.shape[1]))]}") 
            print(f"  单月进出口额（列1）: {df.iloc[i, 1]}")
            print(f"  累计进出口额（列7）: {df.iloc[i, 7]}")
            break
    
    # 测试数据提取
    if total_row_index is not None:
        monthly_total = float(str(df.iloc[total_row_index, 1]).replace(',', ''))
        cumulative_total = float(str(df.iloc[total_row_index, 7]).replace(',', ''))
        print(f"\n提取的数据:")
        print(f"  单月总额: {monthly_total} 亿元")
        print(f"  累计总额: {cumulative_total} 亿元")

if __name__ == "__main__":
    debug_jiangsu_data()