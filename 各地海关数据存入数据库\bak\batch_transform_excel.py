import pandas as pd
import numpy as np
import datetime
import os
import re
from pathlib import Path

def clean_excel_data(df):
    """
    清洗Excel数据，处理特殊字符、空值、数据类型等问题
    """
    # 删除全为空值的列
    df = df.dropna(axis=1, how='all')
    
    # 清理列名：去除特殊字符和空格
    df.columns = df.columns.astype(str).str.strip().str.replace('\xa0', ' ').str.replace(r'Unnamed: \d+', '', regex=True)
    
    # 查找项目列
    project_col = None
    for col in df.columns:
        if '项目' in col or col.strip() == '':
            project_col = col
            break
    
    if project_col is None:
        # 如果没有找到项目列，使用第一列
        project_col = df.columns[0]
    
    # 过滤数据
    clean_df = df[df[project_col].notna()]
    clean_df = clean_df[~clean_df[project_col].astype(str).str.contains('单位|合计|总计|nan', case=False, na=False)]
    clean_df = clean_df[clean_df[project_col].astype(str).str.strip() != '']
    
    # 清理数据中的特殊字符
    for col in clean_df.columns:
        if clean_df[col].dtype == 'object':
            clean_df[col] = clean_df[col].astype(str).str.replace('*', '').str.strip()
    
    return clean_df, project_col

def extract_date_from_filename(filename):
    """
    从文件名中提取日期信息
    """
    # 匹配年份和月份
    patterns = [
        r'(\d{4})年(\d{1,2})月',
        r'(\d{4})年1-(\d{1,2})月',
        r'(\d{4})年1-(\d{1,2})月',
    ]
    
    for pattern in patterns:
        match = re.search(pattern, filename)
        if match:
            year = match.group(1)
            month = match.group(2)
            
            # 如果是1-X月的格式，使用X作为月份
            if '1-' in filename:
                return f"{year}/{month.zfill(2)}/1"
            else:
                return f"{year}/{month.zfill(2)}/1"
    
    # 默认返回当前日期
    return datetime.datetime.now().strftime('%Y/%m/1')

def transform_single_excel(input_excel_path, output_csv_path):
    """
    转换单个Excel文件为CSV
    """
    try:
        xls = pd.ExcelFile(input_excel_path, engine='openpyxl')
    except FileNotFoundError:
        print(f"错误：找不到文件 {input_excel_path}")
        return None
    except Exception as e:
        print(f"读取Excel文件时出错 {input_excel_path}: {e}")
        return None

    output_dfs = []
    
    # 目标CSV文件的列名（完全匹配Oracle表结构）
    output_columns = [
        "STAT_DATE", "STAT_TYPE", "STAT_NAME", "STAT_CODE", "STAT_CONTENT_RAW",
        "STAT_CONTENT_CLEANSE", "ACC_A_CNY_AMOUNT", "ACC_A_CNY_YOY", "ACC_A_USD_AMOUNT",
        "ACC_A_USD_YOY", "MON_A_CNY_AMOUNT", "MON_A_CNY_YOY", "MON_A_USD_AMOUNT",
        "MON_A_USD_YOY", "ACC_E_CNY_AMOUNT", "ACC_E_CNY_YOY", "ACC_E_USD_AMOUNT",
        "ACC_E_USD_YOY", "MON_E_CNY_AMOUNT", "MON_E_CNY_YOY", "MON_E_USD_AMOUNT",
        "MON_E_USD_YOY", "ACC_I_CNY_AMOUNT", "ACC_I_CNY_YOY", "ACC_I_USD_AMOUNT",
        "ACC_I_USD_YOY", "MON_I_CNY_AMOUNT", "MON_I_CNY_YOY", "MON_I_USD_AMOUNT",
        "MON_I_USD_YOY", "ACC_E_AMOUNT", "ACC_E_AMOUNT_UNIT", "ACC_E_AMOUNT_YOY",
        "MON_E_AMOUNT", "MON_E_AMOUNT_UNIT", "MON_E_AMOUNT_YOY", "ACC_I_AMOUNT",
        "ACC_I_AMOUNT_UNIT", "ACC_I_AMOUNT_YOY", "MON_I_AMOUNT", "MON_I_AMOUNT_UNIT",
        "MON_I_AMOUNT_YOY", "RANK_MARKERS", "DATA_SOURCE", "EMPHASIS_OR_EMERGING_MARK",
        "CREATE_TIME"
    ]

    # 工作表名称到 STAT_TYPE 的映射
    sheet_to_stat_type = {
        '贸易方式': ('01', '贸易方式'),
        '企业性质': ('02', '企业性质'),
        '主要国别（地区）': ('03', '国别地区'),
        '主出商品': ('04', '主出商品'),
        '主进商品': ('05', '主进商品'),
        '十一地市': ('03', '国别地区'),
    }

    # 从文件名提取日期
    filename = os.path.basename(input_excel_path)
    stat_date = extract_date_from_filename(filename)
    
    print(f"处理文件: {filename}")
    print(f"提取的日期: {stat_date}")

    for sheet_name, (stat_type, stat_name) in sheet_to_stat_type.items():
        try:
            df = xls.parse(sheet_name)
            print(f"  正在处理工作表: {sheet_name}")
            
            # 数据清洗
            clean_df, project_col = clean_excel_data(df)
            
            print(f"  清洗后数据行数: {len(clean_df)} (原始: {len(df)})")
            
            if len(clean_df) == 0:
                print(f"  警告：工作表 {sheet_name} 没有有效数据")
                continue
            
            # 创建一个临时的DataFrame来存储当前工作表的数据
            temp_df = pd.DataFrame(columns=output_columns)
            
            # 基础字段
            temp_df['STAT_CONTENT_RAW'] = clean_df[project_col]
            # 数据清洗：去除多余空格
            temp_df['STAT_CONTENT_CLEANSE'] = clean_df[project_col].astype(str).str.strip()
            temp_df['STAT_TYPE'] = stat_type
            temp_df['STAT_NAME'] = stat_name
            temp_df['STAT_CODE'] = ''
            
            # 处理不同工作表的数据结构
            if sheet_name in ['贸易方式', '企业性质', '主要国别（地区）', '十一地市']:
                # 这些表有标准的进出口结构
                # 注意：Excel中的单位是万元，需要转换为亿元（除以10000）
                if '当期进出口' in clean_df.columns:
                    temp_df['MON_A_CNY_AMOUNT'] = pd.to_numeric(clean_df['当期进出口'], errors='coerce') / 10000
                if '当期进口' in clean_df.columns:
                    temp_df['MON_I_CNY_AMOUNT'] = pd.to_numeric(clean_df['当期进口'], errors='coerce') / 10000
                if '当期出口' in clean_df.columns:
                    temp_df['MON_E_CNY_AMOUNT'] = pd.to_numeric(clean_df['当期出口'], errors='coerce') / 10000
                
                # 同比数据
                if '进出口同比' in clean_df.columns:
                    temp_df['MON_A_CNY_YOY'] = pd.to_numeric(clean_df['进出口同比'], errors='coerce')
                if '进口同比' in clean_df.columns:
                    temp_df['MON_I_CNY_YOY'] = pd.to_numeric(clean_df['进口同比'], errors='coerce')
                if '出口同比' in clean_df.columns:
                    temp_df['MON_E_CNY_YOY'] = pd.to_numeric(clean_df['出口同比'], errors='coerce')
                    
            elif sheet_name in ['主出商品', '主进商品']:
                # 商品表有不同结构
                # 注意：Excel中的单位是万元，需要转换为亿元（除以10000）
                if '当期' in clean_df.columns:
                    if sheet_name == '主出商品':
                        temp_df['MON_E_CNY_AMOUNT'] = pd.to_numeric(clean_df['当期'], errors='coerce') / 10000
                        temp_df['MON_E_CNY_YOY'] = pd.to_numeric(clean_df['同比'], errors='coerce')
                    else:  # 主进商品
                        temp_df['MON_I_CNY_AMOUNT'] = pd.to_numeric(clean_df['当期'], errors='coerce') / 10000
                        temp_df['MON_I_CNY_YOY'] = pd.to_numeric(clean_df['同比'], errors='coerce')
            
            # 设置累计数据为空（浙江数据只有当月数据）
            acc_columns = [
                'ACC_A_CNY_AMOUNT', 'ACC_A_CNY_YOY', 'ACC_A_USD_AMOUNT', 'ACC_A_USD_YOY',
                'ACC_E_CNY_AMOUNT', 'ACC_E_CNY_YOY', 'ACC_E_USD_AMOUNT', 'ACC_E_USD_YOY',
                'ACC_I_CNY_AMOUNT', 'ACC_I_CNY_YOY', 'ACC_I_USD_AMOUNT', 'ACC_I_USD_YOY',
                'ACC_E_AMOUNT', 'ACC_E_AMOUNT_UNIT', 'ACC_E_AMOUNT_YOY',
                'ACC_I_AMOUNT', 'ACC_I_AMOUNT_UNIT', 'ACC_I_AMOUNT_YOY'
            ]
            for col in acc_columns:
                temp_df[col] = np.nan
            
            # 设置美元金额为空（浙江数据只有人民币金额）
            usd_columns = [
                'ACC_A_USD_AMOUNT', 'ACC_A_USD_YOY', 'MON_A_USD_AMOUNT', 'MON_A_USD_YOY',
                'ACC_E_USD_AMOUNT', 'ACC_E_USD_YOY', 'MON_E_USD_AMOUNT', 'MON_E_USD_YOY',
                'ACC_I_USD_AMOUNT', 'ACC_I_USD_YOY', 'MON_I_USD_AMOUNT', 'MON_I_USD_YOY'
            ]
            for col in usd_columns:
                temp_df[col] = np.nan
                
            # 设置数量单位为空
            amount_unit_columns = [
                'MON_E_AMOUNT_UNIT', 'MON_I_AMOUNT_UNIT', 'ACC_E_AMOUNT_UNIT', 'ACC_I_AMOUNT_UNIT'
            ]
            for col in amount_unit_columns:
                temp_df[col] = ''
            
            # 设置当月数量为空
            temp_df['MON_E_AMOUNT'] = np.nan
            temp_df['MON_I_AMOUNT'] = np.nan
            temp_df['MON_E_AMOUNT_YOY'] = np.nan
            temp_df['MON_I_AMOUNT_YOY'] = np.nan
            
            output_dfs.append(temp_df)
            
        except Exception as e:
            print(f"  处理工作表 {sheet_name} 时出错: {e}")
            continue

    if not output_dfs:
        print("没有成功处理任何数据")
        return None

    # 合并所有处理过的数据
    final_df = pd.concat(output_dfs, ignore_index=True)
    
    # 填充公共字段
    final_df['STAT_DATE'] = stat_date
    final_df['DATA_SOURCE'] = '02'  # 浙江
    final_df['EMPHASIS_OR_EMERGING_MARK'] = ''
    final_df['RANK_MARKERS'] = ''
    final_df['CREATE_TIME'] = datetime.datetime.now().strftime('%Y/%m/%d %H:%M:%S')
    
    # 确保所有列都存在
    for col in output_columns:
        if col not in final_df.columns:
            final_df[col] = np.nan if 'AMOUNT' in col or 'YOY' in col else ''
    
    # 按照目标顺序排列列
    final_df = final_df[output_columns]
    
    # 数据清理：将空字符串替换为NaN，然后填充适当的默认值
    for col in final_df.columns:
        if 'AMOUNT' in col or 'YOY' in col:
            final_df[col] = pd.to_numeric(final_df[col], errors='coerce')
        elif 'UNIT' in col or 'CODE' in col or 'MARKERS' in col or 'MARK' in col:
            final_df[col] = final_df[col].fillna('').astype(str)
    
    # 将处理好的数据保存为CSV文件
    final_df.to_csv(output_csv_path, index=False, encoding='utf-8-sig', float_format='%.4f')
    print(f"数据已成功转换并保存到 {output_csv_path}")
    print(f"总记录数: {len(final_df)}")
    print(f"字段数: {len(final_df.columns)}")
    
    return final_df

def batch_transform_excel_files(input_dir, output_dir):
    """
    批量转换Excel文件
    """
    # 创建输出目录
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    # 获取所有Excel文件
    excel_files = []
    for file in os.listdir(input_dir):
        if file.endswith('.xlsx') and not file.startswith('~'):
            excel_files.append(file)
    
    if not excel_files:
        print(f"在目录 {input_dir} 中没有找到Excel文件")
        return
    
    print(f"找到 {len(excel_files)} 个Excel文件")
    
    # 统计信息
    total_files = 0
    successful_files = 0
    total_records = 0
    
    for excel_file in sorted(excel_files):
        total_files += 1
        
        input_path = os.path.join(input_dir, excel_file)
        output_filename = f"T_STATISTICAL_CUS_TOTAL_{os.path.splitext(excel_file)[0]}.csv"
        output_path = os.path.join(output_dir, output_filename)
        
        print(f"\n{'='*60}")
        print(f"处理文件 {total_files}/{len(excel_files)}: {excel_file}")
        print(f"{'='*60}")
        
        try:
            result_df = transform_single_excel(input_path, output_path)
            if result_df is not None:
                successful_files += 1
                total_records += len(result_df)
                print(f"成功处理: {excel_file} ({len(result_df)} 条记录)")
            else:
                print(f"处理失败: {excel_file}")
        except Exception as e:
            print(f"处理出错 {excel_file}: {e}")
    
    print(f"\n{'='*60}")
    print("批量处理完成！")
    print(f"{'='*60}")
    print(f"总文件数: {total_files}")
    print(f"成功处理: {successful_files}")
    print(f"失败文件: {total_files - successful_files}")
    print(f"总记录数: {total_records}")
    print(f"输出目录: {output_dir}")

if __name__ == '__main__':
    # 配置路径
    input_directory = "录入的"
    output_directory = "批量转换结果"
    
    print("=== 浙江海关数据批量转换 ===")
    print(f"输入目录: {input_directory}")
    print(f"输出目录: {output_directory}")
    print(f"开始时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    batch_transform_excel_files(input_directory, output_directory)
    
    print(f"\n结束时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")