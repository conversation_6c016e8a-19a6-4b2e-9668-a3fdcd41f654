from DrissionPage import ChromiumPage
import time
import os
import json

def save_all_pages_data():
    """保存每页的所有链接数据到文件"""
    
    print("=== 保存所有页面数据（第1-24页）===")
    
    page = ChromiumPage(timeout=30)
    
    try:
        all_pages_data = {}
        
        # 先访问第一页
        start_url = "http://beijing.customs.gov.cn/beijing_customs/434756/434804/2941702/434773/434774/index.html"
        page.get(start_url)
        time.sleep(5)  # 增加等待时间
        
        current_page_num = 1
        
        # 处理第1页
        print(f"\n=== 保存第 {current_page_num} 页数据 ===")
        page_data = extract_page_data(page, current_page_num)
        all_pages_data[current_page_num] = page_data
        print(f"第 {current_page_num} 页找到 {len(page_data)} 个链接")
        
        # 从第2页开始翻页，强制翻到第24页
        while current_page_num < 24:
            current_page_num += 1
            
            print(f"\n=== 尝试翻到第 {current_page_num} 页 ===")
            
            try:
                # 查找"下一页"按钮
                next_page_buttons = page.eles("下一页")
                next_button_found = False
                
                for next_btn in next_page_buttons:
                    if next_btn.tag == 'a' and next_btn.attr('onclick'):
                        print(f"找到下一页按钮，点击翻页...")
                        try:
                            next_btn.click()
                            time.sleep(5)  # 增加等待时间
                            next_button_found = True
                            break
                        except Exception as e:
                            print(f"点击下一页按钮失败: {e}")
                            continue
                
                if not next_button_found:
                    print(f"未找到可点击的下一页按钮，尝试直接访问URL")
                    url = f"http://beijing.customs.gov.cn/beijing_customs/434756/434804/2941702/434773/434774/16673809-{current_page_num}.html"
                    page.get(url)
                    time.sleep(5)
                
                print(f"页面标题: {page.title}")
                
                # 检查页面是否有效
                page_content = page.html
                if "404" in page.title or "未找到" in page_content:
                    print(f"第 {current_page_num} 页返回404，停止翻页")
                    break
                
                # 提取当前页面数据
                page_data = extract_page_data(page, current_page_num)
                all_pages_data[current_page_num] = page_data
                print(f"第 {current_page_num} 页找到 {len(page_data)} 个链接")
                
                # 保存当前页面的HTML源码（用于调试）
                with open(f'page_{current_page_num}_source.html', 'w', encoding='utf-8') as f:
                    f.write(page.html)
                print(f"已保存第 {current_page_num} 页源码")
                
            except Exception as e:
                print(f"访问第 {current_page_num} 页时出错: {e}")
                # 继续尝试下一页
                continue
            
            time.sleep(3)  # 防止请求过快
        
        # 保存所有页面数据到JSON文件
        with open('all_pages_data.json', 'w', encoding='utf-8') as f:
            json.dump(all_pages_data, f, ensure_ascii=False, indent=2)
        
        # 保存所有页面数据到文本文件（便于查看）
        with open('all_pages_data.txt', 'w', encoding='utf-8') as f:
            f.write("页码\t链接文本\t链接URL\t发布日期\n")
            for page_num, page_data in all_pages_data.items():
                for link_data in page_data:
                    f.write(f"{page_num}\t{link_data['text']}\t{link_data['url']}\t{link_data.get('date', '')}\n")
        
        print(f"\n=== 数据保存完成 ===")
        total_links = sum(len(page_data) for page_data in all_pages_data.values())
        print(f"总共保存了 {len(all_pages_data)} 页数据")
        print(f"总共找到 {total_links} 个链接")
        print("数据已保存到:")
        print("  - all_pages_data.json (JSON格式)")
        print("  - all_pages_data.txt (文本格式)")
        print("  - page_X_source.html (各页源码)")
        
        return all_pages_data
        
    except Exception as e:
        print(f"保存过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return {}
    
    finally:
        page.quit()

def extract_page_data(page, page_num):
    """提取页面中的所有链接数据"""
    page_data = []
    
    try:
        all_links = page.eles('tag:a')
        
        for link in all_links:
            link_text = link.text.strip()
            href = link.attr('href')
            
            if not href or not link_text:
                continue
            
            # 只保存包含年份的链接
            if any(year in link_text for year in ['2024', '2025', '2023', '2022', '2021']):
                # 构建完整URL
                if href.startswith('/'):
                    full_url = "http://beijing.customs.gov.cn" + href
                else:
                    full_url = href
                
                # 尝试提取发布日期
                date = ""
                try:
                    # 查找链接旁边的日期信息
                    parent = link.parent
                    if parent:
                        parent_text = parent.text
                        import re
                        date_match = re.search(r'(\d{4}-\d{2}-\d{2})', parent_text)
                        if date_match:
                            date = date_match.group(1)
                except:
                    pass
                
                link_data = {
                    'text': link_text,
                    'url': full_url,
                    'date': date,
                    'page': page_num
                }
                
                page_data.append(link_data)
    
    except Exception as e:
        print(f"提取第 {page_num} 页数据时出错: {e}")
    
    return page_data

if __name__ == "__main__":
    save_all_pages_data()
