import pandas as pd
import cx_Oracle
import os
import re
import sys
from tqdm import tqdm
from pathlib import Path
import numpy as np # 引入numpy

def get_db_connection():
    """建立并返回数据库连接"""
    try:
        # 根据用户提供的正确连接信息进行更新
        connection = cx_Oracle.connect("manifest_dcb/manifest_dcb@192.168.1.151/TEST")
        return connection
    except Exception as e:
        print(f"数据库连接失败: {e}")
        sys.exit(1)

def get_date_and_unit(file_path):
    """从文件名提取年月和币制，并从文件内容提取单位"""
    filename = os.path.basename(file_path)
    # 增强的正则表达式，匹配 "2021年10月" 和 "2021年1-10月"
    date_match = re.search(r'(\d{4})年(?:1-)?(\d{1,2})月', filename)
    if not date_match:
        print(f"警告: 无法从文件名 '{filename}' 中提取日期。")
        return None, None, None
    year, month = date_match.groups()
    current_month = f"{year}-{int(month):02d}-01"
    currency_type = "人民币" if "人民币" in filename else "美元"
    
    unit = "不明"
    try:
        # 移除固定的engine，让pandas自动选择
        df_head = pd.read_excel(file_path, header=None, nrows=5, dtype=str, engine=None)
        for _, row in df_head.iterrows():
            for cell in row:
                if isinstance(cell, str) and '单位：' in cell:
                    unit = cell.split('：')[1].strip()
                    break
            if unit != "不明":
                break
    except Exception as e:
        print(f"警告: 读取 '{filename}' 单位时出错: {e}。将使用默认值 '不明'。")

    return current_month, currency_type, unit

def get_unit_from_dataframe(df):
    """从DataFrame中动态查找单位信息"""
    for _, row in df.head(5).iterrows(): # 通常单位在前5行
        for cell in row:
            if isinstance(cell, str) and '单位：' in cell:
                return cell.split('：')[1].strip()
    return "不明"

def parse_excel_file(file_path):
    """
    解析表15的Excel文件（最终版）。
    此版本根据用户指出的“两列为一个国家”的二维结构编写，确保正确性。
    """
    filename = os.path.basename(file_path)
    date_match = re.search(r'(\d{4})年(?:1-)?(\d{1,2})月', filename)
    if not date_match:
        print(f"警告: 无法从文件名 '{filename}' 中提取日期。")
        return None
    year, month = date_match.groups()
    current_month_str = f"{year}-{int(month):02d}-01"
    currency_type = "人民币" if "人民币" in filename else "美元"

    try:
        df_raw = pd.read_excel(file_path, header=None, dtype=str, engine=None)
    except Exception as e:
        print(f"错误: 使用pandas读取文件失败: {file_path}, 错误: {e}")
        return None

    unit = get_unit_from_dataframe(df_raw)
    
    header_row_idx = 3
    data_start_row_idx = 5
    
    country_headers = df_raw.iloc[header_row_idx].values
    df_data = df_raw.iloc[data_start_row_idx:]

    processed_data = []
    for _, row in df_data.iterrows():
        hs_code_desc = row.iloc[1]
        if pd.isna(hs_code_desc) or not str(hs_code_desc).strip() or '总' in str(hs_code_desc) or '计' in str(hs_code_desc):
            continue

        for col_idx in range(2, len(country_headers), 2):
            country_name = country_headers[col_idx]
            if pd.isna(country_name) or '合计' in str(country_name):
                continue

            value_month = pd.to_numeric(row.iloc[col_idx], errors='coerce')
            value_ytd = pd.to_numeric(row.iloc[col_idx + 1], errors='coerce')

            if pd.notna(value_month) or pd.notna(value_ytd):
                processed_data.append({
                    "HS_CODE_DESC": str(hs_code_desc).strip(),
                    "COUNTRY": str(country_name).strip(),
                    "VALUE_MONTH": value_month,
                    "VALUE_YTD": value_ytd,
                })
    
    if not processed_data:
        return None
        
    df = pd.DataFrame(processed_data)
    df['CURRENT_MONTH'] = pd.to_datetime(current_month_str)
    df['CURRENCY_TYPE'] = currency_type
    df['UNIT'] = unit
    return df

def insert_data_incrementally(connection, df_to_process):
    """
    借鉴表10脚本的成功逻辑，使用“先查、再筛、最后批量插”的模式。
    """
    if df_to_process is None or df_to_process.empty:
        print("    -> 无数据传入，跳过数据库操作。")
        return 0, 0

    cursor = connection.cursor()
    
    # 确定当前批次数据的唯一标识
    current_month = df_to_process['CURRENT_MONTH'].iloc[0]
    currency_type = df_to_process['CURRENCY_TYPE'].iloc[0]

    # 1. 先查询数据库中已存在的记录 (HS_CODE_DESC, COUNTRY, CURRENT_MONTH的组合)
    query_existing = """
    SELECT HS_CODE_DESC, COUNTRY FROM temp_cus_mon_15
    WHERE "CURRENT_MONTH" = :p_month AND CURRENCY_TYPE = :p_currency
    """
    try:
        cursor.execute(query_existing, p_month=current_month, p_currency=currency_type)
        existing_records = { (row[0], row[1]) for row in cursor.fetchall() }
        print(f"    -> 数据库中已存在 {len(existing_records)} 条 {current_month.strftime('%Y-%m')} {currency_type} 的记录。")
    except Exception as e:
        print(f"    [!] 查询已存在记录时出错: {e}")
        return 0, 0
    
    # 2. 在 pandas 中筛选出新数据
    df_to_process['composite_key'] = list(zip(df_to_process['HS_CODE_DESC'], df_to_process['COUNTRY']))
    df_new = df_to_process[~df_to_process['composite_key'].isin(existing_records)].copy()
    df_new.drop(columns=['composite_key'], inplace=True)
    
    skipped_count = len(df_to_process) - len(df_new)

    if df_new.empty:
        print("    -> 无新数据需要插入。")
        return 0, skipped_count

    print(f"    -> 筛选出 {len(df_new)} 条新数据准备插入。")

    # 3. 准备数据并进行批量插入
    df_new.replace({np.nan: None}, inplace=True)
    data_to_insert = df_new.to_dict('records')

    insert_query = """
    INSERT INTO temp_cus_mon_15 (
        HS_CODE_DESC, COUNTRY, VALUE_MONTH, VALUE_YTD, 
        "CURRENT_MONTH", CURRENCY_TYPE, UNIT
    ) VALUES (
        :HS_CODE_DESC, :COUNTRY, :VALUE_MONTH, :VALUE_YTD, 
        :CURRENT_MONTH, :CURRENCY_TYPE, :UNIT
    )
    """
    try:
        cursor.executemany(insert_query, data_to_insert, batcherrors=True)
        # batcherrors=True 允许部分成功，但我们需要检查错误
        error_count = len(cursor.getbatcherrors())
        inserted_count = cursor.rowcount - error_count
        
        if error_count > 0:
            print(f"    [!] 批量插入时发生 {error_count} 个错误。")
            # for error in cursor.getbatcherrors():
            #     print(f"        - 错误: {error.message} 在行: {error.offset}")

        connection.commit()
        return inserted_count, skipped_count + error_count
    except Exception as e:
        print(f"    [!] 执行批量插入时发生严重错误: {e}")
        connection.rollback()
        return 0, len(df_to_process)

def process_directory(directory_path, connection):
    """处理指定目录下的所有Excel文件，并返回新增和跳过的记录数"""
    base_dir = Path(directory_path)
    if not base_dir.exists():
        print(f"错误: 数据目录不存在 -> {base_dir}，跳过处理。")
        return 0, 0

    files_to_process = sorted(list(base_dir.rglob('*.xlsx')))
    if not files_to_process:
        print(f"在目录 {base_dir.name} 中未找到任何 .xlsx 文件。")
        return 0, 0
    
    print(f"\n--- 开始处理目录: {base_dir.name} ---")
    print(f"发现 {len(files_to_process)} 个文件待处理。")
    
    total_new_in_dir = 0
    total_skipped_in_dir = 0

    for file_path in files_to_process:
        if file_path.name.startswith('~'):
            continue
        print(f"\n  -- 处理文件: {file_path.name} --")
        df = parse_excel_file(file_path)
        if df is not None and not df.empty:
            new, skipped = insert_data_incrementally(connection, df)
            print(f"    -> 处理完成: 新增 {new} 条, 跳过 {skipped} 条。")
            total_new_in_dir += new
            total_skipped_in_dir += skipped
        else:
            print("    -> 解析完成，无数据入库。")
            
    return total_new_in_dir, total_skipped_in_dir


def main():
    """主执行函数，自动处理人民币和美元两个目录"""
    # --- 配置区 ---
    # 定义人民币和美元数据目录的根路径
    # rmb_dir = r'C:\Users\<USER>\Desktop\海关数据\小超海关统计\全国进出口\进出口商品统计表_人民币值\(15)对部分国家(地区)出口商品类章金额表_人民币值'
    # usd_dir = r'C:\Users\<USER>\Desktop\海关数据\小超海关统计\全国进出口\进出口商品统计表_美元值\(15)对部分国家(地区)出口商品类章金额表_美元值'
    # --- 配置区结束 ---

    connection = None
    grand_total_new = 0
    grand_total_skipped = 0
    
    try:
        connection = get_db_connection()
        print("数据库连接成功。")
        
        # 依次处理人民币和美元目录
        # 使用os.getcwd()获取当前工作目录
        base_dir = os.getcwd()
        rmb_dir = os.path.join(base_dir, '进出口商品统计表_人民币值', '(15)对部分国家(地区)出口商品类章金额表_人民币值')
        usd_dir = os.path.join(base_dir, '进出口商品统计表_美元值', '(15)对部分国家(地区)出口商品类章金额表_美元值')

        rmb_new, rmb_skipped = process_directory(rmb_dir, connection)
        grand_total_new += rmb_new
        grand_total_skipped += rmb_skipped
        
        usd_new, usd_skipped = process_directory(usd_dir, connection)
        grand_total_new += usd_new
        grand_total_skipped += usd_skipped

    except Exception as e:
        print(f"\n[!!!] 批量处理期间发生严重错误: {e}")
        if connection:
            connection.rollback()
    finally:
        if connection:
            connection.close()
            print("\n数据库连接已关闭。")

    print("\n======================================")
    print(f"全部任务完成！")
    print(f"总计新增: {grand_total_new} 条记录")
    print(f"总计跳过: {grand_total_skipped} 条记录")
    print("======================================")


if __name__ == "__main__":
    sys.stdout.reconfigure(encoding='utf-8')
    main() 