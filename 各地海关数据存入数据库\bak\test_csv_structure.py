import pandas as pd
import os
from datetime import datetime

def validate_csv_structure(csv_file_path):
    """
    验证CSV文件结构
    """
    try:
        df = pd.read_csv(csv_file_path, encoding='utf-8-sig')
        
        expected_columns = [
            'STAT_DATE', 'STAT_TYPE', 'STAT_NAME', 'STAT_CODE', 'STAT_CONTENT_RAW', 'STAT_CONTENT_CLEANSE',
            'ACC_A_CNY_AMOUNT', 'ACC_A_CNY_YOY', 'ACC_A_USD_AMOUNT', 'ACC_A_USD_YOY',
            'MON_A_CNY_AMOUNT', 'MON_A_CNY_YOY', 'MON_A_USD_AMOUNT', 'MON_A_USD_YOY',
            'ACC_E_CNY_AMOUNT', 'ACC_E_CNY_YOY', 'ACC_E_USD_AMOUNT', 'ACC_E_USD_YOY',
            'MON_E_CNY_AMOUNT', 'MON_E_CNY_YOY', 'MON_E_USD_AMOUNT', 'MON_E_USD_YOY',
            'ACC_I_CNY_AMOUNT', 'ACC_I_CNY_YOY', 'ACC_I_USD_AMOUNT', 'ACC_I_USD_YOY',
            'MON_I_CNY_AMOUNT', 'MON_I_CNY_YOY', 'MON_I_USD_AMOUNT', 'MON_I_USD_YOY',
            'ACC_E_AMOUNT', 'ACC_E_AMOUNT_UNIT', 'ACC_E_AMOUNT_YOY',
            'MON_E_AMOUNT', 'MON_E_AMOUNT_UNIT', 'MON_E_AMOUNT_YOY',
            'ACC_I_AMOUNT', 'ACC_I_AMOUNT_UNIT', 'ACC_I_AMOUNT_YOY',
            'MON_I_AMOUNT', 'MON_I_AMOUNT_UNIT', 'MON_I_AMOUNT_YOY',
            'RANK_MARKERS', 'DATA_SOURCE', 'EMPHASIS_OR_EMERGING_MARK', 'CREATE_TIME'
        ]
        
        actual_columns = list(df.columns)
        
        print(f"\n文件: {os.path.basename(csv_file_path)}")
        print(f"期望字段数: {len(expected_columns)}")
        print(f"实际字段数: {len(actual_columns)}")
        print(f"记录数: {len(df)}")
        
        # 检查缺失字段
        missing_columns = set(expected_columns) - set(actual_columns)
        if missing_columns:
            print(f"缺失字段: {missing_columns}")
        
        # 检查多余字段
        extra_columns = set(actual_columns) - set(expected_columns)
        if extra_columns:
            print(f"多余字段: {extra_columns}")
        
        # 检查字段顺序
        if actual_columns == expected_columns:
            print("✅ 字段结构完全匹配")
            return True
        else:
            print("❌ 字段结构不匹配")
            print("字段对比:")
            for i in range(max(len(actual_columns), len(expected_columns))):
                actual = actual_columns[i] if i < len(actual_columns) else "N/A"
                expected = expected_columns[i] if i < len(expected_columns) else "N/A"
                match = "✅" if actual == expected else "❌"
                print(f"  {i+1:2d}. {actual:25s} {match} (期望: {expected})")
            return False
            
    except Exception as e:
        print(f"验证出错: {e}")
        return False

def test_data_sample(csv_file_path, num_rows=3):
    """
    测试数据样本
    """
    try:
        df = pd.read_csv(csv_file_path, encoding='utf-8-sig')
        
        if len(df) == 0:
            print("CSV文件为空")
            return
        
        print(f"\n前 {min(num_rows, len(df))} 条记录样本:")
        for i in range(min(num_rows, len(df))):
            print(f"\n--- 第 {i+1} 条记录 ---")
            row = df.iloc[i]
            for col in df.columns:
                value = row[col]
                if pd.isna(value) or value == '':
                    value_str = "NULL"
                else:
                    value_str = str(value)
                print(f"  {col:25s}: {value_str}")
        
    except Exception as e:
        print(f"测试数据样本出错: {e}")

def analyze_data_types(csv_file_path):
    """
    分析数据类型
    """
    try:
        df = pd.read_csv(csv_file_path, encoding='utf-8-sig')
        
        print(f"\n数据类型分析:")
        
        # 字符串字段
        string_fields = ['STAT_TYPE', 'STAT_NAME', 'STAT_CODE', 'STAT_CONTENT_RAW', 'STAT_CONTENT_CLEANSE',
                        'ACC_E_AMOUNT_UNIT', 'MON_E_AMOUNT_UNIT', 'ACC_I_AMOUNT_UNIT', 'MON_I_AMOUNT_UNIT',
                        'RANK_MARKERS', 'DATA_SOURCE', 'EMPHASIS_OR_EMERGING_MARK']
        
        # 数值字段
        numeric_fields = ['ACC_A_CNY_AMOUNT', 'ACC_A_CNY_YOY', 'ACC_A_USD_AMOUNT', 'ACC_A_USD_YOY',
                         'MON_A_CNY_AMOUNT', 'MON_A_CNY_YOY', 'MON_A_USD_AMOUNT', 'MON_A_USD_YOY',
                         'ACC_E_CNY_AMOUNT', 'ACC_E_CNY_YOY', 'ACC_E_USD_AMOUNT', 'ACC_E_USD_YOY',
                         'MON_E_CNY_AMOUNT', 'MON_E_CNY_YOY', 'MON_E_USD_AMOUNT', 'MON_E_USD_YOY',
                         'ACC_I_CNY_AMOUNT', 'ACC_I_CNY_YOY', 'ACC_I_USD_AMOUNT', 'ACC_I_USD_YOY',
                         'MON_I_CNY_AMOUNT', 'MON_I_CNY_YOY', 'MON_I_USD_AMOUNT', 'MON_I_USD_YOY',
                         'ACC_E_AMOUNT', 'ACC_E_AMOUNT_YOY', 'MON_E_AMOUNT', 'MON_E_AMOUNT_YOY',
                         'ACC_I_AMOUNT', 'ACC_I_AMOUNT_YOY', 'MON_I_AMOUNT', 'MON_I_AMOUNT_YOY']
        
        # 日期字段
        date_fields = ['STAT_DATE', 'CREATE_TIME']
        
        print("字符串字段空值统计:")
        for field in string_fields:
            if field in df.columns:
                null_count = df[field].isna().sum() + (df[field] == '').sum()
                print(f"  {field:25s}: {null_count}/{len(df)} 个空值")
        
        print("\n数值字段空值统计:")
        for field in numeric_fields:
            if field in df.columns:
                null_count = df[field].isna().sum() + (df[field] == '').sum()
                non_null_values = df[field].dropna()
                non_null_values = non_null_values[non_null_values != '']
                if len(non_null_values) > 0:
                    try:
                        numeric_values = pd.to_numeric(non_null_values, errors='coerce')
                        valid_count = numeric_values.notna().sum()
                        print(f"  {field:25s}: {null_count}/{len(df)} 个空值, {valid_count} 个有效数值")
                    except:
                        print(f"  {field:25s}: {null_count}/{len(df)} 个空值, 数值转换失败")
                else:
                    print(f"  {field:25s}: {null_count}/{len(df)} 个空值, 无有效数据")
        
        print("\n日期字段格式检查:")
        for field in date_fields:
            if field in df.columns:
                null_count = df[field].isna().sum() + (df[field] == '').sum()
                non_null_values = df[field].dropna()
                non_null_values = non_null_values[non_null_values != '']
                if len(non_null_values) > 0:
                    sample_value = str(non_null_values.iloc[0])
                    print(f"  {field:25s}: {null_count}/{len(df)} 个空值, 样本格式: {sample_value}")
                else:
                    print(f"  {field:25s}: {null_count}/{len(df)} 个空值, 无有效数据")
        
    except Exception as e:
        print(f"数据类型分析出错: {e}")

def test_all_csv_files(csv_directory):
    """
    测试目录下所有CSV文件
    """
    try:
        print(f"=== 测试目录: {csv_directory} ===")
        
        # 获取所有CSV文件
        csv_files = []
        for file in os.listdir(csv_directory):
            if file.endswith('.csv'):
                csv_files.append(os.path.join(csv_directory, file))
        
        print(f"找到 {len(csv_files)} 个CSV文件")
        
        valid_count = 0
        invalid_files = []
        
        for i, csv_file in enumerate(csv_files[:5]):  # 只测试前5个文件
            print(f"\n{'='*60}")
            print(f"测试第 {i+1}/{min(5, len(csv_files))} 个文件")
            
            # 验证文件结构
            is_valid = validate_csv_structure(csv_file)
            
            if is_valid:
                valid_count += 1
                # 测试数据样本
                test_data_sample(csv_file, 2)
                # 分析数据类型
                analyze_data_types(csv_file)
            else:
                invalid_files.append(csv_file)
        
        print(f"\n{'='*60}")
        print(f"=== 测试总结 ===")
        print(f"有效文件: {valid_count}/{min(5, len(csv_files))}")
        
        if invalid_files:
            print(f"无效文件:")
            for file in invalid_files:
                print(f"  - {os.path.basename(file)}")
        
        return valid_count, invalid_files
        
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")
        return 0, []

if __name__ == "__main__":
    csv_directory = '北京海关数据_完整转换结果'
    
    print("=== 北京海关数据CSV文件结构测试 ===")
    
    if os.path.exists(csv_directory):
        test_all_csv_files(csv_directory)
    else:
        print(f"目录不存在: {csv_directory}")
        
        # 测试单个文件
        test_files = [
            'T_STATISTICAL_CUS_TOTAL_BEIJING_2024_(1)_（1）北京地区进出口商品总值表B：月度表（2024年1-2月）.csv',
            'T_STATISTICAL_CUS_TOTAL_BEIJING_2024_(8)_（8）北京地区出口主要商品量值表（2024年1-2月）.csv'
        ]
        
        for test_file in test_files:
            if os.path.exists(test_file):
                print(f"\n=== 测试文件: {test_file} ===")
                validate_csv_structure(test_file)
                test_data_sample(test_file, 2)
                analyze_data_types(test_file)
