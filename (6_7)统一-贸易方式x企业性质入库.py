# 文件名: (6_7)统一-贸易方式x企业性质入库.py
# 这是一个完整、可运行的、合并了(6)和(7)号表处理逻辑的最终版本。

import pandas as pd
import os
import re
import cx_Oracle
import numpy as np
from tqdm import tqdm
from pathlib import Path

def get_db_connection():
    """【已补全】建立并返回数据库连接"""
    try:
        connection = cx_Oracle.connect("manifest_dcb/manifest_dcb@*************/TEST")
        return connection
    except Exception as e:
        print(f"数据库连接失败: {e}")
        # 在实际脚本中，遇到关键错误时直接退出更安全
        exit(1)

def convert_to_float_or_none(value):
    """【已补全】清理数据，将'-'或空值转为None，其余转为浮点数"""
    if pd.isna(value) or str(value).strip() in ['-', '…']:
        return None
    try:
        return float(str(value).replace(',', ''))
    except (ValueError, TypeError):
        return None

def parse_trade_company_data(file_path):
    """
    【已补全 & 融合】一个通用的解析器，能够处理(6)和(7)号表相似的Excel结构。
    基于原有的 parse_table_6 函数。
    """
    try:
        df_raw = pd.read_excel(file_path, header=None, sheet_name=0)
        
        unit = "不明"
        for i in range(min(5, len(df_raw))):
            row_text = ' '.join(str(s) for s in df_raw.iloc[i].tolist() if pd.notna(s))
            unit_match = re.search(r'单位[：:]\s*([^\s]+)', row_text)
            if unit_match:
                unit = unit_match.group(1).strip()
                break

        header_start_row = -1
        trade_mode_col_idx = -1
        for i, row in df_raw.iterrows():
            if row.astype(str).str.contains('贸易方式').any():
                header_start_row = i
                for j, cell_content in enumerate(row):
                    if '贸易方式' in str(cell_content):
                        trade_mode_col_idx = j
                        break
                break
        
        if header_start_row == -1:
            print(f"    [!] 错误: 在 {os.path.basename(file_path)} 中未能找到'贸易方式'表头。")
            return None, None
        
        data_start_row = -1
        for i in range(header_start_row + 2, len(df_raw)):
            cell_value = str(df_raw.iloc[i, trade_mode_col_idx])
            if '总' in cell_value and ('值' in cell_value or '计' in cell_value):
                data_start_row = i
                break
        
        if data_start_row == -1: return None, None
        
        data_end_row = len(df_raw)
        remarks = ''
        for i in range(data_start_row, len(df_raw)):
            cell_value = str(df_raw.iloc[i, trade_mode_col_idx])
            if '注' in cell_value:
                data_end_row = i
                remarks = cell_value.strip()
                break

        end_col = trade_mode_col_idx + 6
        df_sliced = df_raw.iloc[data_start_row:data_end_row, trade_mode_col_idx:end_col].copy()
        df_sliced.reset_index(drop=True, inplace=True)

        processed_data = []
        for i in range(0, len(df_sliced), 2):
            row_amount = df_sliced.iloc[i]
            if i + 1 >= len(df_sliced): continue
            row_yoy = df_sliced.iloc[i+1]
            
            trade_mode = row_amount.iloc[0]
            if pd.isna(trade_mode): continue
            
            processed_data.append({
                'TRADE_MODE': trade_mode,
                'TOTAL_AMOUNT': convert_to_float_or_none(row_amount.iloc[1]),
                'TOTAL_YOY': convert_to_float_or_none(row_yoy.iloc[1]),
                'STATE_OWNED_AMOUNT': convert_to_float_or_none(row_amount.iloc[2]),
                'STATE_OWNED_YOY': convert_to_float_or_none(row_yoy.iloc[2]),
                'FOREIGN_INVESTED_AMOUNT': convert_to_float_or_none(row_amount.iloc[3]),
                'FOREIGN_INVESTED_YOY': convert_to_float_or_none(row_yoy.iloc[3]),
                'PRIVATE_AMOUNT': convert_to_float_or_none(row_amount.iloc[4]),
                'PRIVATE_YOY': convert_to_float_or_none(row_yoy.iloc[4]),
                'OTHER_AMOUNT': convert_to_float_or_none(row_amount.iloc[5]),
                'OTHER_YOY': convert_to_float_or_none(row_yoy.iloc[5]),
            })
            
        df_result = pd.DataFrame(processed_data)
        if not df_result.empty:
            df_result['REMARKS'] = remarks
        return df_result, unit
    except Exception as e:
        print(f"    [!] 解析文件 '{os.path.basename(file_path)}' 时出错: {e}")
        return None, None

def upsert_to_6_7(connection, df, trade_direction):
    """
    【修正】使用MERGE语句将数据插入或更新到统一表 TEMP_CUS_MON_6_7
    """
    if df is None or df.empty:
        return 0
    
    cursor = connection.cursor()
    
    df['TRADE_DIRECTION'] = trade_direction
    df = df.replace({np.nan: None})
    # 为了让executemany能正确匹配占位符，将列名转为大写
    df.columns = [col.upper() for col in df.columns]
    data_to_merge = df.to_dict('records')

    # 【修正】MERGE语句，目标是新表，且UPDATE部分已补全所有字段
    merge_sql = """
    MERGE INTO TEMP_CUS_MON_6_7 dest
    USING (
        SELECT 
            TO_DATE(:CURRENT_MONTH, 'YYYYMMDD') AS current_month,
            :CURRENCY_TYPE AS currency_type,
            :TRADE_MODE AS trade_mode,
            :TRADE_DIRECTION AS trade_direction
        FROM dual
    ) src ON (
        dest.current_month = src.current_month AND
        dest.currency_type = src.currency_type AND
        dest.trade_mode = src.trade_mode AND
        dest.trade_direction = src.trade_direction
    )
    WHEN MATCHED THEN
        UPDATE SET
            dest.unit = :UNIT,
            dest.remarks = :REMARKS,
            dest.total_amount = :TOTAL_AMOUNT,
            dest.total_yoy = :TOTAL_YOY,
            dest.state_owned_amount = :STATE_OWNED_AMOUNT,
            dest.state_owned_yoy = :STATE_OWNED_YOY,
            dest.foreign_invested_amount = :FOREIGN_INVESTED_AMOUNT,
            dest.foreign_invested_yoy = :FOREIGN_INVESTED_YOY,
            dest.private_amount = :PRIVATE_AMOUNT,
            dest.private_yoy = :PRIVATE_YOY,
            dest.other_amount = :OTHER_AMOUNT,
            dest.other_yoy = :OTHER_YOY
    WHEN NOT MATCHED THEN
        INSERT (
            TRADE_MODE, CURRENT_MONTH, CURRENCY_TYPE, UNIT, REMARKS,
            TOTAL_AMOUNT, TOTAL_YOY, STATE_OWNED_AMOUNT, STATE_OWNED_YOY,
            FOREIGN_INVESTED_AMOUNT, FOREIGN_INVESTED_YOY, PRIVATE_AMOUNT, PRIVATE_YOY,
            OTHER_AMOUNT, OTHER_YOY,
            TRADE_DIRECTION
        ) VALUES (
            :TRADE_MODE, TO_DATE(:CURRENT_MONTH, 'YYYYMMDD'), :CURRENCY_TYPE, :UNIT, :REMARKS,
            :TOTAL_AMOUNT, :TOTAL_YOY, :STATE_OWNED_AMOUNT, :STATE_OWNED_YOY,
            :FOREIGN_INVESTED_AMOUNT, :FOREIGN_INVESTED_YOY, :PRIVATE_AMOUNT, :PRIVATE_YOY,
            :OTHER_AMOUNT, :OTHER_YOY,
            :TRADE_DIRECTION
        )
    """
    try:
        cursor.executemany(merge_sql, data_to_merge)
        connection.commit()
        print(f"        -> 成功合并 {cursor.rowcount} 条 [{trade_direction}] 记录.")
        return cursor.rowcount
    except cx_Oracle.Error as e:
        print(f"        [!] 数据库合并错误: {e}")
        connection.rollback()
        return 0
    finally:
        cursor.close()


def process_directory(directory_path, trade_direction, connection):
    """
    【已补全】通用的目录处理函数
    """
    print(f"\n--- 开始扫描目录 ({trade_direction}): {directory_path} ---")
    if not os.path.isdir(directory_path):
        print(f"    [!] 目录不存在: {directory_path}")
        return

    files_to_process = [f for f in sorted(os.listdir(directory_path)) if f.lower().endswith(('.xls', '.xlsx')) and not f.startswith('~')]
    
    for filename in tqdm(files_to_process, desc=f"处理 {os.path.basename(directory_path)}"):
        file_path = os.path.join(directory_path, filename)
        
        # 使用通用解析器
        df, unit = parse_trade_company_data(file_path)
        
        if df is not None and not df.empty:
            # 从文件名解析年月
            year_month_match = re.search(r'(\d{4})年(?:1至)?(\d{1,2})月', filename)
            if not year_month_match:
                print(f"    [!] 无法从文件名中解析年月: {filename}, 跳过。")
                continue
            
            year, month = year_month_match.groups()
            df['current_month'] = f"{year}{month.zfill(2)}01"
            
            # 从文件名判断币种
            df['currency_type'] = "人民币" if "人民币" in filename else "美元"
            df['unit'] = unit

            # 调用新的 upsert 函数，并传入方向
            upsert_to_6_7(connection, df, trade_direction)

def main():
    """【修正】主函数，定义并处理所有目录"""
    base_dir = os.getcwd()
    # (6) 出口目录
    exp_rmb_dir_6 = os.path.join(base_dir, r"进出口商品统计表_人民币值\(6)出口商品贸易方式企业性质总值表_人民币值")
    exp_usd_dir_6 = os.path.join(base_dir, r"进出口商品统计表_美元值\(6)出口商品贸易方式企业性质总值表_美元值")
    # (7) 进口目录
    imp_rmb_dir_7 = os.path.join(base_dir, r"进出口商品统计表_人民币值\(7)进口商品贸易方式企业性质总值表_人民币值")
    imp_usd_dir_7 = os.path.join(base_dir, r"进出口商品统计表_美元值\(7)进口商品贸易方式企业性质总值表_美元值")
    
    connection = None
    try:
        connection = get_db_connection()
        print("数据库连接成功。")

        # 按顺序处理所有目录，并传入正确的贸易方向
        process_directory(exp_rmb_dir_6, '出口', connection)
        process_directory(exp_usd_dir_6, '出口', connection)
        process_directory(imp_rmb_dir_7, '进口', connection)
        process_directory(imp_usd_dir_7, '进口', connection)
        
        print("\n(6)和(7)所有数据处理完毕。")
    except Exception as e:
        print(f"\n处理过程中发生未知错误: {e}")
    finally:
        if connection:
            connection.close()
            print("数据库连接已关闭。")

if __name__ == "__main__":
    main()