{"cells": [{"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["广东省广州市进出口数据(不含美元):\n", "  Unnamed: 0_level_0_地区4_代码修正  进出口_2025年01月-2025年02月_人民币万  \\\n", "1                      广东省广州市                1.896163e+07   \n", "\n", "   进出口_2025年01月-2025年02月_人民币同比(%)  进出口_2025年02月_人民币万  进出口_2025年02月_人民币同比(%)  \\\n", "1                         14.3116       8.572786e+06                18.9271   \n", "\n", "   出口_2025年01月-2025年02月_人民币万  出口_2025年01月-2025年02月_人民币同比(%)  出口_2025年02月_人民币万  \\\n", "1               1.230083e+07                        27.7625      5.448007e+06   \n", "\n", "   出口_2025年02月_人民币同比(%)  进口_2025年01月-2025年02月_人民币万  \\\n", "1                34.523               6.660795e+06   \n", "\n", "   进口_2025年01月-2025年02月_人民币同比(%)  进口_2025年02月_人民币万  进口_2025年02月_人民币同比(%)  \n", "1                        -4.2958      3.124779e+06               -1.0698  \n", "   数据类型(0：当期值/1：累计值) 地区(省市)  主要地区标识（1：是/0：否） 统计日期（格式：yyyy/mm/dd）    进出口额（人民币）  \\\n", "0                  1     广州                1            2025/2/1  1896.162614   \n", "1                  0     广州                1            2025/2/1   857.278641   \n", "\n", "      出口额（人民币）    进口额（人民币）    进出口同比     出口同比    进口同比  排序号  \n", "0  1230.083096  666.079518  14.3116  27.7625 -4.2958   16  \n", "1   544.800738  312.477904  18.9271  34.5230 -1.0698   16  \n"]}], "source": ["import pandas as pd\n", "\n", "# 读取Excel文件\n", "file_path = r\"C:\\Users\\<USER>\\Downloads\\2025031917314783106 (1).xls\"\n", "sheet_name = \"地区进出口总值表\"\n", "\n", "# 读取指定sheet的数据\n", "df = pd.read_excel(file_path, sheet_name=sheet_name, header=[0,1,2])\n", "\n", "# 查看所有列名，确认正确的列名结构\n", "# print(\"所有列名:\")\n", "# for col in df.columns:\n", "#     print(col)\n", "\n", "# ... 前面的代码保持不变 ...\n", "\n", "# 方法2：简化方式 - 重置列名为单层索引\n", "df_simple = df.copy()\n", "df_simple.columns = ['_'.join(col).strip() for col in df_simple.columns.values]\n", "guangzhou_data = df_simple[df_simple.iloc[:, 0] == '广东省广州市']\n", "\n", "# 过滤掉包含\"美元\"的列\n", "non_usd_cols = [col for col in guangzhou_data.columns if '美元' not in col]\n", "guangzhou_data = guangzhou_data[non_usd_cols]\n", "\n", "# 打印结果\n", "# ... 前面的代码保持不变 ...\n", "\n", "# 打印结果\n", "print(\"广东省广州市进出口数据(不含美元):\")\n", "print(guangzhou_data)\n", "\n", "# 新增代码：转换为指定格式并写入Excel\n", "# 创建新DataFrame\n", "output_data = pd.DataFrame({\n", "    \"数据类型(0：当期值/1：累计值)\": [1, 0],\n", "    \"地区(省市)\": [\"广州\", \"广州\"],\n", "    \"主要地区标识（1：是/0：否）\": [1, 1],\n", "    \"统计日期（格式：yyyy/mm/dd）\": [\"2025/2/1\", \"2025/2/1\"],\n", "    \"进出口额（人民币）\": [\n", "        guangzhou_data.iloc[0]['进出口_2025年01月-2025年02月_人民币万']/10000,\n", "        guangzhou_data.iloc[0]['进出口_2025年02月_人民币万']/10000\n", "    ],\n", "    \"出口额（人民币）\": [\n", "        guangzhou_data.iloc[0]['出口_2025年01月-2025年02月_人民币万']/10000,\n", "        guangzhou_data.iloc[0]['出口_2025年02月_人民币万']/10000\n", "    ],\n", "    \"进口额（人民币）\": [\n", "        guangzhou_data.iloc[0]['进口_2025年01月-2025年02月_人民币万']/10000,\n", "        guangzhou_data.iloc[0]['进口_2025年02月_人民币万']/10000\n", "    ],\n", "    \"进出口同比\": [\n", "        guangzhou_data.iloc[0]['进出口_2025年01月-2025年02月_人民币同比(%)'],\n", "        guangzhou_data.iloc[0]['进出口_2025年02月_人民币同比(%)']\n", "    ],\n", "    \"出口同比\": [\n", "        guangzhou_data.iloc[0]['出口_2025年01月-2025年02月_人民币同比(%)'],\n", "        guangzhou_data.iloc[0]['出口_2025年02月_人民币同比(%)']\n", "    ],\n", "    \"进口同比\": [\n", "        guangzhou_data.iloc[0]['进口_2025年01月-2025年02月_人民币同比(%)'],\n", "        guangzhou_data.iloc[0]['进口_2025年02月_人民币同比(%)']\n", "    ],\n", "    \"排序号\": [16, 16]\n", "})\n", "\n", "# 写入Excel文件\n", "# output_path = r\"C:\\Users\\<USER>\\Desktop\\海关数据\\小超海关统计\\全国进出口\\广州进出口数据.xlsx\"\n", "# output_data.to_excel(output_path, index=False)\n", "# print(f\"数据已成功保存到: {output_path}\")\n", "print(output_data)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}