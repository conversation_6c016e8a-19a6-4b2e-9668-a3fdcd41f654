import pandas as pd
import os

def inspect_file(file_path):
    """
    读取指定的Excel文件，并打印其前15行的原始内容，以便于分析其结构。
    """
    try:
        # 使用 xlrd 引擎来读取旧版的 .xls 文件
        df = pd.read_excel(file_path, engine='xlrd', header=None)
        
        print(f"\n=================================================")
        print(f"Inspecting file: {os.path.basename(file_path)}")
        print(f"=================================================\n")
        
        # 使用pandas的option_context来确保打印内容不被截断
        with pd.option_context('display.max_rows', 20, 'display.max_columns', None, 'display.width', 200):
            print(df.head(15))
            
    except Exception as e:
        print(f"Error reading {file_path}: {e}")

if __name__ == '__main__':
    # 选取一个有代表性的文件进行探查
    # 我们将探查两个文件，一个总值表，一个商品表，它们的结构可能不同
    files_to_inspect = [
        os.path.join('北京单月的', '（1）北京地区进出口商品总值表B：月度表（2025年1-5月）.xls'),
        os.path.join('北京单月的', '（8）北京地区出口主要商品量值表（2025年1-5月）.xls')
    ]
    
    for f in files_to_inspect:
        if os.path.exists(f):
            inspect_file(f)
        else:
            print(f"File not found: {f}")
