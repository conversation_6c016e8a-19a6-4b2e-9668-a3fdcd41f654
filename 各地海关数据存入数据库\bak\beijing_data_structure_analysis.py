import pandas as pd
import numpy as np
import datetime
import os
import re
from pathlib import Path

def analyze_single_excel_file(excel_path):
    """
    分析单个Excel文件的结构
    """
    filename = os.path.basename(excel_path)
    print(f"分析文件: {filename}")
    
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_path, header=None)
        print(f"  文件形状: {df.shape}")
        
        # 显示前几行内容
        print("  前5行内容:")
        for i in range(min(5, len(df))):
            row_data = [str(x)[:20] for x in df.iloc[i].tolist()[:6]]  # 限制显示长度
            print(f"    行{i}: {row_data}")
        
        # 查找可能的表头行
        print("  可能的表头行:")
        for i, row in df.iterrows():
            if i > 10:  # 只检查前10行
                break
            first_cell = str(row.iloc[0])
            if any(keyword in first_cell for keyword in ['项目', '商品', '国别', '地区', '年月日', '企业', '贸易']):
                print(f"    行{i}: {first_cell}")
                # 显示这一行的所有列
                header_row = [str(x) for x in row.tolist() if str(x) != 'nan']
                print(f"    完整表头: {header_row}")
        
        print("-" * 80)
        return True
        
    except Exception as e:
        print(f"  分析文件时出错: {e}")
        print("-" * 80)
        return False

def analyze_different_file_types(input_dir):
    """
    分析不同类型的北京海关Excel文件
    """
    # 定义文件类型模式
    file_patterns = {
        '(1)': '（1）',  # 总值表B：月度表
        '(2)': '（2）',  # 国别（地区）总值表
        '(5)': '（5）',  # 贸易方式总值表
        '(6)': '（6）',  # 出口商品贸易方式企业性质总值表
        '(7)': '（7）',  # 进口商品贸易方式企业性质总值表
        '(8)': '（8）',  # 出口主要商品量值表
        '(9)': '（9）'   # 进口主要商品量值表
    }
    
    # 获取所有Excel文件
    excel_files = []
    for file in os.listdir(input_dir):
        if file.endswith('.xls') and not file.startswith('~'):
            excel_files.append(file)
    
    print(f"找到 {len(excel_files)} 个Excel文件")
    
    # 为每种类型找一个样本文件进行分析
    for pattern_key, pattern_value in file_patterns.items():
        print(f"\n{'='*100}")
        print(f"分析文件类型 {pattern_key} - {pattern_value}")
        print(f"{'='*100}")
        
        # 找到该类型的一个文件
        sample_file = None
        for file in excel_files:
            if pattern_value in file:
                sample_file = file
                break
        
        if sample_file:
            sample_path = os.path.join(input_dir, sample_file)
            analyze_single_excel_file(sample_path)
        else:
            print(f"未找到类型 {pattern_key} 的文件")

def simple_convert_to_csv(excel_path, output_dir):
    """
    简单转换Excel为CSV，保持原始结构
    """
    filename = os.path.basename(excel_path)
    csv_filename = os.path.splitext(filename)[0] + '.csv'
    csv_path = os.path.join(output_dir, csv_filename)
    
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_path, header=None)
        
        # 保存为CSV
        df.to_csv(csv_path, index=False, encoding='utf-8-sig')
        print(f"已转换: {filename} -> {csv_filename}")
        return True
        
    except Exception as e:
        print(f"转换文件 {filename} 时出错: {e}")
        return False

def convert_samples_to_csv(input_dir, output_dir):
    """
    将每种类型的样本文件转换为CSV
    """
    # 创建输出目录
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    # 定义文件类型模式
    file_patterns = {
        '(1)': '（1）',  # 总值表B：月度表
        '(2)': '（2）',  # 国别（地区）总值表
        '(5)': '（5）',  # 贸易方式总值表
        '(6)': '（6）',  # 出口商品贸易方式企业性质总值表
        '(7)': '（7）',  # 进口商品贸易方式企业性质总值表
        '(8)': '（8）',  # 出口主要商品量值表
        '(9)': '（9）'   # 进口主要商品量值表
    }
    
    # 获取所有Excel文件
    excel_files = []
    for file in os.listdir(input_dir):
        if file.endswith('.xls') and not file.startswith('~'):
            excel_files.append(file)
    
    print(f"找到 {len(excel_files)} 个Excel文件")
    
    # 为每种类型转换一个样本文件
    converted_files = []
    for pattern_key, pattern_value in file_patterns.items():
        print(f"\n处理文件类型 {pattern_key} - {pattern_value}")
        
        # 找到该类型的一个文件（优先选择较新的文件）
        sample_files = [f for f in excel_files if pattern_value in f]
        if sample_files:
            # 选择一个2024年的文件作为样本
            sample_file = None
            for f in sample_files:
                if '2024' in f and ('1-12月' in f or '12月' in f):
                    sample_file = f
                    break
            if not sample_file:
                sample_file = sample_files[0]  # 如果没找到2024年的，就用第一个
            
            sample_path = os.path.join(input_dir, sample_file)
            if simple_convert_to_csv(sample_path, output_dir):
                converted_files.append(sample_file)
        else:
            print(f"  未找到类型 {pattern_key} 的文件")
    
    print(f"\n转换完成！共转换了 {len(converted_files)} 个样本文件")
    print(f"输出目录: {output_dir}")
    
    return converted_files

def main():
    """
    主函数：分析北京海关数据结构
    """
    input_directory = "北京海关统计数据_最终版"
    output_directory = "北京海关数据_样本分析"
    
    print("=" * 100)
    print("北京海关统计数据结构分析")
    print("=" * 100)
    print(f"输入目录: {input_directory}")
    print(f"输出目录: {output_directory}")
    print()
    
    # 第一步：分析不同类型文件的结构
    print("第一步：分析不同类型文件的结构")
    print("-" * 50)
    analyze_different_file_types(input_directory)
    
    # 第二步：将样本文件转换为CSV便于查看
    print(f"\n\n第二步：将样本文件转换为CSV便于查看")
    print("-" * 50)
    converted_files = convert_samples_to_csv(input_directory, output_directory)
    
    print("\n" + "=" * 100)
    print("分析完成！")
    print("请查看输出目录中的CSV文件来了解每种类型的数据结构")
    print("=" * 100)

if __name__ == '__main__':
    main()