from DrissionPage import ChromiumPage, SessionPage
import time
import os
import re
import shutil
import pandas as pd
from datetime import datetime
   #    2019年开始  - 2014都没有下载按钮
# 创建一个ChromiumPage实例，并设置下载路径
download_dir = "进出口商品收发货人所在地总值表_下载文件"
if not os.path.exists(download_dir):
    os.makedirs(download_dir)
    print(f"创建下载目录: {download_dir}")

# 设置ChromiumPage下载路径
# 创建临时下载目录用于存放原始下载文件
temp_download_dir = "temp_download"
if not os.path.exists(temp_download_dir):
    os.makedirs(temp_download_dir)
    print(f"创建临时下载目录: {temp_download_dir}")

# 设置ChromiumPage下载路径到临时目录
page = ChromiumPage(timeout=20)
page.set.download_path(os.path.abspath(temp_download_dir))
print(f"设置下载目录: {temp_download_dir}")

# 定义基础URL
base_url = "http://www.customs.gov.cn"

# 访问初始URL
start_url = "http://www.customs.gov.cn/customs/302249/zfxxgk/2799825/302274/302277/6348926/index.html"
page.get(start_url)

# 打印页面标题，确认访问成功
print(f"页面标题: {page.title}")

# 等待页面完全加载
time.sleep(2)

# 创建一个大列表来存储所有年份的链接
all_years_links = []

# 找到所有年份的链接
year_links_div = page.ele('.tjYear')
if year_links_div:
    year_links = year_links_div.eles('tag:a')
    year_urls = []
    
    # 提取所有年份的URL
    for year_link in year_links:
        href = year_link.attr('href')
        if href:
            if href.startswith('/'):
                full_year_url = base_url + href
            else:
                full_year_url = href
            year_text = year_link.text
            
            # 只添加2019年及以前的年份
            try:
                year_value = int(year_text.strip('年'))
                if year_value <= 2019:
                    year_urls.append((year_text, full_year_url))
                    print(f"添加年份: {year_text} (URL: {full_year_url})")
                else:
                    print(f"跳过年份: {year_text} (大于2019)")
            except ValueError:
                # 如果无法解析为数字，保险起见还是添加
                year_urls.append((year_text, full_year_url))
                print(f"添加无法解析的年份: {year_text} (URL: {full_year_url})")
    
    print(f"找到 {len(year_urls)} 个2019年及以前的年份链接")
    
    # 遍历每个年份的链接
    for year_text, year_url in year_urls:
        print(f"\n正在处理 {year_text} 年份数据: {year_url}")
        
        # 访问年份页面
        page.get(year_url)
        time.sleep(2)  # 等待页面加载
        
        # 查找包含"进出口商品收发货人所在地总值表"的元素
        target_ele = page.ele("进出口商品收发货人所在地总值表", timeout=5)
        if target_ele:
            print(f"找到目标元素: {target_ele.text}")
            
            # 获取下一个元素
            next_ele = target_ele.next()
            if next_ele:
                print(f"找到下一个元素: {next_ele.tag}")
                
                # 查找该元素中的所有a标签
                links = next_ele.eles('tag:a')
                
                # 该年份的链接列表
                year_links_list = []
                
                # 遍历所有a标签并获取完整超链接地址
                for link in links:
                    href = link.attr('href')
                    if href:
                        # 如果是相对路径，转换为完整URL
                        if not href.startswith('http'):
                            if href.startswith('/'):
                                full_url = base_url + href
                            else:
                                # 从当前URL构建完整路径
                                current_url = page.url
                                base_path = '/'.join(current_url.split('/')[:-1]) + '/'
                                full_url = base_path + href
                        else:
                            full_url = href
                        
                        # 添加到该年份的链接列表
                        link_info = {
                            "year": year_text,
                            "text": link.text,
                            "url": full_url
                        }
                        year_links_list.append(link_info)
                        print(f"链接文本: {link.text}, URL: {full_url}")
                
                # 将该年份的链接添加到总列表中
                all_years_links.extend(year_links_list)
                print(f"{year_text}年共找到 {len(year_links_list)} 个链接")
            else:
                print(f"{year_text}年未找到下一个元素")
        else:
            print(f"{year_text}年未找到包含'进出口商品收发货人所在地总值表'的元素")

# 打印所有年份的链接总数
print(f"\n总共找到 {len(all_years_links)} 个链接")
print("2019年及以前的链接已收集完成")

# 可以将结果保存到文件中
with open('进出口商品收发货人所在地总值表_2019年及以前_所有链接.txt', 'w', encoding='utf-8') as f:
    for link_info in all_years_links:
        f.write(f"{link_info['year']},{link_info['text']},{link_info['url']}\n")





#  读取保存的
print("链接已保存到文件: 进出口商品收发货人所在地总值表_2019年及以前_所有链接.txt")

# 从本地文件读取链接
all_years_links = []
base_url = "http://www.customs.gov.cn"
link_file = '进出口商品收发货人所在地总值表_2019年及以前_所有链接.txt'

print(f"从本地文件读取链接: {link_file}")
with open(link_file, 'r', encoding='utf-8') as f:
    for line in f:
        parts = line.strip().split(',', 2)  # 最多分成3部分（年份,文本,URL）
        if len(parts) == 3:
            year, text, url = parts
            link_info = {
                "year": year,
                "text": text,
                "url": url
            }
            all_years_links.append(link_info)

print(f"从本地文件读取了 {len(all_years_links)} 个链接")

# 遍历所有链接并下载数据
print("\n开始处理文件...")
download_count = 0
scrape_count = 0
error_count = 0

for link_info in all_years_links:
    try:
        year = link_info['year']
        text = link_info['text']
        url = link_info['url']
        
        print(f"\n正在处理: {year} - {text}")
        print(f"访问URL: {url}")
        
        # 访问详情页
        page.get(url)
        time.sleep(2)  # 等待页面加载
        
        # 查找文件名元素（包含"进出口商品收发货人所在地总值表"的最后一个元素）
        title_elements = page.eles("进出口商品收发货人所在地总值表")
        if title_elements and len(title_elements) > 0:
            title_element = title_elements[-1]  # 取最后一个标题元素
            file_title = title_element.text.strip()
            
            # 清理文件名（移除不允许的字符）
            file_title = re.sub(r'[\\/*?:"<>|]', '', file_title)
            
            # 构造文件名
            file_name = f"{year}_{file_title}.xlsx"
        else:
            print("未找到包含'进出口商品收发货人所在地总值表'的标题元素")
            # 使用链接文本作为备用文件名
            file_name = f"{year}_{text}.xlsx"
        
        # 构造完整的文件保存路径
        file_path = os.path.join(download_dir, file_name)
        print(f"准备处理文件: {file_name}")
        
        # 对于2019年及以前的年份，直接解析表格
        print("年份为2019年或以前，直接提取表格数据...")
        
        # 查找表格元素
        table_elements = page.eles('tag:table')
        
        if table_elements and len(table_elements) > 0:
            print(f"找到{len(table_elements)}个表格元素")
            
            # 找到最有可能是目标表格的元素（通常是较大的表格）
            target_table = None
            max_rows = 0
            
            for i, table in enumerate(table_elements):
                rows = table.eles('tag:tr')
                if len(rows) > max_rows:
                    max_rows = len(rows)
                    target_table = table
                print(f"表格 {i+1}: {len(rows)} 行")
            
            if target_table:
                print(f"选择了具有 {max_rows} 行的表格")
                
                # 获取表格HTML
                html_content = target_table.html
                
                try:
                    # 尝试使用pandas解析表格
                    # 根据表格结构确定表头行数
                    try:
                        # 先尝试双行表头
                        dfs = pd.read_html(html_content, header=[0,1], flavor='bs4')
                        df = dfs[0]
                        # 合并多级表头
                        df.columns = [' '.join(str(col) for col in cols if str(col) != 'nan').strip() for cols in df.columns.values]
                    except Exception:
                        try:
                            # 尝试单行表头
                            dfs = pd.read_html(html_content, header=0, flavor='bs4')
                            df = dfs[0]
                        except:
                            # 无表头
                            dfs = pd.read_html(html_content, header=None, flavor='bs4')
                            df = dfs[0]
                    
                    # 保存为Excel
                    df.to_excel(file_path, index=False)
                    print(f"表格数据已保存到: {file_path}")
                    scrape_count += 1
                except Exception as e:
                    print(f"解析表格数据时出错: {e}")
                    
                    # 备用方案：直接保存HTML到文件
                    html_file_path = os.path.join(download_dir, f"{year}_{file_title}_原始表格.html")
                    with open(html_file_path, 'w', encoding='utf-8') as html_file:
                        html_file.write(f"<html><body>{html_content}</body></html>")
                    print(f"无法解析表格，原始HTML已保存到: {html_file_path}")
                    error_count += 1
            else:
                print("未找到合适的表格")
                error_count += 1
        else:
            print("页面上未找到表格元素")
            error_count += 1
    except Exception as e:
        print(f"处理链接时出错: {e}")
        error_count += 1
    
    # 防止请求过于频繁
    time.sleep(2)

print(f"\n处理完成! 爬取成功: {scrape_count}, 失败: {error_count}")

# 清理临时下载目录
try:
    shutil.rmtree(temp_download_dir)
    print(f"临时下载目录已清理: {temp_download_dir}")
except Exception as e:
    print(f"清理临时下载目录时出错: {e}")

# 关闭浏览器
page.quit()



