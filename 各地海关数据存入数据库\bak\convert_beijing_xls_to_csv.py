import pandas as pd
import os
import glob
from pathlib import Path

def convert_xls_to_csv(input_folder, output_folder):
    """
    将指定输入文件夹中的所有 .xls 文件转换为 .csv 文件，
    并保存到指定的输出文件夹中，以便于后续的检查和分析。
    """
    print(f"开始将 {input_folder} 中的 .xls 文件转换为 CSV...")
    
    # 创建输出目录
    Path(output_folder).mkdir(parents=True, exist_ok=True)
    
    # 查找所有 .xls 文件
    xls_files = glob.glob(os.path.join(input_folder, "*.xls"))
    
    if not xls_files:
        print(f"在 {input_folder} 中未找到任何 .xls 文件。")
        return

    print(f"找到 {len(xls_files)} 个文件进行转换。")

    for xls_file in xls_files:
        try:
            filename = os.path.basename(xls_file)
            output_csv_path = os.path.join(output_folder, f"{Path(filename).stem}.csv")
            
            # 使用 pandas 读取 Excel 文件
            # header=None 确保读取所有行，不将任何行误判为表头
            df = pd.read_excel(xls_file, engine='xlrd', header=None)
            
            # 将 DataFrame 保存为 CSV 文件
            df.to_csv(output_csv_path, index=False, header=False, encoding='utf-8-sig')
            
            print(f"  - 成功转换: {filename} -> {output_csv_path}")
            
        except Exception as e:
            print(f"  - 转换失败: {filename}, 错误: {e}")
            
    print("\n所有文件转换完成。")

if __name__ == '__main__':
    input_dir = '北京单月的'
    output_dir = 'temp_csv_inspection'
    
    convert_xls_to_csv(input_dir, output_dir)
