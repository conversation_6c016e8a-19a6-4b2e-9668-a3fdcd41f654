import cx_Oracle as oracle
import pandas as pd
import logging
import os
import configparser
import sys
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    filename='data_migration.log'
)
logger = logging.getLogger('data_migration')

# 获取配置文件路径
script_dir = os.path.dirname(os.path.abspath(__file__))
config_path = os.path.join(script_dir, 'db_config.ini')

def get_db_config(section):
    """从配置文件读取数据库配置"""
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
    config = configparser.ConfigParser()
    config.read(config_path)
    
    if section not in config:
        raise ValueError(f"配置文件中未找到 {section} 节")
    
    return {
        'username': config[section]['username'],
        'password': config[section]['password'],
        'host': config[section]['host'],
        'port': config[section]['port'],
        'service_name': config[section]['service_name']
    }

def get_connection_string(db_config):
    """根据配置生成数据库连接字符串"""
    return f"{db_config['username']}/{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['service_name']}"

def get_column_names(table_name, conn):
    """获取表的列名"""
    cursor = conn.cursor()
    try:
        cursor.execute(f"""
        SELECT column_name 
        FROM all_tab_columns 
        WHERE table_name = '{table_name.upper()}'
        ORDER BY column_id
        """)
        columns = [row[0] for row in cursor.fetchall()]
        return columns
    finally:
        cursor.close()

def migrate_table_data(source_table_name, target_table_name=None, batch_size=1000):
    """将数据从测试环境迁移到生产环境"""
    if target_table_name is None:
        target_table_name = source_table_name
        
    source_conn = None
    target_conn = None
    
    try:
        # 连接源数据库（测试环境）
        test_db_config = get_db_config('TEST_DB')
        source_conn = oracle.connect(get_connection_string(test_db_config))
        print(f"已连接到测试环境数据库")
        
        # 连接目标数据库（生产环境）
        prod_db_config = get_db_config('PROD_DB')
        target_conn = oracle.connect(get_connection_string(prod_db_config))
        print(f"已连接到生产环境数据库")
        
        # 获取源表列名
        source_columns = get_column_names(source_table_name, source_conn)
        if not source_columns:
            raise ValueError(f"在测试环境中未找到表 {source_table_name} 或表没有列")
        
        # 获取目标表列名
        target_columns = get_column_names(target_table_name, target_conn)
        if not target_columns:
            raise ValueError(f"在生产环境中未找到表 {target_table_name} 或表没有列")
        
        # 确保两个表的列匹配
        # 这里采用简单的策略：使用两个表中都存在的列
        common_columns = [col for col in source_columns if col in target_columns]
        if not common_columns:
            raise ValueError(f"源表和目标表没有共同的列")
        
        print(f"迁移的列: {', '.join(common_columns)}")
        
        # 准备源表查询
        source_cursor = source_conn.cursor()
        source_query = f"SELECT {', '.join(common_columns)} FROM {source_table_name}"
        
        # 准备目标表插入
        target_cursor = target_conn.cursor()
        placeholders = ', '.join([':' + str(i+1) for i in range(len(common_columns))])
        insert_sql = f"INSERT INTO {target_table_name} ({', '.join(common_columns)}) VALUES ({placeholders})"
        
        # 开始数据传输
        start_time = datetime.now()
        print(f"开始数据迁移，时间: {start_time}")
        
        # 先清空目标表
        try:
            target_cursor.execute(f"DELETE FROM {target_table_name}")
            target_conn.commit()
            print(f"已清空目标表 {target_table_name}")
        except Exception as e:
            print(f"清空目标表时出错，将继续进行数据插入: {str(e)}")
            target_conn.rollback()
        
        # 分批获取和插入数据
        source_cursor.execute(source_query)
        total_rows = 0
        batch_data = []
        
        # 开始批处理插入
        for row in source_cursor:
            batch_data.append(row)
            
            if len(batch_data) >= batch_size:
                try:
                    target_cursor.executemany(insert_sql, batch_data)
                    target_conn.commit()
                    total_rows += len(batch_data)
                    print(f"已插入 {total_rows} 行数据")
                except Exception as e:
                    target_conn.rollback()
                    print(f"插入数据批次失败: {str(e)}")
                    logger.error(f"插入数据批次失败: {str(e)}")
                
                batch_data = []
        
        # 处理最后一批数据
        if batch_data:
            try:
                target_cursor.executemany(insert_sql, batch_data)
                target_conn.commit()
                total_rows += len(batch_data)
                print(f"已插入 {total_rows} 行数据")
            except Exception as e:
                target_conn.rollback()
                print(f"插入最后一批数据失败: {str(e)}")
                logger.error(f"插入最后一批数据失败: {str(e)}")
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        print(f"数据迁移完成，总共插入 {total_rows} 行数据")
        print(f"开始时间: {start_time}, 结束时间: {end_time}, 总耗时: {duration} 秒")
        
    except Exception as e:
        print(f"数据迁移过程中发生错误: {str(e)}")
        logger.error(f"数据迁移过程中发生错误: {str(e)}")
    finally:
        # 关闭连接
        if 'source_cursor' in locals() and source_cursor:
            source_cursor.close()
        if 'target_cursor' in locals() and target_cursor:
            target_cursor.close()
        if source_conn:
            source_conn.close()
        if target_conn:
            target_conn.close()

def main():
    if len(sys.argv) > 1:
        source_table = sys.argv[1]
        target_table = sys.argv[2] if len(sys.argv) > 2 else source_table
        batch_size = int(sys.argv[3]) if len(sys.argv) > 3 else 1000
        migrate_table_data(source_table, target_table, batch_size)
    else:
        # 默认迁移CS_SINGLEWINDOW_PRODUCT_CODE表到T_BC_SW_TS_CODE
        migrate_table_data('CS_SINGLEWINDOW_PRODUCT_CODE', 'T_BC_SW_TS_CODE')
        
if __name__ == "__main__":
    main() 