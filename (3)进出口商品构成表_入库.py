import pandas as pd
import os
import re
import cx_Oracle
import numpy as np

# 设置pandas显示选项
pd.set_option('display.max_columns', None)
pd.set_option('display.width', 1000)
pd.set_option('display.max_colwidth', None)

def convert_to_float_or_none(value):
    """清理数据，将'-'或空值转为None，其余转为浮点数"""
    if pd.isna(value) or str(value).strip() == '-':
        return None
    try:
        return float(str(value).replace(',', ''))
    except (ValueError, TypeError):
        return None

def parse_table_3(file_path):
    """
    专门为(3)进出口商品构成表编写的解析器。
    能够处理其复杂的双层、错位表头和数据结构。
    返回一个元组 (DataFrame, unit)
    """
    try:
        df_raw = pd.read_excel(file_path, header=None)

        # 1. 动态从文件内容中查找单位
        unit = None
        header_search_start_row = 0
        for i in range(min(10, len(df_raw))):
            row_text = ' '.join(str(s) for s in df_raw.iloc[i].tolist() if pd.notna(s))
            unit_match = re.search(r'单位[：:]\s*([^\s]+)', row_text)
            if unit_match:
                unit = unit_match.group(1).strip()
                header_search_start_row = i + 1
                break
        
        # 2. 查找表头起始行 (包含"出口"、"进口"和"按SITC分类"的行)
        header_start_row = -1
        for i, row in df_raw.iloc[header_search_start_row:].iterrows():
            row_str = ' '.join(str(s) for s in row if pd.notna(s))
            if '出口' in row_str and '进口' in row_str and '按SITC分类' in row_str:
                header_start_row = i
                break
        
        if header_start_row == -1:
            print(f"    [!] 错误: 在文件 '{os.path.basename(file_path)}' 中未找到表头行。")
            return None, None

        # 3. 数据从表头下两行开始
        data_start_row = header_start_row + 2
        
        # 4. 精确切片数据区域 (B-H列，共7列)
        df_data = df_raw.iloc[data_start_row:, 1:8].copy()
        
        # 5. 遍历并处理数据
        processed_data = []
        for i in range(len(df_data)):
            row = df_data.iloc[i].values
            
            composition = row[0]
            # 增加对"注:"的过滤
            if pd.isna(composition) or '注:' in str(composition):
                continue

            # 修改：增加对列数的检查，以兼容不同版本的文件格式
            processed_data.append({
                'COMPOSITION': str(composition).strip(),
                'MONTH_EXPORT': convert_to_float_or_none(row[1]),
                'YTD_EXPORT': convert_to_float_or_none(row[2]),
                'MONTH_IMPORT': convert_to_float_or_none(row[3]),
                'YTD_IMPORT': convert_to_float_or_none(row[4]),
                'YOY_EXPORT': convert_to_float_or_none(row[5]),
                'YOY_IMPORT': convert_to_float_or_none(row[6]) if len(row) > 6 else None
            })

        return pd.DataFrame(processed_data), unit

    except Exception as e:
        print(f"    [!] 解析文件 '{os.path.basename(file_path)}' 时出错: {e}")
        return None, None

def get_current_month_from_filename(filename):
    """从文件名中提取年月，返回 YYYYMMDD 格式"""
    # 更新正则表达式以兼容 "2021年10月" 和 "2021年1至10月" 两种格式
    match = re.search(r'(\d{4})年(?:1至)?(\d{1,2})月', filename)
    if match:
        year, month = match.groups()
        return f"{year}{month.zfill(2)}01"
    return None

def batch_process_directory(directory_path, cursor, conn):
    """遍历目录，对所有文件执行增量入库"""
    print(f"\n--- 开始扫描目录: {directory_path} ---")
    if not os.path.isdir(directory_path):
        print(f"    [!] 错误: 目录不存在")
        return

    for filename in sorted(os.listdir(directory_path)):
        if not (filename.lower().endswith(".xlsx") or filename.lower().endswith(".xls")):
            continue
        
        file_path = os.path.join(directory_path, filename)
        current_month_str = get_current_month_from_filename(filename)
        if not current_month_str:
            print(f"    [!] 警告: 无法从文件名 '{filename}' 中提取年月，跳过。")
            continue
        
        currency_type = "人民币" if "人民币" in directory_path else "美元"
        
        print(f"    [*] 正在处理: {filename}")
        
        try:
            # 1. 解析文件获取数据和单位
            df, unit_from_file = parse_table_3(file_path)
            
            # 如果未从文件中解析到单位，则使用基于货币类型的默认值
            unit = unit_from_file if unit_from_file else ("万元" if currency_type == "人民币" else "千美元")

            if df is None or df.empty:
                print(f"        -> 文件解析为空或失败，跳过。")
                continue
            
            print(f"        -> 文件解析成功，单位: {unit}")

            # 2. 查询当月数据库中已有的COMPOSITION
            select_keys_query = "SELECT COMPOSITION FROM temp_cus_mon_3 WHERE CURRENT_MONTH = TO_DATE(:1, 'YYYYMMDD') AND CURRENCY_TYPE = :2"
            cursor.execute(select_keys_query, (current_month_str, currency_type))
            existing_compositions = {row[0] for row in cursor.fetchall()}
            
            if existing_compositions:
                print(f"        -> 数据库中已存在 {len(existing_compositions)} 条当月记录。")

            # 3. 筛选出需要新增的数据
            df_new = df[~df['COMPOSITION'].isin(existing_compositions)]
            new_rows_count = len(df_new)

            if new_rows_count == 0:
                print(f"        -> 无新数据需要插入，跳过。")
                continue
            
            print(f"        -> 筛选出 {new_rows_count} 条新数据准备插入。")

            # 4. 准备并插入新数据
            df_new = df_new.copy() # 避免SettingWithCopyWarning
            df_new['CURRENT_MONTH'] = current_month_str
            df_new['CURRENCY_TYPE'] = currency_type
            df_new['UNIT'] = unit
            
            df_new.replace({np.nan: None}, inplace=True)
            data_to_insert = [tuple(row) for row in df_new.to_records(index=False)]

            insert_query = """
            INSERT INTO temp_cus_mon_3 (
                COMPOSITION, MONTH_EXPORT, YTD_EXPORT, MONTH_IMPORT, YTD_IMPORT,
                YOY_EXPORT, YOY_IMPORT,
                CURRENT_MONTH, CURRENCY_TYPE, UNIT
            ) VALUES (
                :1, :2, :3, :4, :5, :6, :7, TO_DATE(:8, 'YYYYMMDD'), :9, :10
            )
            """
            cursor.executemany(insert_query, data_to_insert)
            print(f"        -> 成功插入 {cursor.rowcount} 条新记录。")

        except cx_Oracle.Error as e:
            print(f"        [!] 数据库错误: {e}")
            conn.rollback()
        except Exception as e:
            print(f"        [!] 未知错误: {e}")
            conn.rollback()

    print(f"--- 目录扫描完成 ---")

def debug_single_file():
    """用于调试的函数，只处理单个文件并打印第一条解析结果"""
    # 使用一个已知的文件进行调试
    base_dir = os.getcwd()
    # 注意：这里的路径分隔符在Windows上通常是'\'，但os.path.join会自动处理
    rmb_dir = os.path.join(base_dir, "进出口商品统计表_人民币值", "(3)进出口商品构成表_人民币值")
    # 选择一个具体的文件
    test_file = os.path.join(rmb_dir, "2021_（3）2021年10月进出口商品构成表（人民币值）.xlsx")
    
    print(f"--- 开始单文件调试 ---")
    print(f"[*] 目标文件: {test_file}")

    if not os.path.exists(test_file):
        print(f"[!] 错误: 调试文件不存在。")
        return

    df, unit = parse_table_3(test_file)

    if df is not None and not df.empty:
        print("\n--- 解析成功 ---")
        print("[*] 打印第一条完整数据记录:")
        
        # 为了更清晰地显示，我们将第一行数据转为字典格式打印
        first_record = df.iloc[0].to_dict()
        
        # 格式化打印字典
        for key, value in first_record.items():
            print(f"    - {key}: {value}")
        print(f"    - 单位: {unit}")

    elif df is not None and df.empty:
         print("\n--- 解析完成，但未提取到数据 ---")
    else:
        print("\n--- 解析失败 ---")


if __name__ == "__main__":
    # 设置为True以运行调试模式，设置为False以运行正常的批量入库
    DEBUG_MODE = False

    if DEBUG_MODE:
        debug_single_file()
    else:
        base_dir = os.getcwd()
        rmb_dir = os.path.join(base_dir, "进出口商品统计表_人民币值", "(3)进出口商品构成表_人民币值")
        usd_dir = os.path.join(base_dir, "进出口商品统计表_美元值", "(3)进出口商品构成表_美元值")

        conn = None
        try:
            print("正在连接到Oracle数据库...")
            conn = cx_Oracle.connect("manifest_dcb/manifest_dcb@192.168.1.151/TEST")
            cursor = conn.cursor()
            conn.autocommit = False # 手动控制事务
            print("数据库连接成功。")

            batch_process_directory(rmb_dir, cursor, conn)
            batch_process_directory(usd_dir, cursor, conn)
            
            conn.commit()
            print("\n数据提交成功。")
            
        except Exception as e:
            print(f"\n[!!!] 批量处理期间发生未知错误: {e}")
            if conn: conn.rollback()
        finally:
            if 'cursor' in locals() and cursor: cursor.close()
            if conn:
                conn.close()
                print("数据库连接已关闭。") 