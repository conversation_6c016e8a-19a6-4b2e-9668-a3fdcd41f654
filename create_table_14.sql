CREATE TABLE temp_cus_mon_14 (
    COMMODITY_NAME VARCHAR2(500),
    CURRENT_MONTH DATE,
    CURRENCY_TYPE VARCHAR2(10),
    UNIT VARCHAR2(50),
    MONTH_QUANTITY NUMBER,
    MONTH_AMOUNT NUMBER,
    YTD_QUANTITY NUMBER,
    YTD_AMOUNT NUMBER,
    MONTH_QUANTITY_YOY NUMBER,
    MONTH_AMOUNT_YOY NUMBER,
    YTD_QUANTITY_YOY NUMBER,
    YTD_AMOUNT_YOY NUMBER,
    YOY_MONTH_IMPORT_WEIGHT NUMBER,
    YOY_YTD_IMPORT_WEIGHT NUMBER,
    CONSTRAINT pk_temp_cus_mon_14 PRIMARY KEY (COMMODITY_NAME, UNIT, CURRENT_MONTH, CURRENCY_TYPE)
);

COMMENT ON TABLE temp_cus_mon_14 IS '（14）进口主要商品量值表 - 月度数据临时表';
COMMENT ON COLUMN temp_cus_mon_14.COMMODITY_NAME IS '商品名称';
COMMENT ON COLUMN temp_cus_mon_14.CURRENT_MONTH IS '当前月份（YYYYMMDD格式）';
COMMENT ON COLUMN temp_cus_mon_14.CURRENCY_TYPE IS '货币类型（人民币/美元）';
COMMENT ON COLUMN temp_cus_mon_14.UNIT IS '计量单位';
COMMENT ON COLUMN temp_cus_mon_14.MONTH_QUANTITY IS '当月-数量';
COMMENT ON COLUMN temp_cus_mon_14.MONTH_AMOUNT IS '当月-金额（千元）';
COMMENT ON COLUMN temp_cus_mon_14.YTD_QUANTITY IS '累计-数量';
COMMENT ON COLUMN temp_cus_mon_14.YTD_AMOUNT IS '累计-金额（千元）';
COMMENT ON COLUMN temp_cus_mon_14.MONTH_QUANTITY_YOY IS '当月同比-数量(%)';
COMMENT ON COLUMN temp_cus_mon_14.MONTH_AMOUNT_YOY IS '当月同比-金额(%)';
COMMENT ON COLUMN temp_cus_mon_14.YTD_QUANTITY_YOY IS '累计同比-数量(%)';
COMMENT ON COLUMN temp_cus_mon_14.YTD_AMOUNT_YOY IS '累计同比-金额(%)';
COMMENT ON COLUMN temp_cus_mon_14.YOY_MONTH_IMPORT_WEIGHT IS '比重-当月占全部进口比重(%)';
COMMENT ON COLUMN temp_cus_mon_14.YOY_YTD_IMPORT_WEIGHT IS '比重-累计占全部进口比重(%)'; 