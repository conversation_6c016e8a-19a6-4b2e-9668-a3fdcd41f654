import pandas as pd
import os
import glob

# 设置正确的目录
base_dir = r'C:\Users\<USER>\Desktop\海关数据\小超海关统计\全国进出口\各地海关数据存入数据库'
data_dir = os.path.join(base_dir, '北京单月的')

print(f"基础目录: {base_dir}")
print(f"数据目录: {data_dir}")

# 列出目录中的所有文件
try:
    files = os.listdir(data_dir)
    print("\n目录中的文件:")
    for i, file in enumerate(files):
        print(f"{i+1}. {file}")
except Exception as e:
    print(f"列出目录文件失败: {e}")
    # 尝试使用glob
    try:
        pattern = os.path.join(data_dir, "*.*")
        files = glob.glob(pattern)
        print("\n使用glob找到的文件:")
        for i, file in enumerate(files):
            print(f"{i+1}. {os.path.basename(file)}")
    except Exception as e2:
        print(f"使用glob也失败: {e2}")

# 尝试直接构造文件路径
file_patterns = [
    "(1)北京地区进出口商品总值表B：月度表（2025年1-5月）.xls",
    "(2)北京地区进出口商品国别（地区）总值表（2025年1-5月）.xls"
]

print("\n尝试读取文件:")
for pattern in file_patterns:
    file_path = os.path.join(data_dir, pattern)
    print(f"尝试路径: {file_path}")
    
    # 检查文件是否存在
    if os.path.exists(file_path):
        print("文件存在")
        try:
            # 尝试不同的读取方式
            df = pd.read_excel(file_path, engine='xlrd')
            print(f"成功读取文件: {pattern}")
            print(f"数据形状: {df.shape}")
            print("前5行数据:")
            print(df.head())
            break
        except Exception as e:
            print(f"使用xlrd读取失败: {e}")
            
            try:
                df = pd.read_excel(file_path, engine='openpyxl')
                print(f"使用openpyxl读取成功: {pattern}")
                print(f"数据形状: {df.shape}")
                print("前5行数据:")
                print(df.head())
                break
            except Exception as e2:
                print(f"使用openpyxl读取也失败: {e2}")
    else:
        print("文件不存在")
        
        # 尝试模糊匹配
        try:
            pattern_glob = os.path.join(data_dir, "*.xls")
            xls_files = glob.glob(pattern_glob)
            if xls_files:
                print(f"找到 {len(xls_files)} 个.xls文件")
                for xls_file in xls_files[:2]:  # 只处理前两个
                    print(f"尝试读取: {xls_file}")
                    try:
                        df = pd.read_excel(xls_file, engine='xlrd')
                        print(f"成功读取: {os.path.basename(xls_file)}")
                        print(f"数据形状: {df.shape}")
                        print("前5行数据:")
                        print(df.head())
                        break
                    except Exception as e:
                        print(f"读取失败: {e}")
                        try:
                            df = pd.read_excel(xls_file, engine='openpyxl')
                            print(f"使用openpyxl读取成功: {os.path.basename(xls_file)}")
                            print(f"数据形状: {df.shape}")
                            print("前5行数据:")
                            print(df.head())
                            break
                        except Exception as e2:
                            print(f"使用openpyxl读取也失败: {e2}")
        except Exception as e3:
            print(f"模糊匹配也失败: {e3}")