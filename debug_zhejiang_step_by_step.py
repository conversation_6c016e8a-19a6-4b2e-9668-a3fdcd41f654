#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import os
from analyze_customs_data import (
    read_march_zhejiang_data, 
    read_april_zhejiang_data,
    calculate_correct_monthly_yoy
)

def debug_zhejiang_processing_trade():
    print("=== 浙江加工贸易同比计算调试 ===")
    
    # 1. 读取3月数据
    print("\n1. 读取3月数据...")
    march_data = read_march_zhejiang_data()
    print(f"3月加工贸易累计值: {march_data.get('processing_trade_cumulative', 0)}")
    print(f"3月加工贸易累计同比: {march_data.get('processing_trade_cumulative_yoy', 0)}%")
    
    # 2. 读取4月数据
    print("\n2. 读取4月数据...")
    april_data = read_april_zhejiang_data()
    
    # 查找4月数据文件
    april_file = "20250530/2025年1-4月 -浙江/2025年1-4月浙江省进出口情况.xlsx"
    if not os.path.exists(april_file):
        print(f"错误: 找不到文件 {april_file}")
        return
    
    # 读取Excel文件
    df = pd.read_excel(april_file, sheet_name=0)
    print(f"\n4月数据文件列数: {len(df.columns)}")
    print(f"4月数据文件行数: {len(df)}")
    
    # 查找列索引
    cumulative_col = None
    cumulative_yoy_col = None
    import_export_col = None
    
    for i, col in enumerate(df.columns):
        col_str = str(col).strip()
        print(f"列{i}: {col_str}")
        if '累计' in col_str and ('进出口' in col_str or '总值' in col_str):
            cumulative_col = i
        elif '累计' in col_str and '同比' in col_str:
            cumulative_yoy_col = i
        elif '进出口' in col_str and '累计' not in col_str:
            import_export_col = i
    
    print(f"\n找到的列索引:")
    print(f"累计列: {cumulative_col}")
    print(f"累计同比列: {cumulative_yoy_col}")
    print(f"进出口列: {import_export_col}")
    
    # 3. 查找加工贸易相关行
    print("\n3. 查找加工贸易相关行...")
    processing_trade_row_indices = []
    april_processing_cumulative = 0
    
    for i in range(len(df)):
        cell_value = str(df.iloc[i, 0]).strip()
        if '加工贸易' in cell_value and cell_value != '加工贸易':
            processing_trade_row_indices.append(i)
            try:
                cumulative_value_str = str(df.iloc[i, cumulative_col]) if cumulative_col is not None else str(df.iloc[i, import_export_col])
                cumulative_value_str = cumulative_value_str.replace(',', '')
                cumulative_value = float(cumulative_value_str)
                april_processing_cumulative += cumulative_value
                print(f"行{i}: {cell_value} = {cumulative_value}")
            except Exception as e:
                print(f"行{i}: {cell_value} - 读取错误: {e}")
    
    print(f"\n4月加工贸易累计总计: {april_processing_cumulative}")
    
    # 4. 计算4月单月值
    march_cumulative_value = march_data.get('processing_trade_cumulative', 0)
    total_processing_monthly = april_processing_cumulative - march_cumulative_value
    print(f"4月单月值 = {april_processing_cumulative} - {march_cumulative_value} = {total_processing_monthly}")
    
    # 5. 读取4月累计同比
    print("\n4. 读取4月累计同比...")
    weighted_avg_cumulative_yoy = 0
    for i in range(len(df)):
        cell_value = str(df.iloc[i, 0]).strip()
        if cell_value == '加工贸易':
            try:
                cumulative_yoy = df.iloc[i, cumulative_yoy_col] if cumulative_yoy_col is not None else 0
                if isinstance(cumulative_yoy, str):
                    cumulative_yoy = cumulative_yoy.replace('%', '')
                weighted_avg_cumulative_yoy = float(cumulative_yoy)
                print(f"找到加工贸易行，累计同比: {weighted_avg_cumulative_yoy}%")
                break
            except Exception as e:
                print(f"读取加工贸易累计同比错误: {e}")
    
    # 6. 使用calculate_correct_monthly_yoy函数计算
    print("\n5. 计算单月同比...")
    march_cumulative_yoy = march_data.get('processing_trade_cumulative_yoy', 0)
    
    print(f"传入参数:")
    print(f"  4月单月值: {total_processing_monthly}")
    print(f"  4月累计值: {april_processing_cumulative}")
    print(f"  4月累计同比: {weighted_avg_cumulative_yoy}%")
    print(f"  3月累计值: {march_cumulative_value}")
    print(f"  3月累计同比: {march_cumulative_yoy}%")
    
    correct_monthly_yoy = calculate_correct_monthly_yoy(
        total_processing_monthly, april_processing_cumulative, weighted_avg_cumulative_yoy,
        march_cumulative_value, march_cumulative_yoy
    )
    
    print(f"\n计算结果: {correct_monthly_yoy}%")
    
    # 7. 手动验证计算过程
    print("\n6. 手动验证计算过程...")
    
    # 根据累计同比推算去年累计值
    last_year_april_cumulative = april_processing_cumulative / (1 + weighted_avg_cumulative_yoy / 100)
    last_year_march_cumulative = march_cumulative_value / (1 + march_cumulative_yoy / 100)
    last_year_april_monthly = last_year_april_cumulative - last_year_march_cumulative
    
    print(f"去年4月累计 = {april_processing_cumulative} / (1 + {weighted_avg_cumulative_yoy}/100) = {last_year_april_cumulative}")
    print(f"去年3月累计 = {march_cumulative_value} / (1 + {march_cumulative_yoy}/100) = {last_year_march_cumulative}")
    print(f"去年4月单月 = {last_year_april_cumulative} - {last_year_march_cumulative} = {last_year_april_monthly}")
    
    if last_year_april_monthly != 0:
        manual_yoy = (total_processing_monthly / last_year_april_monthly - 1) * 100
        print(f"手动计算同比 = ({total_processing_monthly} / {last_year_april_monthly} - 1) * 100 = {manual_yoy}%")
    else:
        print("去年4月单月为0，无法计算同比")
    
    print("\n=== 调试完成 ===")

if __name__ == "__main__":
    debug_zhejiang_processing_trade()