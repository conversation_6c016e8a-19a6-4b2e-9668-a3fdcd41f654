# %%
from DrissionPage import Chromium, ChromiumOptions
from DrissionPage import SessionPage
from urllib.parse import urlparse
import shutil
import json
import time
# -*- coding: utf-8 -*-
# @Software: PyCharm
import requests
import time
import json
import os
import re
import random

# --- 日志文件处理 ---
LOG_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', 'download_log.json')

def load_log():
    """加载日志文件，如果不存在则创建一个空的。"""
    if not os.path.exists(LOG_FILE):
        return {"national": [], "guangdong": []}
    with open(LOG_FILE, 'r', encoding='utf-8') as f:
        return json.load(f)

def save_log(log_data):
    """保存日志数据到文件。"""
    with open(LOG_FILE, 'w', encoding='utf-8') as f:
        json.dump(log_data, f, indent=4, ensure_ascii=False)

def is_file_logged(filename, log_data):
    """检查文件是否已在日志中。"""
    return filename in log_data.get("national", [])

def log_file(filename, log_data):
    """将文件添加到日志中。"""
    if filename not in log_data["national"]:
        log_data["national"].append(filename)

# --- 文件下载路径设置 ---
# 获取脚本所在目录
script_dir = os.path.dirname(os.path.abspath(__file__))
# 构建目标下载路径，即 ../../文件落地/全国进出口总值表
download_path = os.path.join(script_dir, '..', '..', '文件落地', '全国进出口总值表')

# 如果目标文件夹不存在则创建
if not os.path.exists(download_path):
    os.makedirs(download_path)
    print(f"已创建目录: {download_path}")



# %%
#tab = Chromium().latest_tab

co = ChromiumOptions().auto_port()

tab = Chromium(addr_or_opts=co).latest_tab



def sanitize_filename(filename):
    """
    去除文件名中的非法字符
    :param filename: 原始文件名
    :return: 处理后的文件名
    """
    INVALID_CHARS_REGEX = r'[\\/*?:"<>|]'

    return re.sub(INVALID_CHARS_REGEX, '', filename)

def download_file(url, save_path):
    # 这里假设 SessionPage 是已经定义好的类
    page = SessionPage()
    res = page.download(url, save_path)
    print(res)
    return res

def find_a_to_down(text_path,download_path):
    down_name = text_path.text

    new_month = ''
    # 定位这个元素的下一个元素
    text_path = text_path.parent().children()[1]
    # print(text_path)
    # 获取 td 元素下所有的 a 标签
    a_elements = text_path.children('tag:a')

    # 用于存储最后一个有效的 href
    last_valid_href = None

    # 遍历所有 a 标签
    for a in a_elements:
        href = a.attr('href')
        if href:
            last_valid_href = href
            new_month = a.text
            print(new_month)

    # 输出最后一个有效的 href
    if last_valid_href:
        print('# 输出最后一个有效的 href:',last_valid_href)
    else:
        print("未找到有效的 a 标签链接。")

    print('new_month:',new_month)
    tab.get(last_valid_href)


    down_name = tab.ele('xpath=/html/body/div[4]/div/div[2]/div/div/div[1]/h2').text

    # 设置下载路径
    # download_path = r'C:\Users\<USER>\Desktop\海关数据\总署统计快讯\全国'
    download_path = download_path

    # 假设 tab 是已经定义好的对象
    this_url = tab.eles('下载')[-1].attr('href')
    # 假设 tab 是已经定义好的对象
    this_url = tab.eles('下载')[-1].attr('href')
    download_result = download_file(this_url, download_path)
    # 从 URL 中提取文件名
    parsed_url = urlparse(this_url)
    web_file_name = os.path.basename(parsed_url.path)
    print(web_file_name)

    # 假设 down_name 是已经定义好的变量
    # 设置文件名字
    file_name = down_name + '.xls'

    # 处理新文件名，去除非法字符
    new_file_name = sanitize_filename(file_name)

    # 构建原文件和新文件的完整路径
    old_file_path = os.path.join(download_path, web_file_name)
    new_file_path = os.path.join(download_path, new_file_name)

    try:
        # 重命名文件
        os.rename(old_file_path, new_file_path)
        print(f"文件重命名成功，原文件 {web_file_name} 已重命名为 {new_file_name}")
    except FileNotFoundError:
        print(f"未找到文件: {old_file_path}，请检查文件路径是否正确。")
    except FileExistsError:
        print(f"新文件名 {new_file_name} 已存在，请更换新的文件名。")
    except PermissionError:
        print("没有权限对文件进行重命名操作，请检查文件权限。")
    except Exception as e:
        print(f"重命名文件时出现未知错误: {e}")



def find_a_to_down_last_mon(text_path,download_path):
    down_name = text_path.text

    new_month = ''
    last_month = ''
    # 定位这个元素的下一个元素
    text_path = text_path.parent().children()[1]
    # print(text_path)
    # 获取 td 元素下所有的 a 标签
    a_elements = text_path.children('tag:a')

    # 用于存储最后一个有效的 href
    last_valid_href = None
    last_href = None
    # 遍历所有 a 标签
    for a in a_elements:
        href = a.attr('href')
        if href:
            last_href = last_valid_href
            last_valid_href = href


            last_month = new_month
            new_month = a.text
            print(new_month)

    new_month = last_month
    # 输出最后一个有效的 href
    if last_valid_href:
        print('# 输出最后一个有效的 href:',last_valid_href)
    else:
        print("未找到有效的 a 标签链接。")

    print('new_month:',new_month)
    tab.get(last_href)


    down_name = tab.ele('xpath=/html/body/div[4]/div/div[2]/div/div/div[1]/h2').text
    print(down_name)
    # 设置下载路径
    # download_path = r'C:\Users\<USER>\Desktop\海关数据\总署统计快讯\全国'
    download_path = download_path
    # 假设 tab 是已经定义好的对象
    this_url = tab.eles('下载')[-1].attr('href')
    # 假设 tab 是已经定义好的对象
    this_url = tab.eles('下载')[-1].attr('href')
    download_result = download_file(this_url, download_path)
    # 从 URL 中提取文件名
    parsed_url = urlparse(this_url)
    web_file_name = os.path.basename(parsed_url.path)
    print(web_file_name)

    # 假设 down_name 是已经定义好的变量
    # 设置文件名字
    file_name = down_name + '.xls'

    # 处理新文件名，去除非法字符
    new_file_name = sanitize_filename(file_name)

    # 构建原文件和新文件的完整路径
    old_file_path = os.path.join(download_path, web_file_name)
    new_file_path = os.path.join(download_path, new_file_name)

    try:
        # 重命名文件
        os.rename(old_file_path, new_file_path)
        print(f"文件重命名成功，原文件 {web_file_name} 已重命名为 {new_file_name}")
    except FileNotFoundError:
        print(f"未找到文件: {old_file_path}，请检查文件路径是否正确。")
    except FileExistsError:
        print(f"新文件名 {new_file_name} 已存在，请更换新的文件名。")
    except PermissionError:
        print("没有权限对文件进行重命名操作，请检查文件权限。")
    except Exception as e:
        print(f"重命名文件时出现未知错误: {e}")


def download_reports_for_year(year, download_path, tab_instance):
    """
    根据指定年份和分页下载"（1）...全国进出口总值表（人民币值）"报表。

    :param year: 要下载的年份 (int)
    :param download_path: 文件保存路径 (str)
    :param tab_instance: DrissionPage 的 tab 对象
    """
    print(f"--- 开始下载 {year} 年的报表 ---")
    page_num = 1
    reports_found_for_year = False

    download_log = load_log()

    while True:
        print(f"正在搜索 {year} 年的报表, 第 {page_num} 页...")
        
        # 使用 DrissionPage 原生组合语法，精确定位目标报表链接
        selector = f"t:a@@title:{year}年@@title:全国进出口总值表（人民币值）@@title:（1）"
        links_on_page = tab_instance.eles(selector)

        if links_on_page:
            reports_found_for_year = True
            print(f"在第 {page_num} 页找到 {len(links_on_page)} 个匹配的报表。")

            # 先收集本页所有链接信息，再进行页面跳转和下载，防止元素失效
            reports_to_download = []
            for link in links_on_page:
                reports_to_download.append({'url': link.attr('href'), 'title': link.text})
            
            for report in reports_to_download:
                report_title = report['title']
                print(f"正在处理: {report_title}")

                file_name = report_title + '.xls'
                new_file_name = sanitize_filename(file_name)

                if is_file_logged(new_file_name, download_log):
                    print(f"文件 '{new_file_name}' 已在日志中，跳过下载。")
                    continue
                
                new_file_path = os.path.join(download_path, new_file_name)

                if os.path.exists(new_file_path):
                    print(f"文件 '{new_file_name}' 已存在于本地，记录日志并跳过。")
                    log_file(new_file_name, download_log)
                    save_log(download_log)
                    continue

                tab_instance.get(report['url'])
                time.sleep(random.uniform(1, 2))

                try:
                    download_url = tab_instance.eles('下载')[-1].attr('href')
                    download_file(download_url, download_path)
                    
                    web_file_name = os.path.basename(urlparse(download_url).path)
                    old_file_path = os.path.join(download_path, web_file_name)

                    timeout = 60
                    start_time = time.time()
                    while not os.path.exists(old_file_path):
                        time.sleep(1)
                        if time.time() - start_time > timeout:
                            print(f"错误：等待下载文件 {web_file_name} 超时。")
                            break
                    
                    if os.path.exists(old_file_path):
                        os.rename(old_file_path, new_file_path)
                        print(f"文件已保存为: {new_file_name}")
                        # 下载成功后记录日志
                        log_file(new_file_name, download_log)
                        save_log(download_log)

                except Exception as e:
                    print(f"处理报表 '{report_title}' 时出错: {e}")
                
                tab_instance.back()
                time.sleep(random.uniform(1, 2))
        else:
            print(f"在第 {page_num} 页未找到 {year} 年的目标报表。")

        # 使用 DrissionPage 原生组合语法，查找并点击"下一页"
        next_button = tab_instance.ele('t:a@@class:pagingNormal@@class:next', timeout=2)
        if next_button and 'pagingDisabled' not in (next_button.attr('class') or ''):
            print("找到'下一页'，准备翻页...")
            next_button.click()
            time.sleep(random.uniform(2, 3))
            page_num += 1
        else:
            print(f"已到达最后一页或未找到'下一页'按钮。结束 {year} 年的搜索。")
            break



# %%
# 主程序
if __name__ == "__main__":
    # 目标网址
    TARGET_URL = 'http://www.customs.gov.cn/customs/302249/zfxxgk/2799825/302274/302275/index.html'
    
    # 要下载的年份列表，优先处理2025年
    YEARS_TO_DOWNLOAD = [2024]
    
    # 按年份下载报表
    for year in YEARS_TO_DOWNLOAD:
        # 每次都重新访问初始页面，确保从第一页开始搜索
        print(f"\n准备开始处理 {year} 年的数据...")
        tab.get(TARGET_URL)
        time.sleep(3) # 等待页面加载
        download_reports_for_year(year, download_path, tab)

    print("\n--- 所有下载任务已完成 ---")
    tab.quit()

    # --- 调用新的通用入库脚本 ---
    print("\n--- 开始执行数据入库流程 ---")
    # 假设入库脚本与当前脚本在同一目录下或父目录下
    # 构建到入库脚本的路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # 入库脚本预计在父目录中
    importer_script_path = os.path.join(os.path.dirname(current_dir), '贸易数据入库.py')

    if os.path.exists(importer_script_path):
        try:
            # 使用os.system来执行另一个Python脚本
            print(f"正在执行: {importer_script_path}")
            os.system(f'python "{importer_script_path}"')
            print("--- 数据入库流程已结束 ---")
        except Exception as e:
            print(f"执行入库脚本时发生错误: {e}")
    else:
        # 如果入库脚本在当前目录
        importer_script_path_alt = os.path.join(current_dir, '贸易数据入库.py')
        if os.path.exists(importer_script_path_alt):
            try:
                print(f"正在执行: {importer_script_path_alt}")
                os.system(f'python "{importer_script_path_alt}"')
                print("--- 数据入库流程已结束 ---")
            except Exception as e:
                print(f"执行入库脚本时发生错误: {e}")
        else:
            print(f"错误：未找到入库脚本。请确认 '贸易数据入库.py' 存在于项目根目录或当前脚本目录。")


