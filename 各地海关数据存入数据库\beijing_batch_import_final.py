"""
北京海关数据完整批量入库程序
基于成功的单文件导入逻辑
"""

import pandas as pd
import cx_Oracle
import os
from datetime import datetime

def import_single_csv_to_oracle(csv_file_path, table_name, db_config):
    """
    导入单个CSV文件到Oracle数据库（基于成功的逻辑）
    """
    try:
        filename = os.path.basename(csv_file_path)
        print(f"正在处理: {filename}")
        
        # 检查文件是否存在
        if not os.path.exists(csv_file_path):
            print(f"❌ 文件不存在: {csv_file_path}")
            return False, 0
        
        # 读取CSV文件
        df = pd.read_csv(csv_file_path, encoding='utf-8-sig')
        print(f"  读取到 {len(df)} 条记录，{len(df.columns)} 个字段")
        
        if len(df) == 0:
            print("  CSV文件为空，跳过")
            return True, 0
        
        # 连接Oracle数据库
        conn = cx_Oracle.connect(**db_config)
        cursor = conn.cursor()
        
        # 成功的插入SQL（简化版，不使用TO_DATE）
        insert_sql = f"""
        INSERT INTO {table_name} (
            STAT_DATE, STAT_TYPE, STAT_NAME, STAT_CODE, STAT_CONTENT_RAW, STAT_CONTENT_CLEANSE,
            ACC_A_CNY_AMOUNT, ACC_A_CNY_YOY, ACC_A_USD_AMOUNT, ACC_A_USD_YOY,
            MON_A_CNY_AMOUNT, MON_A_CNY_YOY, MON_A_USD_AMOUNT, MON_A_USD_YOY,
            ACC_E_CNY_AMOUNT, ACC_E_CNY_YOY, ACC_E_USD_AMOUNT, ACC_E_USD_YOY,
            MON_E_CNY_AMOUNT, MON_E_CNY_YOY, MON_E_USD_AMOUNT, MON_E_USD_YOY,
            ACC_I_CNY_AMOUNT, ACC_I_CNY_YOY, ACC_I_USD_AMOUNT, ACC_I_USD_YOY,
            MON_I_CNY_AMOUNT, MON_I_CNY_YOY, MON_I_USD_AMOUNT, MON_I_USD_YOY,
            ACC_E_AMOUNT, ACC_E_AMOUNT_UNIT, ACC_E_AMOUNT_YOY,
            MON_E_AMOUNT, MON_E_AMOUNT_UNIT, MON_E_AMOUNT_YOY,
            ACC_I_AMOUNT, ACC_I_AMOUNT_UNIT, ACC_I_AMOUNT_YOY,
            MON_I_AMOUNT, MON_I_AMOUNT_UNIT, MON_I_AMOUNT_YOY,
            RANK_MARKERS, DATA_SOURCE, EMPHASIS_OR_EMERGING_MARK, CREATE_TIME
        ) VALUES (
            :1, :2, :3, :4, :5, :6,
            :7, :8, :9, :10,
            :11, :12, :13, :14,
            :15, :16, :17, :18,
            :19, :20, :21, :22,
            :23, :24, :25, :26,
            :27, :28, :29, :30,
            :31, :32, :33,
            :34, :35, :36,
            :37, :38, :39,
            :40, :41, :42,
            :43, :44, :45, :46
        )
        """
        
        # 数据处理函数（成功验证的版本）
        def get_numeric_value(value):
            if pd.isna(value) or value == '' or str(value).lower() == 'nan':
                return None
            try:
                return float(value)
            except (ValueError, TypeError):
                return None
        
        def get_string_value(value):
            if pd.isna(value) or value == '' or str(value).lower() == 'nan':
                return None
            return str(value).strip()
        
        def get_date_value(value):
            if pd.isna(value) or value == '' or str(value).lower() == 'nan':
                return None
            try:
                date_str = str(value).strip()
                if '/' in date_str and len(date_str.split('/')) == 3:
                    parts = date_str.split('/')
                    year, month, day = parts[0], parts[1].zfill(2), parts[2].zfill(2)
                    # 返回Python datetime对象，让cx_Oracle自动转换
                    return datetime.strptime(f"{year}-{month}-{day}", "%Y-%m-%d").date()
                elif '-' in date_str:
                    return datetime.strptime(date_str, "%Y-%m-%d").date()
                return None
            except:
                return None
        
        def get_datetime_value(value):
            if pd.isna(value) or value == '' or str(value).lower() == 'nan':
                return None
            try:
                datetime_str = str(value).strip()
                if '/' in datetime_str:
                    if ' ' in datetime_str:
                        date_part, time_part = datetime_str.split(' ', 1)
                        parts = date_part.split('/')
                        if len(parts) == 3:
                            year, month, day = parts[0], parts[1].zfill(2), parts[2].zfill(2)
                            full_datetime = f"{year}-{month}-{day} {time_part}"
                            return datetime.strptime(full_datetime, "%Y-%m-%d %H:%M:%S")
                    else:
                        parts = datetime_str.split('/')
                        if len(parts) == 3:
                            year, month, day = parts[0], parts[1].zfill(2), parts[2].zfill(2)
                            return datetime.strptime(f"{year}-{month}-{day}", "%Y-%m-%d")
                elif '-' in datetime_str:
                    if ' ' not in datetime_str:
                        return datetime.strptime(datetime_str, "%Y-%m-%d")
                    return datetime.strptime(datetime_str, "%Y-%m-%d %H:%M:%S")
                return None
            except:
                return None
        
        # 批量插入数据（提高效率）
        batch_size = 100
        total_rows = len(df)
        inserted_rows = 0
        
        for start_idx in range(0, total_rows, batch_size):
            end_idx = min(start_idx + batch_size, total_rows)
            batch_df = df.iloc[start_idx:end_idx]
            
            batch_data = []
            for index, row in batch_df.iterrows():
                try:
                    # 构建数据行（46个参数，成功验证的顺序）
                    data_row = [
                        get_date_value(row['STAT_DATE']),
                        get_string_value(row['STAT_TYPE']),
                        get_string_value(row['STAT_NAME']),
                        get_string_value(row['STAT_CODE']),
                        get_string_value(row['STAT_CONTENT_RAW']),
                        get_string_value(row['STAT_CONTENT_CLEANSE']),
                        get_numeric_value(row['ACC_A_CNY_AMOUNT']),
                        get_numeric_value(row['ACC_A_CNY_YOY']),
                        get_numeric_value(row['ACC_A_USD_AMOUNT']),
                        get_numeric_value(row['ACC_A_USD_YOY']),
                        get_numeric_value(row['MON_A_CNY_AMOUNT']),
                        get_numeric_value(row['MON_A_CNY_YOY']),
                        get_numeric_value(row['MON_A_USD_AMOUNT']),
                        get_numeric_value(row['MON_A_USD_YOY']),
                        get_numeric_value(row['ACC_E_CNY_AMOUNT']),
                        get_numeric_value(row['ACC_E_CNY_YOY']),
                        get_numeric_value(row['ACC_E_USD_AMOUNT']),
                        get_numeric_value(row['ACC_E_USD_YOY']),
                        get_numeric_value(row['MON_E_CNY_AMOUNT']),
                        get_numeric_value(row['MON_E_CNY_YOY']),
                        get_numeric_value(row['MON_E_USD_AMOUNT']),
                        get_numeric_value(row['MON_E_USD_YOY']),
                        get_numeric_value(row['ACC_I_CNY_AMOUNT']),
                        get_numeric_value(row['ACC_I_CNY_YOY']),
                        get_numeric_value(row['ACC_I_USD_AMOUNT']),
                        get_numeric_value(row['ACC_I_USD_YOY']),
                        get_numeric_value(row['MON_I_CNY_AMOUNT']),
                        get_numeric_value(row['MON_I_CNY_YOY']),
                        get_numeric_value(row['MON_I_USD_AMOUNT']),
                        get_numeric_value(row['MON_I_USD_YOY']),
                        get_numeric_value(row['ACC_E_AMOUNT']),
                        get_string_value(row['ACC_E_AMOUNT_UNIT']),
                        get_numeric_value(row['ACC_E_AMOUNT_YOY']),
                        get_numeric_value(row['MON_E_AMOUNT']),
                        get_string_value(row['MON_E_AMOUNT_UNIT']),
                        get_numeric_value(row['MON_E_AMOUNT_YOY']),
                        get_numeric_value(row['ACC_I_AMOUNT']),
                        get_string_value(row['ACC_I_AMOUNT_UNIT']),
                        get_numeric_value(row['ACC_I_AMOUNT_YOY']),
                        get_numeric_value(row['MON_I_AMOUNT']),
                        get_string_value(row['MON_I_AMOUNT_UNIT']),
                        get_numeric_value(row['MON_I_AMOUNT_YOY']),
                        get_string_value(row['RANK_MARKERS']),
                        get_string_value(row['DATA_SOURCE']),
                        get_string_value(row['EMPHASIS_OR_EMERGING_MARK']),
                        get_datetime_value(row['CREATE_TIME'])
                    ]
                    
                    batch_data.append(data_row)
                    
                except Exception as e:
                    print(f"  ⚠️  构建第 {index+1} 条记录时出错: {e}")
                    continue
            
            # 执行批量插入
            if batch_data:
                cursor.executemany(insert_sql, batch_data)
                conn.commit()
                inserted_rows += len(batch_data)
                
                progress = (inserted_rows / total_rows) * 100
                print(f"    已插入 {inserted_rows}/{total_rows} 条记录 ({progress:.1f}%)")
        
        # 关闭连接
        cursor.close()
        conn.close()
        
        print(f"  ✅ 完成！插入 {inserted_rows} 条记录")
        return True, inserted_rows
        
    except cx_Oracle.DatabaseError as e:
        print(f"  ❌ 数据库错误: {e}")
        return False, 0
    except Exception as e:
        print(f"  ❌ 错误: {e}")
        return False, 0
    finally:
        try:
            if 'conn' in locals():
                conn.close()
        except:
            pass

def batch_import_beijing_to_oracle(csv_directory, table_name, db_config, clear_existing=False):
    """
    批量导入所有北京CSV文件到Oracle数据库
    """
    try:
        print(f"=== 北京海关数据完整批量入库 ===")
        print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"目录: {csv_directory}")
        print(f"目标表: {table_name}")
        print(f"数据库: {db_config['dsn']}")
        print()

        # 获取所有北京CSV文件
        csv_files = []
        for file in os.listdir(csv_directory):
            if file.endswith('.csv') and 'BEIJING' in file:
                csv_files.append(os.path.join(csv_directory, file))

        if not csv_files:
            print(f"❌ 在目录 {csv_directory} 中没有找到北京CSV文件")
            return

        print(f"找到 {len(csv_files)} 个北京CSV文件")
        
        # 可选：清空北京现有数据
        if clear_existing:
            try:
                print("正在清空北京现有数据...")
                conn = cx_Oracle.connect(**db_config)
                cursor = conn.cursor()
                cursor.execute("DELETE FROM T_STATISTICAL_CUS_TOTAL_CS WHERE DATA_SOURCE = '02'")
                deleted_count = cursor.rowcount
                conn.commit()
                cursor.close()
                conn.close()
                print(f"已清空北京现有数据：{deleted_count} 条记录")
            except Exception as e:
                print(f"清空北京数据时出错: {e}")
                return

        # 统计信息
        total_files = 0
        successful_files = 0
        total_records = 0
        failed_files = []

        # 按文件名排序处理
        print(f"\n开始批量导入...")
        print("=" * 80)
        
        for csv_file in sorted(csv_files):
            total_files += 1
            
            print(f"\n[{total_files}/{len(csv_files)}] {os.path.basename(csv_file)}")
            print("-" * 60)
            
            try:
                success, record_count = import_single_csv_to_oracle(csv_file, table_name, db_config)
                if success:
                    successful_files += 1
                    total_records += record_count
                    print(f"✅ 成功导入 {record_count} 条记录")
                else:
                    failed_files.append(os.path.basename(csv_file))
                    print(f"❌ 导入失败")
            except Exception as e:
                print(f"❌ 导入出错: {e}")
                failed_files.append(os.path.basename(csv_file))

        # 最终统计
        print("\n" + "=" * 80)
        print("=== 批量导入完成 ===")
        print("=" * 80)
        print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"总文件数: {total_files}")
        print(f"成功导入: {successful_files}")
        print(f"失败文件: {len(failed_files)}")
        print(f"总记录数: {total_records}")
        
        if failed_files:
            print(f"\n失败的文件:")
            for f in failed_files:
                print(f"  - {f}")
        
        # 验证数据
        if successful_files > 0:
            try:
                print(f"\n=== 数据验证 ===")
                conn = cx_Oracle.connect(**db_config)
                cursor = conn.cursor()
                
                # 检查北京数据总记录数
                cursor.execute("SELECT COUNT(*) FROM T_STATISTICAL_CUS_TOTAL_CS WHERE DATA_SOURCE = '02'")
                beijing_count = cursor.fetchone()[0]
                print(f"数据库中北京数据记录数: {beijing_count}")
                
                # 按统计类型分组统计
                cursor.execute("""
                    SELECT STAT_TYPE, COUNT(*) 
                    FROM T_STATISTICAL_CUS_TOTAL_CS 
                    WHERE DATA_SOURCE = '02'
                    GROUP BY STAT_TYPE 
                    ORDER BY STAT_TYPE
                """)
                
                print(f"\n按统计类型分组统计（北京数据）:")
                for row in cursor.fetchall():
                    print(f"  类型 {row[0]}: {row[1]} 条记录")
                
                cursor.close()
                conn.close()
                
            except Exception as e:
                print(f"验证数据时出错: {e}")

        print(f"\n🎉 北京海关数据入库完成！")
        print(f"数据源标识: DATA_SOURCE = '02'")
        print("=" * 80)

        return successful_files, total_records, failed_files

    except Exception as e:
        print(f"批量导入出错: {e}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")
        return 0, 0, []

if __name__ == "__main__":
    # 数据库配置（TEST环境）
    db_config = {
        'user': 'manifest_dcb',
        'password': 'manifest_dcb',
        'dsn': '192.168.1.151/TEST'
    }
    
    table_name = 'T_STATISTICAL_CUS_TOTAL_CS'
    csv_directory = '北京海关数据_完整转换结果'
    
    # 询问是否清空现有北京数据
    clear_existing = False  # 改为True如果需要清空现有数据
    
    # 开始批量导入
    success_count, total_inserted, failed_files = batch_import_beijing_to_oracle(
        csv_directory, 
        table_name, 
        db_config, 
        clear_existing
    )
    
    print(f"\n最终结果:")
    print(f"成功文件: {success_count}")
    print(f"总插入记录: {total_inserted}")
    print(f"失败文件: {len(failed_files)}")