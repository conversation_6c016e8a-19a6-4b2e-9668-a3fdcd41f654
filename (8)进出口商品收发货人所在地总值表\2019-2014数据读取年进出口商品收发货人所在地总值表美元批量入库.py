import pandas as pd
import os
import re
import cx_Oracle
from datetime import datetime
import glob

# 设置pandas显示选项，确保数据完整显示
pd.set_option('display.max_columns', None)
pd.set_option('display.width', 1000)
pd.set_option('display.max_colwidth', None)

# 函数：处理值为"-"的情况
def convert_to_float_or_none(value):
    if pd.isna(value):
        return None
    if isinstance(value, str):
        # 去除千分位逗号
        value = value.replace(',', '')
        if value.strip() == '-':
            return None
    try:
        return float(value)
    except (ValueError, TypeError):
        return None

def process_trade_statistics(file_path, conn, cursor):
    """处理单个贸易统计文件，并将数据插入到数据库"""
    print(f"\n处理文件: {file_path}")
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return False
    
    try:
        # 读取CSV/Excel文件，不需要偏移列，直接读取所有列
        if file_path.endswith('.csv'):
            original_df = pd.read_csv(file_path)
        else:
            # 先尝试读取几行，判断表头结构
            header_sample = pd.read_excel(file_path, nrows=10)
            
            # 检查是否有多级表头
            if any("Unnamed" in str(col) and "_level_" in str(col) for col in header_sample.columns):
                print("检测到多级表头，使用第二行作为列名")
                original_df = pd.read_excel(file_path, header=[1])  # 使用第二行作为列名
            else:
                original_df = pd.read_excel(file_path)
        
        # 提取当前月份信息
        # 从文件名提取年月
        filename = os.path.basename(file_path)
        current_month = ""
        
        # 尝试多种文件名格式匹配
        # 1. 匹配格式: YYYY年MM月 (原始格式)
        year_month_match = re.search(r'(\d{4})年(\d{1,2})月', filename)
        
        # 2. 匹配格式: YYYY_MM月.xlsx (如 2014_6月.xlsx)
        if not year_month_match:
            year_month_match = re.search(r'(\d{4})_(\d{1,2})月', filename)
        
        # 3. 匹配格式: YYYY_（MM）YYYY年MM月... (如 2016_（8）2016年8月...)
        if not year_month_match:
            # 先尝试匹配 YYYY_（MM）
            first_pattern = re.search(r'(\d{4})_（(\d{1,2})）', filename)
            if first_pattern:
                year = first_pattern.group(1)
                month = first_pattern.group(2)
                year_month_match = re.Match(re.compile(''), filename, 0, 0)
                year_month_match.groups = lambda: (year, month)
            else:
                # 再尝试匹配文件名中的年份和月份
                year_match = re.search(r'(\d{4})', filename)
                month_match = re.search(r'(\d{1,2})月', filename)
                if year_match and month_match:
                    year = year_match.group(1)
                    month = month_match.group(1)
                    year_month_match = re.Match(re.compile(''), filename, 0, 0)
                    year_month_match.groups = lambda: (year, month)
        
        if year_month_match:
            year = year_month_match.groups()[0]
            month = year_month_match.groups()[1].zfill(2)
            current_month = f"{year}{month}01"
            print(f"从文件名提取的年月: {current_month}")
        else:
            # 尝试从文件内容的标题行提取
            # 假设标题在前几行中的某一行
            for i in range(min(5, len(original_df))):
                for col in original_df.columns:
                    cell_value = str(original_df.iloc[i, original_df.columns.get_loc(col)])
                    year_month_match = re.search(r'(\d{4})年(\d{1,2})月', cell_value)
                    if year_month_match:
                        year = year_month_match.group(1)
                        month = year_month_match.group(2).zfill(2)
                        current_month = f"{year}{month}01"
                        print(f"从内容提取的年月: {current_month}")
                        break
                if current_month:
                    break
            
            # 如果仍未找到年月信息，使用文件修改时间
            if not current_month:
                print("无法从文件名或内容中提取年月信息")
                file_time = os.path.getmtime(file_path)
                dt = datetime.fromtimestamp(file_time)
                current_month = f"{dt.year}{dt.month:02d}01"
                print(f"使用文件修改时间作为年月: {current_month}")
        
        # 显示原始总行数
        print(f"原始总行数: {len(original_df)}")
        
        # 检查数据表结构，寻找表头行和数据开始行
        # 通常表头行应包含"收发货人所在地"和"进出口"等关键词
        header_row_idx = -1
        data_start_idx = -1
        
        # 查找表头行
        for i in range(min(20, len(original_df))):
            row_values = [str(val).strip() if not pd.isna(val) else "" for val in original_df.iloc[i].values]
            row_text = " ".join(row_values).lower()
            if "所在地" in row_text and ("进出口" in row_text or "出口" in row_text or "进口" in row_text):
                header_row_idx = i
                data_start_idx = i + 1
                break
        
        if header_row_idx == -1:
            print("无法找到表头行，尝试查找'总值'行作为数据起始点")
            # 查找以"总值"开头的行
            for i, row in original_df.iterrows():
                first_val = str(row.iloc[0]).strip() if not pd.isna(row.iloc[0]) else ""
                if first_val == "总值":
                    data_start_idx = i
                    break
        
        if data_start_idx != -1:
            print(f"找到数据起始行，索引为: {data_start_idx}")
            
            # 如果找到了表头行，使用它作为列名，否则使用默认列名
            if header_row_idx != -1:
                headers = [str(val).strip() if not pd.isna(val) else f"列{i}" for i, val in enumerate(original_df.iloc[header_row_idx])]
                df = original_df.iloc[data_start_idx:].reset_index(drop=True)
                df.columns = headers
            else:
                # 如果没有找到表头，使用默认列名
                df = original_df.iloc[data_start_idx:].reset_index(drop=True)
                
                # 检查列数，确保有足够的列
                if len(df.columns) >= 10:
                    df.columns = [
                        "收发货人所在地",
                        "当月进出口金额",
                        "累计进出口金额",
                        "当月出口金额",
                        "累计出口金额",
                        "当月进口金额",
                        "累计进口金额",
                        "累计比去年同期±%进出口",
                        "累计比去年同期±%出口",
                        "累计比去年同期±%进口"
                    ] + [f"额外列{i}" for i in range(len(df.columns) - 10)]
                else:
                    print(f"警告：列数不足({len(df.columns)}列)，可能导致数据错位")
                    df.columns = [
                        "收发货人所在地",
                        "当月进出口金额",
                        "累计进出口金额",
                        "当月出口金额",
                        "累计出口金额",
                        "当月进口金额",
                        "累计进口金额"
                    ] + [f"额外列{i}" for i in range(len(df.columns) - 7)]
            
            # 添加当前月份列
            df['当前月份'] = current_month
            
            print(f"处理后的数据框行数: {len(df)}")
            
            # 确保有"收发货人所在地"列
            location_col = None
            for col in df.columns:
                if isinstance(col, str) and "所在地" in col:
                    location_col = col
                    break
            
            if not location_col:
                location_col = df.columns[0]
                print(f"未找到包含'所在地'的列名，使用第一列 '{location_col}' 作为所在地列")
            
            # 删除重复记录和空白行
            df = df.dropna(subset=[location_col])
            df = df[~df[location_col].astype(str).str.contains("总值汇总", na=False)]
            
            # 准备插入数据
            try:
                # 修改列映射逻辑，避免使用next()和lambda，这些可能导致Series的布尔评估问题
                col_mapping = {}
                
                # 先尝试通过关键词匹配列名
                col_mapping['收发货人所在地'] = location_col
                
                # 初始化其他映射为None
                expected_columns = [
                    "当月进出口金额", "累计进出口金额", "当月出口金额", "累计出口金额", 
                    "当月进口金额", "累计进口金额", "累计比去年同期±%进出口", 
                    "累计比去年同期±%出口", "累计比去年同期±%进口"
                ]
                
                for expected_col in expected_columns:
                    col_mapping[expected_col] = None
                
                # 使用更安全的方式遍历列名查找匹配
                for col in df.columns:
                    if not isinstance(col, str):
                        continue
                        
                    col_lower = col.lower()
                    if "当月" in col_lower and "进出口" in col_lower:
                        col_mapping["当月进出口金额"] = col
                    elif "累计" in col_lower and "进出口" in col_lower and "±" not in col_lower and "同期" not in col_lower:
                        col_mapping["累计进出口金额"] = col
                    elif "当月" in col_lower and "出口" in col_lower and "进出口" not in col_lower:
                        col_mapping["当月出口金额"] = col
                    elif "累计" in col_lower and "出口" in col_lower and "进出口" not in col_lower and "±" not in col_lower and "同期" not in col_lower:
                        col_mapping["累计出口金额"] = col
                    elif "当月" in col_lower and "进口" in col_lower:
                        col_mapping["当月进口金额"] = col
                    elif "累计" in col_lower and "进口" in col_lower and "±" not in col_lower and "同期" not in col_lower:
                        col_mapping["累计进口金额"] = col
                    elif "同期" in col_lower and "进出口" in col_lower:
                        col_mapping["累计比去年同期±%进出口"] = col
                    elif "同期" in col_lower and "出口" in col_lower and "进出口" not in col_lower:
                        col_mapping["累计比去年同期±%出口"] = col
                    elif "同期" in col_lower and "进口" in col_lower:
                        col_mapping["累计比去年同期±%进口"] = col
                
                # 检查列匹配结果 - 月份特殊字符匹配
                month_col_keywords = [f"{int(month)}月", f"{int(month)}至{int(month)}月"]
                
                # 如果无法通过名称找到列，尝试使用索引位置和月份名称（假设列的顺序大致相同）
                columns_to_check = ["当月进出口金额", "累计进出口金额", "当月出口金额", "累计出口金额", 
                                  "当月进口金额", "累计进口金额", "累计比去年同期±%进出口", 
                                  "累计比去年同期±%出口", "累计比去年同期±%进口"]
                
                # 使用位置映射作为备选
                for i, col_name in enumerate(columns_to_check):
                    if i + 1 < len(df.columns) and col_mapping[col_name] is None:
                        col_mapping[col_name] = df.columns[i + 1]  # +1 因为第一列通常是所在地
                        print(f"使用位置映射: {col_name} -> {df.columns[i + 1]}")
                
                # 准备数据
                data_to_insert = []
                
                for idx, row in df.iterrows():
                    # 跳过空行或不适合插入的行
                    if pd.isna(row[location_col]) or row[location_col] == '':
                        continue
                    
                    location = str(row[location_col]).strip() if not pd.isna(row[location_col]) else None
                    
                    # 如果location包含"注："或看起来像是注释，则跳过这行
                    if location and (location.startswith("注") or "注：" in location):
                        continue
                    
                    # 安全地获取每个字段的值
                    month_import_export = None
                    month_export = None
                    month_import = None
                    ytd_import_export = None
                    ytd_export = None
                    ytd_import = None
                    yoy_import_export = None
                    yoy_export = None
                    yoy_import = None
                    
                    # 逐个获取值，避免Series布尔评估问题
                    if col_mapping["当月进出口金额"]:
                        month_import_export = convert_to_float_or_none(row[col_mapping["当月进出口金额"]])
                    
                    if col_mapping["当月出口金额"]:
                        month_export = convert_to_float_or_none(row[col_mapping["当月出口金额"]])
                    
                    if col_mapping["当月进口金额"]:
                        month_import = convert_to_float_or_none(row[col_mapping["当月进口金额"]])
                    
                    if col_mapping["累计进出口金额"]:
                        ytd_import_export = convert_to_float_or_none(row[col_mapping["累计进出口金额"]])
                    
                    if col_mapping["累计出口金额"]:
                        ytd_export = convert_to_float_or_none(row[col_mapping["累计出口金额"]])
                    
                    if col_mapping["累计进口金额"]:
                        ytd_import = convert_to_float_or_none(row[col_mapping["累计进口金额"]])
                    
                    if col_mapping["累计比去年同期±%进出口"]:
                        yoy_import_export = convert_to_float_or_none(row[col_mapping["累计比去年同期±%进出口"]])
                    
                    if col_mapping["累计比去年同期±%出口"]:
                        yoy_export = convert_to_float_or_none(row[col_mapping["累计比去年同期±%出口"]])
                    
                    if col_mapping["累计比去年同期±%进口"]:
                        yoy_import = convert_to_float_or_none(row[col_mapping["累计比去年同期±%进口"]])
                    
                    # 日期格式
                    month_date = current_month
                    
                    data_to_insert.append((
                        location, 
                        month_import_export, 
                        month_export, 
                        month_import, 
                        ytd_import_export, 
                        ytd_export, 
                        ytd_import, 
                        yoy_import_export, 
                        yoy_export, 
                        yoy_import, 
                        month_date
                    ))
                
                # 执行批量插入
                if data_to_insert:
                    insert_query = """
                    INSERT INTO TEMP_TRADE_STATISTICS_USA (
                        LOCATION, 
                        MONTH_IMPORT_EXPORT, 
                        MONTH_EXPORT, 
                        MONTH_IMPORT, 
                        YTD_IMPORT_EXPORT, 
                        YTD_EXPORT, 
                        YTD_IMPORT, 
                        YOY_IMPORT_EXPORT, 
                        YOY_EXPORT, 
                        YOY_IMPORT, 
                        CURRENT_MONTH
                    ) VALUES (
                        :1, :2, :3, :4, :5, :6, :7, :8, :9, :10, TO_DATE(:11, 'YYYYMMDD')
                    )
                    """
                    
                    print(f"准备插入 {len(data_to_insert)} 条数据...")
                    cursor.executemany(insert_query, data_to_insert)
                    conn.commit()
                    insert_count = len(data_to_insert)
                    print(f"成功插入 {insert_count} 条数据到Oracle数据库")
                    
                    return insert_count
                else:
                    print("没有有效数据可插入")
                    return 0
            except cx_Oracle.Error as error:
                print(f"Oracle数据库错误: {error}")
                return 0
            except Exception as e:
                print(f"插入数据时发生错误: {e}")
                import traceback
                traceback.print_exc()
                return 0
        else:
            print("未找到'总值'行或有效的表头行")
            return 0
        
    except Exception as e:
        print(f"读取文件时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 0

def batch_process_files(directory_path):
    """批量处理目录下的所有数据文件"""
    print(f"开始处理目录: {directory_path}")
    
    # 验证目录是否存在
    if not os.path.exists(directory_path):
        print(f"错误: 目录不存在 - {directory_path}")
        return
    
    # 查找所有Excel和CSV文件
    xls_files = glob.glob(os.path.join(directory_path, '**', '*.xls'), recursive=True)
    xlsx_files = glob.glob(os.path.join(directory_path, '**', '*.xlsx'), recursive=True)
    csv_files = glob.glob(os.path.join(directory_path, '**', '*.csv'), recursive=True)
    all_files = xls_files + xlsx_files + csv_files
    
    if not all_files:
        print(f"未在 {directory_path} 目录下找到任何Excel或CSV文件")
        return
    
    print(f"找到 {len(all_files)} 个数据文件")
    
    try:
        # 连接Oracle数据库
        print("\n正在连接到Oracle数据库...")
        conn = cx_Oracle.connect("manifest_dcb/manifest_dcb@192.168.1.151/TEST")
        cursor = conn.cursor()
        print("数据库连接成功")
        
        # 处理文件并记录结果
        total_files = len(all_files)
        success_files = 0
        total_records = 0
        
        for i, file_path in enumerate(all_files, 1):
            print(f"\n处理文件 [{i}/{total_files}]: {file_path}")
            records_inserted = process_trade_statistics(file_path, conn, cursor)
            
            if records_inserted > 0:
                success_files += 1
                total_records += records_inserted
        
        # 输出总结
        print("\n==== 处理完成 ====")
        print(f"共处理: {total_files} 文件")
        print(f"成功处理: {success_files} 文件")
        print(f"失败处理: {total_files - success_files} 文件")
        print(f"成功插入: {total_records} 条记录")
        
        # 关闭数据库连接
        cursor.close()
        conn.close()
        print("数据库连接已关闭")
        
    except cx_Oracle.Error as error:
        print(f"Oracle数据库连接错误: {error}")
    except Exception as e:
        print(f"发生错误: {e}")

if __name__ == "__main__":
    while True:
        print("\n=== 2019-2014年进出口商品收发货人所在地总值表人民币批量入库工具 ===")
        print("请输入要处理的目录路径(包含数据文件的目录)，或输入exit退出:")
        directory_path = input("目录路径: ").strip()
        
        if directory_path.lower() == 'exit':
            print("程序结束，再见!")
            break
        
        if os.path.exists(directory_path):
            batch_process_files(directory_path)
        else:
            print(f"错误: 目录不存在 - {directory_path}") 