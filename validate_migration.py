#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
海关统计数据迁移验证脚本
验证迁移到统一大表的数据完整性和准确性
"""

import cx_Oracle
import pandas as pd
import logging
from datetime import datetime
import sys

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'validation_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class MigrationValidator:
    def __init__(self, connection_string):
        """初始化验证器"""
        self.connection_string = connection_string
        self.conn = None
        self.validation_results = []
        
    def connect(self):
        """建立数据库连接"""
        try:
            self.conn = cx_Oracle.connect(self.connection_string)
            logger.info("数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            return False
    
    def close(self):
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()
            logger.info("数据库连接已关闭")
    
    def validate_record_counts(self):
        """验证记录数量"""
        logger.info("开始验证记录数量")
        
        # 定义源表和对应的统计类型
        table_mappings = [
            ('temp_cus_mon_1', '01', '总值统计'),
            ('temp_cus_mon_2', '02', '国别地区'),
            ('temp_cus_mon_3', '03', '商品构成'),
            ('temp_cus_mon_4', '04', '商品类章'),
            ('temp_cus_mon_5', '05', '贸易方式'),
            ('CUS_TRADE_MAJOR_PRODUCT_MON', '12', '主要商品量值'),
            ('CUS_TRADE_COUNTRY_PRODUCT_MON', '13', '国家×商品'),
        ]
        
        cursor = self.conn.cursor()
        validation_passed = True
        
        for source_table, stat_type, stat_name in table_mappings:
            try:
                # 查询源表记录数
                cursor.execute(f"SELECT COUNT(*) FROM {source_table}")
                source_count = cursor.fetchone()[0]
                
                # 查询统一表中对应记录数
                cursor.execute(
                    "SELECT COUNT(*) FROM CUS_TRADE_UNIFIED_STATISTICS WHERE STAT_TYPE = :1",
                    (stat_type,)
                )
                unified_count = cursor.fetchone()[0]
                
                # 记录验证结果
                result = {
                    'table': source_table,
                    'stat_type': stat_type,
                    'stat_name': stat_name,
                    'source_count': source_count,
                    'unified_count': unified_count,
                    'match': source_count == unified_count
                }
                
                self.validation_results.append(result)
                
                if result['match']:
                    logger.info(f"✓ {stat_name}: 源表 {source_count} = 统一表 {unified_count}")
                else:
                    logger.warning(f"✗ {stat_name}: 源表 {source_count} ≠ 统一表 {unified_count}")
                    validation_passed = False
                    
            except Exception as e:
                logger.error(f"验证表 {source_table} 时发生错误: {e}")
                validation_passed = False
        
        cursor.close()
        return validation_passed
    
    def validate_data_integrity(self):
        """验证数据完整性"""
        logger.info("开始验证数据完整性")
        
        cursor = self.conn.cursor()
        integrity_checks = []
        
        # 检查必填字段
        required_fields = ['STAT_DATE', 'STAT_TYPE', 'CURRENCY_TYPE']
        for field in required_fields:
            cursor.execute(f"""
                SELECT COUNT(*) FROM CUS_TRADE_UNIFIED_STATISTICS 
                WHERE {field} IS NULL
            """)
            null_count = cursor.fetchone()[0]
            
            check_result = {
                'check': f'{field} 非空检查',
                'null_count': null_count,
                'passed': null_count == 0
            }
            integrity_checks.append(check_result)
            
            if check_result['passed']:
                logger.info(f"✓ {field} 字段无空值")
            else:
                logger.warning(f"✗ {field} 字段有 {null_count} 个空值")
        
        # 检查日期范围
        cursor.execute("""
            SELECT MIN(STAT_DATE), MAX(STAT_DATE), COUNT(DISTINCT STAT_DATE)
            FROM CUS_TRADE_UNIFIED_STATISTICS
        """)
        min_date, max_date, date_count = cursor.fetchone()
        
        logger.info(f"数据日期范围: {min_date} 到 {max_date}, 共 {date_count} 个不同日期")
        
        # 检查统计类型分布
        cursor.execute("""
            SELECT STAT_TYPE, STAT_TYPE_NAME, COUNT(*) as RECORD_COUNT
            FROM CUS_TRADE_UNIFIED_STATISTICS
            GROUP BY STAT_TYPE, STAT_TYPE_NAME
            ORDER BY STAT_TYPE
        """)
        
        logger.info("统计类型分布:")
        for stat_type, stat_name, count in cursor.fetchall():
            logger.info(f"  {stat_type} - {stat_name}: {count:,} 条记录")
        
        cursor.close()
        return all(check['passed'] for check in integrity_checks)
    
    def validate_sample_data(self):
        """验证样本数据准确性"""
        logger.info("开始验证样本数据准确性")
        
        cursor = self.conn.cursor()
        
        # 验证表(1)的样本数据
        try:
            # 从源表获取样本
            cursor.execute("""
                SELECT CURRENT_MONTH, CURRENCY_TYPE, VAL_MONTH_IE, VAL_MONTH_EXP, VAL_MONTH_IMP
                FROM temp_cus_mon_1
                WHERE ROWNUM <= 5
                ORDER BY CURRENT_MONTH DESC
            """)
            source_samples = cursor.fetchall()
            
            # 从统一表获取对应样本
            for sample in source_samples:
                stat_date, currency_type, month_ie, month_exp, month_imp = sample
                
                cursor.execute("""
                    SELECT MONTH_IE_AMOUNT, MONTH_EXP_AMOUNT, MONTH_IMP_AMOUNT
                    FROM CUS_TRADE_UNIFIED_STATISTICS
                    WHERE STAT_DATE = :1 AND CURRENCY_TYPE = :2 AND STAT_TYPE = '01'
                """, (stat_date, currency_type))
                
                unified_sample = cursor.fetchone()
                
                if unified_sample:
                    u_ie, u_exp, u_imp = unified_sample
                    
                    # 比较数据（允许小的浮点误差）
                    tolerance = 0.01
                    ie_match = abs((month_ie or 0) - (u_ie or 0)) < tolerance
                    exp_match = abs((month_exp or 0) - (u_exp or 0)) < tolerance
                    imp_match = abs((month_imp or 0) - (u_imp or 0)) < tolerance
                    
                    if ie_match and exp_match and imp_match:
                        logger.info(f"✓ 样本数据匹配: {stat_date} {currency_type}")
                    else:
                        logger.warning(f"✗ 样本数据不匹配: {stat_date} {currency_type}")
                        logger.warning(f"  源表: IE={month_ie}, EXP={month_exp}, IMP={month_imp}")
                        logger.warning(f"  统一表: IE={u_ie}, EXP={u_exp}, IMP={u_imp}")
                else:
                    logger.warning(f"✗ 统一表中未找到对应数据: {stat_date} {currency_type}")
                    
        except Exception as e:
            logger.error(f"验证样本数据时发生错误: {e}")
            return False
        
        cursor.close()
        return True
    
    def generate_validation_report(self):
        """生成验证报告"""
        logger.info("生成验证报告")
        
        report_file = f'migration_validation_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.txt'
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("海关统计数据迁移验证报告\n")
            f.write("=" * 50 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("记录数量验证结果:\n")
            f.write("-" * 30 + "\n")
            for result in self.validation_results:
                status = "✓" if result['match'] else "✗"
                f.write(f"{status} {result['stat_name']}: ")
                f.write(f"源表 {result['source_count']:,} ")
                f.write(f"统一表 {result['unified_count']:,}\n")
            
            f.write(f"\n报告文件: {report_file}\n")
        
        logger.info(f"验证报告已生成: {report_file}")
    
    def run_validation(self):
        """执行完整验证"""
        logger.info("开始数据迁移验证")
        
        if not self.connect():
            return False
        
        try:
            # 执行各项验证
            count_valid = self.validate_record_counts()
            integrity_valid = self.validate_data_integrity()
            sample_valid = self.validate_sample_data()
            
            # 生成报告
            self.generate_validation_report()
            
            # 总体验证结果
            overall_valid = count_valid and integrity_valid and sample_valid
            
            if overall_valid:
                logger.info("✓ 数据迁移验证通过")
            else:
                logger.warning("✗ 数据迁移验证发现问题")
            
            return overall_valid
            
        except Exception as e:
            logger.error(f"验证过程中发生错误: {e}")
            return False
        finally:
            self.close()

def main():
    """主函数"""
    # 数据库连接字符串
    connection_string = "manifest_dcb/manifest_dcb@192.168.1.151/TEST"
    
    # 创建验证器并执行验证
    validator = MigrationValidator(connection_string)
    success = validator.run_validation()
    
    if success:
        logger.info("数据验证成功完成")
        sys.exit(0)
    else:
        logger.error("数据验证发现问题")
        sys.exit(1)

if __name__ == "__main__":
    main()
