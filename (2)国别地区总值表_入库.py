import pandas as pd
import os
import re
import cx_Oracle

# 设置pandas显示选项，确保数据完整显示
pd.set_option('display.max_columns', None)
pd.set_option('display.width', 1000)
pd.set_option('display.max_colwidth', None)

# 函数：处理值为"-"的情况
def convert_to_float_or_none(value):
    if pd.isna(value):
        return None
    if isinstance(value, str) and value.strip() == '-':
        return None
    try:
        return float(value)
    except (ValueError, TypeError):
        return None

def process_trade_statistics(file_path):
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return False
    
    try:
        # 从文件名中提取货币类型
        currency_type = "人民币" if "人民币" in file_path else "美元"

        # 1. 动态查找单位
        df_raw_for_unit = pd.read_excel(file_path, header=None, nrows=5)
        unit = "不明"
        for i in range(len(df_raw_for_unit)):
            row_text = ' '.join(str(s) for s in df_raw_for_unit.iloc[i].tolist() if pd.notna(s))
            unit_match = re.search(r'单位[：:]\s*([^\s]+)', row_text)
            if unit_match:
                unit = unit_match.group(1).strip()
                break
        
        # 如果动态查找失败，则使用基于货币类型的默认值
        if unit == "不明":
            unit = "万元" if currency_type == "人民币" else "千美元"
        
        print(f"提取的货币类型: {currency_type}, 单位: {unit}")

        # 读取XLS文件，假设数据在B到K列（索引1-10）
        original_df = pd.read_excel(file_path, usecols=range(1, 11))
        
        # 提取当前月份信息
        title_df = pd.read_excel(file_path, usecols=[1], nrows=1)
        title_text = title_df.iloc[0, 0] if not title_df.empty and not title_df.iloc[0, 0] is None else ""
        
        print(f"标题文本: {title_text}")
        
        # 从标题中提取年月信息
        current_month = ""
        if isinstance(title_text, str):
            year_month_match = re.search(r'(\d{4})年(\d{1,2})月', title_text)
            if year_month_match:
                year = year_month_match.group(1)
                month = year_month_match.group(2).zfill(2)
                current_month = f"{year}{month}01"  # 设置为当月第一天
                print(f"提取的年月: {current_month}")
            else:
                print("无法从标题中提取年月信息")
        else:
            print("标题不是文本格式")
        
        # 显示原始总行数
        print(f"原始总行数: {len(original_df)}")
        
        # 查找以"总值"开头的行
        first_col = original_df.columns[0]
        total_value_idx = -1
        
        for i, row in original_df.iterrows():
            if isinstance(row[first_col], str) and row[first_col].strip() == "总值":
                total_value_idx = i
                break
        
        if total_value_idx != -1:
            print(f"找到'总值'行，索引为: {total_value_idx}")
            
            # 创建新的数据框，从'总值'行开始
            df = original_df.iloc[total_value_idx:].reset_index(drop=True)
            
            # 检查最后一行的第一个值是否以"注"开头
            last_row = df.iloc[-1]
            
            if isinstance(last_row[first_col], str) and last_row[first_col].strip().startswith("注"):
                print(f"删除最后一行，因为它以'注'开头: {last_row[first_col]}")
                df = df.iloc[:-1]
            
            # 设置列名 - 这是为(2)号表定制的列名
            df.columns = [
                "国别(地区)",
                "当月进出口金额",
                "累计进出口金额",
                "当月出口金额",
                "累计出口金额",
                "当月进口金额",
                "累计进口金额",
                "累计比去年同期±%进出口",
                "累计比去年同期±%出口",
                "累计比去年同期±%进口"
            ]
            
            # 添加当前月份列
            df['当前月份'] = current_month
            
            print(f"\n处理后的数据框行数: {len(df)}")
            
            # 不再保存到临时文件
            # output_file = "处理后的国别(地区)总值表.xlsx"
            # df.to_excel(output_file, index=False)
            # print(f"\n已保存处理后的数据到文件: {output_file}")
            
            # 将数据写入Oracle数据库
            try:
                # 连接到Oracle数据库
                print("\n正在连接到Oracle数据库...")
                conn = cx_Oracle.connect("manifest_dcb/manifest_dcb@192.168.1.151/TEST")
                cursor = conn.cursor()
                print("数据库连接成功")
                
                # 准备插入数据 - 目标表更新为 temp_cus_mon_2，并增加UNIT字段
                insert_query = """
                INSERT INTO temp_cus_mon_2 (
                    LOCATION, 
                    CURRENT_MONTH,
                    CURRENCY_TYPE,
                    UNIT,
                    MONTH_IMPORT_EXPORT, 
                    MONTH_EXPORT, 
                    MONTH_IMPORT, 
                    YTD_IMPORT_EXPORT, 
                    YTD_EXPORT, 
                    YTD_IMPORT, 
                    YOY_IMPORT_EXPORT, 
                    YOY_EXPORT, 
                    YOY_IMPORT
                ) VALUES (
                    :1, TO_DATE(:2, 'YYYYMMDD'), :3, :4, :5, :6, :7, :8, :9, :10, :11, :12, :13
                )
                """
                
                # 准备数据
                data_to_insert = []
                for _, row in df.iterrows():
                    if pd.isna(row['国别(地区)']) or row['国别(地区)'] == '':
                        continue
                    
                    location = str(row['国别(地区)']) if not pd.isna(row['国别(地区)']) else None
                    month_import_export = convert_to_float_or_none(row['当月进出口金额'])
                    month_export = convert_to_float_or_none(row['当月出口金额'])
                    month_import = convert_to_float_or_none(row['当月进口金额'])
                    ytd_import_export = convert_to_float_or_none(row['累计进出口金额'])
                    ytd_export = convert_to_float_or_none(row['累计出口金额'])
                    ytd_import = convert_to_float_or_none(row['累计进口金额'])
                    yoy_import_export = convert_to_float_or_none(row['累计比去年同期±%进出口'])
                    yoy_export = convert_to_float_or_none(row['累计比去年同期±%出口'])
                    yoy_import = convert_to_float_or_none(row['累计比去年同期±%进口'])
                    
                    current_month = str(row['当前月份']) if not pd.isna(row['当前月份']) else None
                    
                    data_to_insert.append((
                        location, 
                        current_month,
                        currency_type,
                        unit, # 添加单位
                        month_import_export, 
                        month_export, 
                        month_import, 
                        ytd_import_export, 
                        ytd_export, 
                        ytd_import, 
                        yoy_import_export, 
                        yoy_export, 
                        yoy_import
                    ))
                
                # 执行批量插入
                print(f"准备插入 {len(data_to_insert)} 条数据到 temp_cus_mon_2 表...")
                cursor.executemany(insert_query, data_to_insert)
                conn.commit()
                print(f"成功插入 {cursor.rowcount} 条数据到Oracle数据库")
                
                # 关闭连接
                cursor.close()
                conn.close()
                print("数据库连接已关闭")
                return True
                
            except cx_Oracle.Error as error:
                print(f"Oracle数据库错误: {error}")
                return False
            except Exception as e:
                print(f"插入数据时发生错误: {e}")
                return False
            
        else:
            print("未找到'总值'行")
            return False
        
    except Exception as e:
        print(f"读取文件时发生错误: {e}")
        return False

def check_if_data_exists(filename, cursor):
    """
    根据文件名检查特定月份和货币类型的数据是否已存在于数据库中。
    使用“中国”作为检查标记。
    """
    # 从文件名中提取年份、月份和货币类型
    year_month_match = re.search(r'(\d{4})年(\d{1,2})月', filename)
    if not year_month_match:
        return False # 文件名格式不符，无法检查

    year = year_month_match.group(1)
    month = year_month_match.group(2).zfill(2)
    current_month = f"{year}{month}01"
    
    currency_type = "未知"
    if "人民币" in filename:
        currency_type = "人民币"
    elif "美元" in filename:
        currency_type = "美元"

    if currency_type == "未知":
        return False # 无法确定货币类型

    # 查询数据库
    query = "SELECT COUNT(*) FROM temp_cus_mon_2 WHERE CURRENT_MONTH = TO_DATE(:1, 'YYYYMMDD') AND CURRENCY_TYPE = :2 AND LOCATION = '中国'"
    cursor.execute(query, (current_month, currency_type))
    count = cursor.fetchone()[0]
    
    return count > 0

def batch_process_directory(directory_path, cursor):
    """
    遍历指定目录下的所有.xlsx文件，并对每个文件执行增量入库操作。
    """
    print(f"\n--- 开始扫描目录: {directory_path} ---")
    if not os.path.isdir(directory_path):
        print(f"错误: 目录不存在 - {directory_path}")
        return

    for filename in os.listdir(directory_path):
        if filename.endswith(".xlsx"):
            # 在处理文件前，先检查数据是否已存在
            if check_if_data_exists(filename, cursor):
                print(f"数据已存在，跳过文件: {filename}")
                continue

            file_path = os.path.join(directory_path, filename)
            print(f"\n>>> 正在处理新文件: {file_path}")
            process_trade_statistics(file_path)

    print(f"\n--- 目录扫描完成: {directory_path} ---")

# 示例使用
if __name__ == "__main__":
    # 使用os.getcwd()获取当前工作目录，而不是硬编码
    base_dir = os.getcwd()
    
    rmb_dir = os.path.join(base_dir, "进出口商品统计表_人民币值", "(2)进出口商品国别(地区)总值表_人民币值")
    usd_dir = os.path.join(base_dir, "进出口商品统计表_美元值", "(2)进出口商品国别(地区)总值表_美元值")

    conn = None
    try:
        # 建立一个全局数据库连接
        print("正在连接到Oracle数据库以进行批量处理...")
        conn = cx_Oracle.connect("manifest_dcb/manifest_dcb@192.168.1.151/TEST")
        cursor = conn.cursor()
        print("数据库连接成功。")

        # 分别处理人民币和美元数据目录
        batch_process_directory(rmb_dir, cursor)
        batch_process_directory(usd_dir, cursor) # 如果需要处理美元数据，取消此行注释
        
        cursor.close()

    except cx_Oracle.Error as error:
        print(f"Oracle数据库操作期间发生错误: {error}")
    except Exception as e:
        print(f"批量处理期间发生未知错误: {e}")
    finally:
        if conn:
            conn.close()
            print("\n数据库连接已关闭。") 