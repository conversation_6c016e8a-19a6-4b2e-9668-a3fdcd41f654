from DrissionPage import ChromiumPage
import time
import os
import re
import shutil
import pandas as pd
from datetime import datetime

# 定义北京海关要抓取的表格种类配置
BEIJING_TARGET_TABLES_CONFIG = {
    "(1)": {"name": "北京地区进出口商品总值表B：月度表", "pattern": r"北京.*?进出口.*?总值表.*?月度表", "is_monthly": True},
    "(2)": {"name": "北京地区进出口商品国别（地区）总值表", "pattern": r"北京.*?进出口.*?国别.*?总值表", "is_monthly": False},
    "(5)": {"name": "北京地区进出口商品贸易方式总值表", "pattern": r"北京.*?进出口.*?贸易方式.*?总值表", "is_monthly": False},
    "(6)": {"name": "北京地区出口商品贸易方式企业性质总值表", "pattern": r"北京.*?出口.*?贸易方式.*?企业性质.*?总值表", "is_monthly": False},
    "(7)": {"name": "北京地区进口商品贸易方式企业性质总值表", "pattern": r"北京.*?进口.*?贸易方式.*?企业性质.*?总值表", "is_monthly": False},
    "(8)": {"name": "北京地区出口主要商品量值表", "pattern": r"北京.*?出口.*?主要商品.*?量值表", "is_monthly": False},
    "(9)": {"name": "北京地区进口主要商品量值表", "pattern": r"北京.*?进口.*?主要商品.*?量值表", "is_monthly": False}
}

# 定义表格类型对应的目录名称
BEIJING_TABLE_DIR_NAMES = {
    "(1)": "北京地区进出口商品总值表_月度表",
    "(2)": "北京地区进出口商品国别地区总值表",
    "(5)": "北京地区进出口商品贸易方式总值表",
    "(6)": "北京地区出口商品贸易方式企业性质总值表",
    "(7)": "北京地区进口商品贸易方式企业性质总值表",
    "(8)": "北京地区出口主要商品量值表",
    "(9)": "北京地区进口主要商品量值表"
}

def download_beijing_files(all_links, base_download_dir, page, temp_download_dir):
    """下载北京海关文件的函数"""
    download_count = 0
    scrape_count = 0
    error_count = 0

    for link_info in all_links:
        try:
            year = link_info['year']
            month = link_info.get('month', '')
            table_key = link_info['table_key']
            config = BEIJING_TARGET_TABLES_CONFIG[table_key]
            text = link_info['text']
            url = link_info['url']

            # 跳过2024年以前的数据
            if int(year) < 2024:
                print(f"跳过{year}年的数据，只处理2024年及以后的数据")
                continue

            print(f"\n正在处理: {year}年{month} - {text}")
            print(f"访问URL: {url}")

            # 访问详情页
            page.get(url)
            time.sleep(2)  # 等待页面加载

            # 获取真实文件名
            file_name = ""
            news_title_div = page.ele('.easysite-news-title')
            if news_title_div:
                title_element = news_title_div.ele('tag:h2')
                if title_element:
                    file_title = title_element.text.strip()
                    print(f"从h2标签获取真实标题: {file_title}")
                    # 清理文件名（移除不允许的字符）
                    file_title = re.sub(r'[\\/*?:"<>|]', '', file_title)
                    file_name = f"{year}年{month}_{file_title}.xlsx"

            # 如果主要方法失败，使用备用方法
            if not file_name:
                print("未找到h2标题，使用链接文本作为备用")
                clean_text = re.sub(r'[\\/*?:"<>|]', '', text)
                file_name = f"{year}年{month}_{clean_text}.xlsx"

            # 构建完整路径
            table_dir_name = BEIJING_TABLE_DIR_NAMES[table_key]
            download_dir = os.path.join(base_download_dir, table_dir_name)
            if not os.path.exists(download_dir):
                os.makedirs(download_dir)
                print(f"创建目录: {download_dir}")

            file_path = os.path.join(download_dir, file_name)

            # 检查文件是否存在
            if os.path.exists(file_path):
                print(f"文件已存在，跳过: {file_name}")
                continue

            # 如果文件不存在，开始下载流程
            print(f"准备下载文件: {file_name}")

            # 查找"下载"按钮
            download_links = page.eles("下载")

            if not download_links or len(download_links) == 0:
                print("未找到下载按钮，尝试直接提取表格数据...")
                # 查找表格元素
                table_elements = page.eles('tag:table')

                if table_elements and len(table_elements) > 0:
                    print(f"找到{len(table_elements)}个表格元素")

                    # 找到最有可能是目标表格的元素（通常是较大的表格）
                    target_table = None
                    max_rows = 0

                    for i, table in enumerate(table_elements):
                        rows = table.eles('tag:tr')
                        if len(rows) > max_rows:
                            max_rows = len(rows)
                            target_table = table
                        print(f"表格 {i+1}: {len(rows)} 行")

                    if target_table:
                        print(f"选择了具有 {max_rows} 行的表格")

                        # 获取表格HTML
                        html_content = target_table.html

                        try:
                            # 尝试使用pandas解析表格
                            try:
                                # 先尝试双行表头
                                dfs = pd.read_html(html_content, header=[0,1], flavor='bs4')
                                df = dfs[0]
                                # 合并多级表头
                                df.columns = [' '.join(str(col) for col in cols if str(col) != 'nan').strip() for cols in df.columns.values]
                            except Exception:
                                try:
                                    # 尝试单行表头
                                    dfs = pd.read_html(html_content, header=0, flavor='bs4')
                                    df = dfs[0]
                                except:
                                    # 无表头
                                    dfs = pd.read_html(html_content, header=None, flavor='bs4')
                                    df = dfs[0]

                            # 保存为Excel
                            df.to_excel(file_path, index=False)
                            print(f"表格数据已保存到: {file_path}")
                            scrape_count += 1
                        except Exception as e:
                            print(f"解析表格数据时出错: {e}")
                            error_count += 1
                    else:
                        print("未找到合适的表格")
                        error_count += 1
                else:
                    print("页面上未找到表格元素")
                    error_count += 1
            else:
                download_link_ele = download_links[-1]  # 取最后一个下载按钮

                # 清空临时下载目录中的文件
                for file in os.listdir(temp_download_dir):
                    file_path_to_remove = os.path.join(temp_download_dir, file)
                    try:
                        if os.path.isfile(file_path_to_remove):
                            os.remove(file_path_to_remove)
                    except Exception as e:
                        print(f"清理临时文件时出错: {e}")

                # 点击下载按钮
                print("点击下载按钮...")
                download_link_ele.click()

                # 等待下载完成
                downloaded = False
                start_time = time.time()
                timeout = 60  # 设置超时时间为60秒

                while not downloaded and time.time() - start_time < timeout:
                    time.sleep(3)  # 每3秒检查一次

                    # 检查临时目录中是否有新文件
                    files = os.listdir(temp_download_dir)
                    if files:
                        for file in files:
                            if file.endswith('.xls') or file.endswith('.xlsx') or file.endswith('.crdownload'):
                                # 如果还在下载中(有.crdownload后缀)，继续等待
                                if file.endswith('.crdownload'):
                                    print(f"文件正在下载中: {file}")
                                    continue

                                # 找到下载的文件
                                temp_file_path = os.path.join(temp_download_dir, file)
                                print(f"文件已下载到临时位置: {temp_file_path}")

                                # 移动并重命名文件
                                try:
                                    shutil.copy2(temp_file_path, file_path)
                                    print(f"文件已复制到最终位置: {file_path}")
                                    downloaded = True
                                    download_count += 1
                                    break
                                except Exception as e:
                                    print(f"移动文件时出错: {e}")
                                    error_count += 1

                if not downloaded:
                    print("下载超时或失败")
                    error_count += 1
        except Exception as e:
            print(f"处理链接时出错: {e}")
            error_count += 1

        # 防止请求过于频繁
        time.sleep(2)

    print(f"\n基础目录 {base_download_dir} 处理完成! 下载成功: {download_count}, 爬取成功: {scrape_count}, 失败: {error_count}")

def main():
    """主函数：爬取北京海关数据"""

    # 创建基础下载目录
    base_dir = "北京海关统计数据"
    if not os.path.exists(base_dir):
        os.makedirs(base_dir)
        print(f"创建基础下载目录: {base_dir}")

    # 创建临时下载目录用于存放原始下载文件
    temp_download_dir = "temp_download_beijing"
    if not os.path.exists(temp_download_dir):
        os.makedirs(temp_download_dir)
        print(f"创建临时下载目录: {temp_download_dir}")

    # 设置ChromiumPage下载路径到临时目录
    page = ChromiumPage(timeout=20)
    page.set.download_path(os.path.abspath(temp_download_dir))
    print(f"设置下载目录: {temp_download_dir}")

    # 定义基础URL
    base_url = "http://beijing.customs.gov.cn"

    # 访问北京海关统计数据页面
    start_url = "http://beijing.customs.gov.cn/beijing_customs/434756/434804/2941702/434773/434774/index.html"

    try:
        page.get(start_url)
        print(f"页面标题: {page.title}")
        time.sleep(2)  # 等待页面完全加载

        # 创建列表来存储所有链接
        all_links = []

        # 查找年份相关的链接
        print("\n开始查找年份和数据链接...")

        # 查找包含2024年和2025年的链接
        year_patterns = ["2024", "2025"]

        for year in year_patterns:
            print(f"\n查找{year}年的数据...")

            # 查找包含年份的元素
            year_elements = page.eles(f"{year}年")

            if year_elements:
                print(f"找到 {len(year_elements)} 个包含{year}年的元素")

                for year_elem in year_elements:
                    # 检查是否是链接
                    if year_elem.tag == 'a':
                        href = year_elem.attr('href')
                        if href:
                            # 构建完整URL
                            if href.startswith('/'):
                                full_url = base_url + href
                            elif not href.startswith('http'):
                                # 相对路径
                                current_url = page.url
                                base_path = '/'.join(current_url.split('/')[:-1]) + '/'
                                full_url = base_path + href
                            else:
                                full_url = href

                            text = year_elem.text.strip()
                            print(f"找到年份链接: {text} -> {full_url}")

                            # 访问年份页面查找具体的统计表格
                            try:
                                page.get(full_url)
                                time.sleep(2)

                                # 查找各种统计表格
                                for table_key, config in BEIJING_TARGET_TABLES_CONFIG.items():
                                    pattern = config['pattern']
                                    name = config['name']

                                    # 使用正则表达式查找匹配的元素
                                    page_html = page.html
                                    matches = re.findall(pattern, page_html, re.IGNORECASE)

                                    if matches:
                                        print(f"  找到匹配的表格类型: {name}")

                                        # 查找包含表格名称关键词的链接
                                        keywords = name.split('：')[0].split('B')[0].strip()  # 提取主要关键词
                                        table_links = page.eles(keywords)

                                        for table_link in table_links:
                                            if table_link.tag == 'a':
                                                table_href = table_link.attr('href')
                                                if table_href:
                                                    # 构建完整URL
                                                    if table_href.startswith('/'):
                                                        table_full_url = base_url + table_href
                                                    elif not table_href.startswith('http'):
                                                        current_url = page.url
                                                        base_path = '/'.join(current_url.split('/')[:-1]) + '/'
                                                        table_full_url = base_path + table_href
                                                    else:
                                                        table_full_url = table_href

                                                    table_text = table_link.text.strip()

                                                    # 提取月份信息（如果有）
                                                    month_match = re.search(r'(\d+)月', table_text)
                                                    month = month_match.group(1) + '月' if month_match else ''

                                                    link_info = {
                                                        "year": year,
                                                        "month": month,
                                                        "text": table_text,
                                                        "url": table_full_url,
                                                        "table_key": table_key,
                                                        "table_type": name
                                                    }

                                                    all_links.append(link_info)
                                                    print(f"    添加链接: {table_text} -> {table_full_url}")

                            except Exception as e:
                                print(f"访问年份页面时出错: {e}")
                                continue

                # 返回主页面
                page.get(start_url)
                time.sleep(2)
            else:
                print(f"未找到{year}年的相关元素")

        print(f"\n总共找到 {len(all_links)} 个链接")

        # 将结果保存到文件中
        with open('北京海关统计数据_所有链接.txt', 'w', encoding='utf-8') as f:
            for link_info in all_links:
                f.write(f"{link_info['year']},{link_info['month']},{link_info['text']},{link_info['url']},{link_info['table_key']},{link_info['table_type']}\n")

        print("链接已保存到文件: 北京海关统计数据_所有链接.txt")

        # 开始下载文件
        if all_links:
            print("\n开始下载文件...")
            download_beijing_files(all_links, base_dir, page, temp_download_dir)
        else:
            print("未找到任何可下载的链接")

    except Exception as e:
        print(f"主程序执行时出错: {e}")

    finally:
        # 清理临时下载目录
        try:
            if os.path.exists(temp_download_dir):
                shutil.rmtree(temp_download_dir)
                print(f"临时下载目录已清理: {temp_download_dir}")
        except Exception as e:
            print(f"清理临时下载目录时出错: {e}")

        # 关闭浏览器
        page.quit()
        print("浏览器已关闭")

if __name__ == "__main__":
    main()