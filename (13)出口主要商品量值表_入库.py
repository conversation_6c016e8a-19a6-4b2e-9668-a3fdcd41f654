import pandas as pd
import os
import re
import cx_Oracle
import numpy as np

def convert_to_float_or_none(value):
    """清理数据，将'-'或空值转为None，其余转为浮点数"""
    if pd.isna(value) or str(value).strip() == '-':
        return None
    try:
        value_str = str(value).replace(',', '')
        return float(value_str)
    except (ValueError, TypeError):
        return None

def parse_table_13(file_path):
    """
    专门为(13)号表格“出口主要商品量值表”编写的解析器。
    它能动态处理10列和12列两种不同的文件格式。
    """
    try:
        # 移除 engine='openpyxl'，让pandas自动检测文件格式和引擎
        df_raw = pd.read_excel(file_path, header=None)
        
        # 1. 提取年月 (逻辑不变)
        title_text = df_raw.iloc[1, 1] if len(df_raw) > 1 and len(df_raw.columns) > 1 else ""
        year, month = "", ""
        if isinstance(title_text, str):
            year_month_match = re.search(r'(\d{4})年(\d{1,2})月', title_text)
            if year_month_match:
                year, month = year_month_match.groups()
        
        if not (year and month):
             print(f"    [!] 错误: 无法从文件 '{os.path.basename(file_path)}' 的标题中提取年月。")
             return None, None

        # 2. 查找表头起始行 "商品名称" (逻辑不变)
        header_start_row = -1
        for i, row in df_raw.iterrows():
            if row.astype(str).str.contains('商品名称').any():
                header_start_row = i
                break
        
        if header_start_row == -1:
            print(f"    [!] 错误: 在文件 '{os.path.basename(file_path)}' 中未找到'商品名称'表头。")
            return None, None
            
        # 3. 数据从表头行之后开始
        data_start_row = header_start_row + 2
        
        # 定义所有可能的列名
        all_column_names = [
            'COMMODITY_NAME', 'UNIT', 
            'MONTH_QUANTITY', 'MONTH_AMOUNT', 
            'YTD_QUANTITY', 'YTD_AMOUNT',
            'MONTH_QUANTITY_YOY', 'MONTH_AMOUNT_YOY',
            'YTD_QUANTITY_YOY', 'YTD_AMOUNT_YOY',
            'YOY_MONTH_EXPORT_WEIGHT', 'YOY_YTD_EXPORT_WEIGHT'
        ]
        
        # 4. 切片数据区域，最多取12列(B到M)
        df_sliced = df_raw.iloc[data_start_row:, 1:13].copy()
        
        # 5. 动态分配列名
        num_actual_cols = df_sliced.shape[1]
        
        if num_actual_cols == 10:
            df_sliced.columns = all_column_names[:10]
        elif num_actual_cols == 12:
            df_sliced.columns = all_column_names
        else:
            print(f"    [!] 错误: 文件 '{os.path.basename(file_path)}' 的列数 ({num_actual_cols}) 不符合预期的10或12列。")
            return None, None
        
        # 6. 标准化DataFrame，确保所有文件都有12列
        for col_name in all_column_names:
            if col_name not in df_sliced.columns:
                df_sliced[col_name] = np.nan # 用NaN填充缺失列

        # 7. 统一进行数据清理
        # 过滤掉商品名称为空或以"注："开头的无效行
        df_sliced.dropna(subset=['COMMODITY_NAME'], inplace=True)
        df_sliced = df_sliced[~df_sliced['COMMODITY_NAME'].astype(str).str.strip().str.startswith('注：')]
        df_sliced = df_sliced[df_sliced['COMMODITY_NAME'].astype(str).str.strip() != '']
        if df_sliced.empty:
            return None, None

        # 对所有数值列应用转换函数
        numeric_cols = all_column_names[2:] # 从 'MONTH_QUANTITY' 开始都是数值
        for col in numeric_cols:
            df_sliced[col] = df_sliced[col].apply(convert_to_float_or_none)

        # 清理字符串列
        df_sliced['COMMODITY_NAME'] = df_sliced['COMMODITY_NAME'].astype(str).str.strip()
        # 对UNIT列进行稳健处理：将空值或'nan'字符串替换为'-'，以满足数据库的非空约束
        df_sliced['UNIT'] = df_sliced['UNIT'].astype(str).str.strip()
        df_sliced['UNIT'].replace(['nan', ''], '-', inplace=True)

        return df_sliced, f"{year}{month.zfill(2)}01"

    except Exception as e:
        print(f"    [!] 解析文件 '{os.path.basename(file_path)}' 时出错: {e}")
        return None, None

def batch_process_directory_13(directory_path, cursor, conn):
    """遍历目录，对2021年及以后版本的新文件执行增量入库"""
    print(f"\n--- 开始扫描目录: {directory_path} ---")
    if not os.path.isdir(directory_path):
        print(f"    [!] 错误: 目录 '{directory_path}' 不存在")
        return

    for filename in sorted(os.listdir(directory_path)):
        if not (filename.lower().endswith(".xlsx") or filename.lower().endswith(".xls")):
            continue
        
        year_match = re.search(r'(\d{4})', filename) # 放宽年份匹配规则
        if year_match:
            year = int(year_match.group(1))
            if year < 2021:
                continue
        else:
            continue
        
        file_path = os.path.join(directory_path, filename)
        
        df, current_month_str = parse_table_13(file_path)

        if df is None or df.empty:
            continue
        
        currency_type = "人民币" if "人民币" in directory_path else "美元"
        
        print(f"    [*] 正在处理: {filename}")
        
        try:
            # 1. 查询当月数据库中已有的商品
            select_keys_query = "SELECT COMMODITY_NAME FROM temp_cus_mon_13 WHERE CURRENT_MONTH = TO_DATE(:1, 'YYYYMMDD') AND CURRENCY_TYPE = :2"
            cursor.execute(select_keys_query, (current_month_str, currency_type))
            existing_commodities = {row[0] for row in cursor.fetchall()}
            
            if existing_commodities:
                print(f"        -> 数据库中已存在 {len(existing_commodities)} 条当月记录。")

            # 2. 筛选出需要新增的数据
            df_new = df[~df['COMMODITY_NAME'].isin(existing_commodities)]
            
            if df_new.empty:
                print(f"        -> 无新数据需要插入，跳过。")
                continue
                
            print(f"        -> 筛选出 {len(df_new)} 条新数据准备插入。")

            # 3. 准备并插入新数据
            df_new = df_new.copy() # 避免SettingWithCopyWarning
            df_new['CURRENT_MONTH'] = current_month_str
            df_new['CURRENCY_TYPE'] = currency_type
            
            column_order = [
                'COMMODITY_NAME', 'CURRENT_MONTH', 'CURRENCY_TYPE', 'UNIT',
                'MONTH_QUANTITY', 'MONTH_AMOUNT', 'YTD_QUANTITY', 'YTD_AMOUNT',
                'MONTH_QUANTITY_YOY', 'MONTH_AMOUNT_YOY', 'YTD_QUANTITY_YOY', 'YTD_AMOUNT_YOY',
                'YOY_MONTH_EXPORT_WEIGHT', 'YOY_YTD_EXPORT_WEIGHT'
            ]
            df_new = df_new[column_order]
            df_new = df_new.replace({np.nan: None})
            data_to_insert = [tuple(row) for row in df_new.to_records(index=False)]

            insert_query = """
            INSERT INTO temp_cus_mon_13 (
                COMMODITY_NAME, CURRENT_MONTH, CURRENCY_TYPE, UNIT,
                MONTH_QUANTITY, MONTH_AMOUNT, YTD_QUANTITY, YTD_AMOUNT,
                MONTH_QUANTITY_YOY, MONTH_AMOUNT_YOY, YTD_QUANTITY_YOY, YTD_AMOUNT_YOY,
                YOY_MONTH_EXPORT_WEIGHT, YOY_YTD_EXPORT_WEIGHT
            ) VALUES (
                :1, TO_DATE(:2, 'YYYYMMDD'), :3, :4, :5, :6, :7, :8, :9, :10, :11, :12, :13, :14
            )
            """
            cursor.executemany(insert_query, data_to_insert)
            print(f"        -> 成功插入 {cursor.rowcount} 条新记录.")

        except cx_Oracle.Error as e:
            print(f"        [!] 数据库错误: {e}")
            print(f"        -> 正在回滚对文件 {filename} 的操作...")
            conn.rollback() # 回滚当前文件的失败事务
        except Exception as e:
            print(f"        [!] 未知错误: {e}")
            print(f"        -> 正在回滚对文件 {filename} 的操作...")
            conn.rollback()

    print(f"--- 目录扫描完成 ---")

if __name__ == "__main__":
    base_dir = os.getcwd()
    rmb_dir = os.path.join(base_dir, r"进出口商品统计表_人民币值\(13)出口主要商品量值表_人民币值")
    usd_dir = os.path.join(base_dir, r"进出口商品统计表_美元值\(13)出口主要商品量值表_美元值")

    conn = None
    try:
        print("正在连接到Oracle数据库...")
        conn = cx_Oracle.connect("manifest_dcb/manifest_dcb@192.168.1.151/TEST")
        cursor = conn.cursor()
        print("数据库连接成功。")
        
        # 关闭自动提交，手动控制事务
        conn.autocommit = False

        batch_process_directory_13(rmb_dir, cursor, conn)
        batch_process_directory_13(usd_dir, cursor, conn)
        
        conn.commit()
        print("\n数据提交成功。")
        
    except cx_Oracle.Error as error:
        print(f"\n[!!!] 数据库操作期间发生严重错误: {error}")
        if conn:
            conn.rollback()
            print("    -> 事务已回滚。")
    except Exception as e:
        print(f"\n[!!!] 批量处理期间发生未知错误: {e}")
        if conn:
            conn.rollback()
            print("    -> 事务已回滚。")
    finally:
        if 'cursor' in locals() and cursor: cursor.close()
        if conn:
            conn.close()
            print("数据库连接已关闭。") 