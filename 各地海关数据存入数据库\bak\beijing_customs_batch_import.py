import pandas as pd
import numpy as np
import datetime
import os
import re
import cx_<PERSON>
from pathlib import Path

def extract_date_from_filename(filename):
    """
    从文件名中提取日期信息
    """
    # 匹配年份和月份
    patterns = [
        r'(\d{4})年(\d{1,2})月',  # 如：2025年1月
        r'(\d{4})年1-(\d{1,2})月',  # 如：2024年1-12月
        r'(\d{4})_.*?(\d{4})年1-(\d{1,2})月',  # 如：2024_(1)_（1）北京地区...（2024年1-2月）
        r'(\d{4})年(\d{1,2})-(\d{1,2})月',  # 如：2024年1-2月
    ]
    
    for pattern in patterns:
        match = re.search(pattern, filename)
        if match:
            groups = match.groups()
            if len(groups) == 2:  # 单月格式
                year, month = groups
                return f"{year}/{month.zfill(2)}/1"
            elif len(groups) == 3:  # 累计格式
                if '1-' in filename:
                    year, end_month = groups[0], groups[2]
                    return f"{year}/{end_month.zfill(2)}/1"
                else:
                    year, start_month, end_month = groups
                    return f"{year}/{end_month.zfill(2)}/1"
    
    # 默认返回当前日期
    return datetime.datetime.now().strftime('%Y/%m/1')

def detect_unit_from_data(df, amount_columns):
    """
    根据数据数值范围智能推断单位
    """
    # 收集所有金额字段的数值
    all_values = []
    for col in amount_columns:
        if col in df.columns:
            values = pd.to_numeric(df[col], errors='coerce').dropna()
            all_values.extend(values.tolist())
    
    if not all_values:
        return 'unknown'
    
    # 计算统计信息
    median_value = np.median(all_values)
    max_value = np.max(all_values)
    min_value = np.min(all_values)
    
    print(f"  数值分析: 中位数={median_value:.2f}, 最大值={max_value:.2f}, 最小值={min_value:.2f}")
    
    # 根据数值范围推断单位
    if median_value > 100000000:  # 大于1亿，推断为元
        return 'yuan'
    elif median_value > 10000:   # 大于1万，推断为万元
        return 'wan_yuan'
    elif median_value > 100:      # 大于100，推断为亿元
        return 'yi_yuan'
    else:
        return 'unknown'

def detect_unit_from_markers(df):
    """
    从工作表中检测单位标记
    """
    # 检查列名中的单位标记
    for col in df.columns:
        col_str = str(col).lower()
        if '单位' in col_str or 'unit' in col_str:
            if '亿元' in col_str:
                return 'yi_yuan'
            elif '万元' in col_str:
                return 'wan_yuan'
            elif '元' in col_str:
                return 'yuan'
    
    # 检查数据行中的单位标记
    for idx, row in df.iterrows():
        for val in row:
            if pd.notna(val) and isinstance(val, str):
                val_lower = val.lower()
                if '单位' in val_lower or 'unit' in val_lower:
                    if '亿元' in val_lower:
                        return 'yi_yuan'
                    elif '万元' in val_lower:
                        return 'wan_yuan'
                    elif '元' in val_lower:
                        return 'yuan'
    
    return 'unknown'

def get_conversion_factor(unit):
    """
    获取单位转换系数（转换为亿元）
    """
    conversion_map = {
        'yuan': 100000000,    # 元 -> 亿元
        'wan_yuan': 10000,    # 万元 -> 亿元
        'yi_yuan': 1,         # 亿元 -> 亿元
        'unknown': 10000      # 默认按万元处理
    }
    return conversion_map.get(unit, 10000)

def clean_excel_data(df, sheet_name):
    """
    清洗Excel数据，处理特殊字符、空值、数据类型等问题
    """
    # 删除全为空值的列
    df = df.dropna(axis=1, how='all')
    
    # 清理列名：去除特殊字符和空格
    df.columns = df.columns.astype(str).str.strip().str.replace('\xa0', ' ').str.replace(r'Unnamed: \d+', '', regex=True)
    
    # 查找项目列或商品列
    project_col = None
    for col in df.columns:
        col_str = str(col).strip()
        if any(keyword in col_str for keyword in ['项目', '商品', '国别', '地区', '']):
            project_col = col
            break
    
    if project_col is None:
        # 如果没有找到项目列，使用第一列
        project_col = df.columns[0]
    
    # 过滤数据
    clean_df = df[df[project_col].notna()]
    clean_df = clean_df[~clean_df[project_col].astype(str).str.contains('单位|合计|总计|nan|年月日', case=False, na=False)]
    clean_df = clean_df[clean_df[project_col].astype(str).str.strip() != '']
    
    # 清理数据中的特殊字符
    for col in clean_df.columns:
        if clean_df[col].dtype == 'object':
            clean_df[col] = clean_df[col].astype(str).str.replace('*', '').str.strip()
    
    return clean_df, project_col

def determine_file_type_and_stat_info(filename):
    """
    根据文件名确定统计类型信息
    """
    filename_lower = filename.lower()
    
    # 北京文件类型映射
    if '总值表b' in filename_lower or '月度表' in filename_lower:
        return ('01', '月度总值')
    elif '国别' in filename_lower or '地区' in filename_lower:
        return ('03', '国别地区')
    elif '贸易方式' in filename_lower and '企业性质' in filename_lower:
        return ('02', '企业性质')
    elif '出口' in filename_lower and '商品' in filename_lower:
        return ('04', '主出商品')
    elif '进口' in filename_lower and '商品' in filename_lower:
        return ('05', '主进商品')
    elif '贸易方式' in filename_lower:
        return ('01', '贸易方式')
    else:
        return ('99', '未知')

def process_beijing_excel_file(excel_path):
    """
    处理单个北京Excel文件，转换为标准格式
    """
    filename = os.path.basename(excel_path)
    print(f"  正在处理文件: {filename}")
    
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_path, header=None)
        
        # 找到表头行（包含"项目"、"商品"等关键词的行）
        header_row_idx = 0
        for i, row in df.iterrows():
            if any(keyword in str(row.iloc[0]) for keyword in ['项目', '商品', '国别', '地区', '年月日']):
                header_row_idx = i
                break
        
        # 设置表头
        header = df.iloc[header_row_idx].tolist()
        data_df = df.iloc[header_row_idx + 1:].copy()
        data_df.columns = header
        
        # 清洗数据
        clean_df, project_col = clean_excel_data(data_df, filename)
        
        if len(clean_df) == 0:
            print(f"    警告：文件 {filename} 没有有效数据")
            return None
        
        print(f"    清洗后数据行数: {len(clean_df)}")
        
        # 确定统计类型
        stat_type, stat_name = determine_file_type_and_stat_info(filename)
        
        # 智能单位检测
        print(f"    正在检测单位...")
        marker_unit = detect_unit_from_markers(df)
        
        # 寻找金额列
        amount_columns = []
        for col in clean_df.columns:
            col_str = str(col).lower()
            if any(keyword in col_str for keyword in ['累计', '当期', '当月', '金额', '总值', '进出口', '出口', '进口']):
                amount_columns.append(col)
        
        data_unit = detect_unit_from_data(clean_df, amount_columns)
        
        # 确定最终单位
        if marker_unit != 'unknown':
            final_unit = marker_unit
        else:
            final_unit = data_unit
        
        conversion_factor = get_conversion_factor(final_unit)
        print(f"    使用单位: {final_unit}, 转换系数: {conversion_factor}")
        
        # 处理后的数据
        result_data = {
            'df': clean_df,
            'project_col': project_col,
            'stat_type': stat_type,
            'stat_name': stat_name,
            'conversion_factor': conversion_factor,
            'filename': filename
        }
        
        return result_data
        
    except Exception as e:
        print(f"    处理文件 {filename} 时出错: {e}")
        return None

def transform_single_excel(input_excel_path, output_csv_path):
    """
    转换单个Excel文件为CSV格式
    """
    try:
        # 获取文件名和日期
        filename = os.path.basename(input_excel_path)
        stat_date = extract_date_from_filename(filename)
        
        print(f"处理文件: {filename}")
        print(f"提取的日期: {stat_date}")
        
        # 处理Excel文件
        result_data = process_beijing_excel_file(input_excel_path)
        if result_data is None:
            return None
        
        # 目标CSV文件的列名
        output_columns = [
            "STAT_DATE", "STAT_TYPE", "STAT_NAME", "STAT_CODE", "STAT_CONTENT_RAW",
            "STAT_CONTENT_CLEANSE", "ACC_A_CNY_AMOUNT", "ACC_A_CNY_YOY", "ACC_A_USD_AMOUNT",
            "ACC_A_USD_YOY", "MON_A_CNY_AMOUNT", "MON_A_CNY_YOY", "MON_A_USD_AMOUNT",
            "MON_A_USD_YOY", "ACC_E_CNY_AMOUNT", "ACC_E_CNY_YOY", "ACC_E_USD_AMOUNT",
            "ACC_E_USD_YOY", "MON_E_CNY_AMOUNT", "MON_E_CNY_YOY", "MON_E_USD_AMOUNT",
            "MON_E_USD_YOY", "ACC_I_CNY_AMOUNT", "ACC_I_CNY_YOY", "ACC_I_USD_AMOUNT",
            "ACC_I_USD_YOY", "MON_I_CNY_AMOUNT", "MON_I_CNY_YOY", "MON_I_USD_AMOUNT",
            "MON_I_USD_YOY", "ACC_E_AMOUNT", "ACC_E_AMOUNT_UNIT", "ACC_E_AMOUNT_YOY",
            "MON_E_AMOUNT", "MON_E_AMOUNT_UNIT", "MON_E_AMOUNT_YOY", "ACC_I_AMOUNT",
            "ACC_I_AMOUNT_UNIT", "ACC_I_AMOUNT_YOY", "MON_I_AMOUNT", "MON_I_AMOUNT_UNIT",
            "MON_I_AMOUNT_YOY", "RANK_MARKERS", "DATA_SOURCE", "EMPHASIS_OR_EMERGING_MARK",
            "CREATE_TIME"
        ]
        
        # 创建输出DataFrame
        output_df = pd.DataFrame(columns=output_columns)
        
        clean_df = result_data['df']
        project_col = result_data['project_col']
        conversion_factor = result_data['conversion_factor']
        
        # 基础字段
        output_df['STAT_CONTENT_RAW'] = clean_df[project_col]
        output_df['STAT_CONTENT_CLEANSE'] = clean_df[project_col].astype(str).str.strip()
        output_df['STAT_TYPE'] = result_data['stat_type']
        output_df['STAT_NAME'] = result_data['stat_name']
        output_df['STAT_CODE'] = ''
        output_df['STAT_DATE'] = stat_date
        output_df['DATA_SOURCE'] = '01'  # 北京
        output_df['EMPHASIS_OR_EMERGING_MARK'] = ''
        output_df['RANK_MARKERS'] = ''
        output_df['CREATE_TIME'] = datetime.datetime.now().strftime('%Y/%m/%d %H:%M:%S')
        
        # 根据文件类型处理数据字段
        if result_data['stat_type'] == '01':  # 月度总值
            # 月度总值表特殊处理（时间序列数据）
            for col in clean_df.columns:
                col_str = str(col).lower()
                if '累计' in col_str and '进出口' in col_str:
                    output_df['ACC_A_CNY_AMOUNT'] = pd.to_numeric(clean_df[col], errors='coerce') / conversion_factor
                elif '累计' in col_str and '出口' in col_str:
                    output_df['ACC_E_CNY_AMOUNT'] = pd.to_numeric(clean_df[col], errors='coerce') / conversion_factor
                elif '累计' in col_str and '进口' in col_str:
                    output_df['ACC_I_CNY_AMOUNT'] = pd.to_numeric(clean_df[col], errors='coerce') / conversion_factor
                elif ('当期' in col_str or '当月' in col_str) and '进出口' in col_str:
                    output_df['MON_A_CNY_AMOUNT'] = pd.to_numeric(clean_df[col], errors='coerce') / conversion_factor
                elif ('当期' in col_str or '当月' in col_str) and '出口' in col_str:
                    output_df['MON_E_CNY_AMOUNT'] = pd.to_numeric(clean_df[col], errors='coerce') / conversion_factor
                elif ('当期' in col_str or '当月' in col_str) and '进口' in col_str:
                    output_df['MON_I_CNY_AMOUNT'] = pd.to_numeric(clean_df[col], errors='coerce') / conversion_factor
                elif '同比' in col_str and '进出口' in col_str:
                    if '累计' in col_str:
                        output_df['ACC_A_CNY_YOY'] = pd.to_numeric(clean_df[col], errors='coerce')
                    else:
                        output_df['MON_A_CNY_YOY'] = pd.to_numeric(clean_df[col], errors='coerce')
                elif '同比' in col_str and '出口' in col_str:
                    if '累计' in col_str:
                        output_df['ACC_E_CNY_YOY'] = pd.to_numeric(clean_df[col], errors='coerce')
                    else:
                        output_df['MON_E_CNY_YOY'] = pd.to_numeric(clean_df[col], errors='coerce')
                elif '同比' in col_str and '进口' in col_str:
                    if '累计' in col_str:
                        output_df['ACC_I_CNY_YOY'] = pd.to_numeric(clean_df[col], errors='coerce')
                    else:
                        output_df['MON_I_CNY_YOY'] = pd.to_numeric(clean_df[col], errors='coerce')
        
        else:  # 其他类型的表
            # 智能识别列并映射到对应字段
            for col in clean_df.columns:
                col_str = str(col).lower()
                
                # 累计数据
                if '累计' in col_str:
                    if '进出口' in col_str or '总值' in col_str:
                        output_df['ACC_A_CNY_AMOUNT'] = pd.to_numeric(clean_df[col], errors='coerce') / conversion_factor
                    elif '出口' in col_str:
                        output_df['ACC_E_CNY_AMOUNT'] = pd.to_numeric(clean_df[col], errors='coerce') / conversion_factor
                    elif '进口' in col_str:
                        output_df['ACC_I_CNY_AMOUNT'] = pd.to_numeric(clean_df[col], errors='coerce') / conversion_factor
                
                # 当期数据
                elif '当期' in col_str or '当月' in col_str:
                    if '进出口' in col_str or '总值' in col_str:
                        output_df['MON_A_CNY_AMOUNT'] = pd.to_numeric(clean_df[col], errors='coerce') / conversion_factor
                    elif '出口' in col_str:
                        output_df['MON_E_CNY_AMOUNT'] = pd.to_numeric(clean_df[col], errors='coerce') / conversion_factor
                    elif '进口' in col_str:
                        output_df['MON_I_CNY_AMOUNT'] = pd.to_numeric(clean_df[col], errors='coerce') / conversion_factor
                
                # 同比数据
                elif '同比' in col_str:
                    if '进出口' in col_str or '总值' in col_str:
                        if '累计' in filename.lower():
                            output_df['ACC_A_CNY_YOY'] = pd.to_numeric(clean_df[col], errors='coerce')
                        else:
                            output_df['MON_A_CNY_YOY'] = pd.to_numeric(clean_df[col], errors='coerce')
                    elif '出口' in col_str:
                        if '累计' in filename.lower():
                            output_df['ACC_E_CNY_YOY'] = pd.to_numeric(clean_df[col], errors='coerce')
                        else:
                            output_df['MON_E_CNY_YOY'] = pd.to_numeric(clean_df[col], errors='coerce')
                    elif '进口' in col_str:
                        if '累计' in filename.lower():
                            output_df['ACC_I_CNY_YOY'] = pd.to_numeric(clean_df[col], errors='coerce')
                        else:
                            output_df['MON_I_CNY_YOY'] = pd.to_numeric(clean_df[col], errors='coerce')
                
                # 数量数据
                elif '数量' in col_str:
                    if '出口' in result_data['stat_name']:
                        if '累计' in filename.lower():
                            output_df['ACC_E_AMOUNT'] = pd.to_numeric(clean_df[col], errors='coerce')
                        else:
                            output_df['MON_E_AMOUNT'] = pd.to_numeric(clean_df[col], errors='coerce')
                    elif '进口' in result_data['stat_name']:
                        if '累计' in filename.lower():
                            output_df['ACC_I_AMOUNT'] = pd.to_numeric(clean_df[col], errors='coerce')
                        else:
                            output_df['MON_I_AMOUNT'] = pd.to_numeric(clean_df[col], errors='coerce')
        
        # 填充空值字段
        numeric_columns = [col for col in output_columns if 'AMOUNT' in col or 'YOY' in col]
        string_columns = [col for col in output_columns if col not in numeric_columns]
        
        for col in numeric_columns:
            if col not in output_df.columns or output_df[col].isna().all():
                output_df[col] = np.nan
        
        for col in string_columns:
            if col not in output_df.columns:
                output_df[col] = ''
        
        # 确保列顺序正确
        output_df = output_df[output_columns]
        
        # 数据类型处理
        for col in output_df.columns:
            if 'AMOUNT' in col or 'YOY' in col:
                output_df[col] = pd.to_numeric(output_df[col], errors='coerce')
            else:
                output_df[col] = output_df[col].fillna('').astype(str)
        
        # 保存CSV文件
        output_df.to_csv(output_csv_path, index=False, encoding='utf-8-sig', float_format='%.4f')
        print(f"数据已成功转换并保存到 {output_csv_path}")
        print(f"总记录数: {len(output_df)}")
        
        return output_df
        
    except Exception as e:
        print(f"转换文件 {input_excel_path} 时出错: {e}")
        return None

def batch_transform_excel_files(input_dir, output_dir):
    """
    批量转换北京Excel文件
    """
    # 创建输出目录
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    # 获取所有Excel文件
    excel_files = []
    for file in os.listdir(input_dir):
        if file.endswith('.xls') and not file.startswith('~'):
            excel_files.append(file)
    
    if not excel_files:
        print(f"在目录 {input_dir} 中没有找到Excel文件")
        return
    
    print(f"找到 {len(excel_files)} 个Excel文件")
    
    # 统计信息
    total_files = 0
    successful_files = 0
    total_records = 0
    
    for excel_file in sorted(excel_files):
        total_files += 1
        
        input_path = os.path.join(input_dir, excel_file)
        output_filename = f"T_STATISTICAL_CUS_TOTAL_BEIJING_{os.path.splitext(excel_file)[0]}.csv"
        output_path = os.path.join(output_dir, output_filename)
        
        print(f"\n{'='*80}")
        print(f"处理文件 {total_files}/{len(excel_files)}: {excel_file}")
        print(f"{'='*80}")
        
        try:
            result_df = transform_single_excel(input_path, output_path)
            if result_df is not None:
                successful_files += 1
                total_records += len(result_df)
                print(f"成功处理: {excel_file} ({len(result_df)} 条记录)")
            else:
                print(f"处理失败: {excel_file}")
        except Exception as e:
            print(f"处理出错 {excel_file}: {e}")
    
    print(f"\n{'='*80}")
    print("批量转换完成！")
    print(f"{'='*80}")
    print(f"总文件数: {total_files}")
    print(f"成功处理: {successful_files}")
    print(f"失败文件: {total_files - successful_files}")
    print(f"总记录数: {total_records}")
    print(f"输出目录: {output_dir}")

def import_single_csv_to_oracle(csv_file_path, db_config, table_name="T_STATISTICAL_CUS_TOTAL_CS"):
    """
    将单个CSV文件导入Oracle数据库
    """
    try:
        # 读取CSV文件
        print(f"正在读取CSV文件: {os.path.basename(csv_file_path)}")
        df = pd.read_csv(csv_file_path, encoding='utf-8-sig')
        
        print(f"CSV文件读取成功")
        print(f"总记录数: {len(df)}")
        
        # 连接Oracle数据库
        print(f"正在连接Oracle数据库...")
        conn = cx_Oracle.connect(**db_config)
        cursor = conn.cursor()
        
        print("数据库连接成功")
        
        # 准备插入SQL
        insert_sql = f"""
        INSERT INTO {table_name} (
            STAT_DATE, STAT_TYPE, STAT_NAME, STAT_CODE, STAT_CONTENT_RAW, STAT_CONTENT_CLEANSE,
            ACC_A_CNY_AMOUNT, ACC_A_CNY_YOY, ACC_A_USD_AMOUNT, ACC_A_USD_YOY,
            MON_A_CNY_AMOUNT, MON_A_CNY_YOY, MON_A_USD_AMOUNT, MON_A_USD_YOY,
            ACC_E_CNY_AMOUNT, ACC_E_CNY_YOY, ACC_E_USD_AMOUNT, ACC_E_USD_YOY,
            MON_E_CNY_AMOUNT, MON_E_CNY_YOY, MON_E_USD_AMOUNT, MON_E_USD_YOY,
            ACC_I_CNY_AMOUNT, ACC_I_CNY_YOY, ACC_I_USD_AMOUNT, ACC_I_USD_YOY,
            MON_I_CNY_AMOUNT, MON_I_CNY_YOY, MON_I_USD_AMOUNT, MON_I_USD_YOY,
            ACC_E_AMOUNT, ACC_E_AMOUNT_UNIT, ACC_E_AMOUNT_YOY,
            MON_E_AMOUNT, MON_E_AMOUNT_UNIT, MON_E_AMOUNT_YOY,
            ACC_I_AMOUNT, ACC_I_AMOUNT_UNIT, ACC_I_AMOUNT_YOY,
            MON_I_AMOUNT, MON_I_AMOUNT_UNIT, MON_I_AMOUNT_YOY,
            RANK_MARKERS, DATA_SOURCE, EMPHASIS_OR_EMERGING_MARK, CREATE_TIME
        ) VALUES (
            :1, :2, :3, :4, :5, :6,
            :7, :8, :9, :10,
            :11, :12, :13, :14,
            :15, :16, :17, :18,
            :19, :20, :21, :22,
            :23, :24, :25, :26,
            :27, :28, :29, :30,
            :31, :32, :33,
            :34, :35, :36,
            :37, :38, :39,
            :40, :41, :42,
            :43, :44, :45, :46
        )
        """
        
        # 批量插入数据
        batch_size = 100
        total_rows = len(df)
        inserted_rows = 0
        
        print(f"开始插入数据（批量大小: {batch_size}）...")
        
        for start_idx in range(0, total_rows, batch_size):
            end_idx = min(start_idx + batch_size, total_rows)
            batch_df = df.iloc[start_idx:end_idx]
            
            # 准备批量数据
            batch_data = []
            for _, row in batch_df.iterrows():
                # 处理数值字段的空值
                def get_numeric_value(value):
                    if pd.isna(value) or value == '' or value == 'nan':
                        return None
                    try:
                        return float(value)
                    except (ValueError, TypeError):
                        return None
                
                # 处理字符串字段的空值
                def get_string_value(value):
                    if pd.isna(value) or value == '' or value == 'nan':
                        return None
                    return str(value)
                
                # 构建数据行
                data_row = (
                    get_string_value(row['STAT_DATE']),
                    get_string_value(row['STAT_TYPE']),
                    get_string_value(row['STAT_NAME']),
                    get_string_value(row['STAT_CODE']),
                    get_string_value(row['STAT_CONTENT_RAW']),
                    get_string_value(row['STAT_CONTENT_CLEANSE']),
                    get_numeric_value(row['ACC_A_CNY_AMOUNT']),
                    get_numeric_value(row['ACC_A_CNY_YOY']),
                    get_numeric_value(row['ACC_A_USD_AMOUNT']),
                    get_numeric_value(row['ACC_A_USD_YOY']),
                    get_numeric_value(row['MON_A_CNY_AMOUNT']),
                    get_numeric_value(row['MON_A_CNY_YOY']),
                    get_numeric_value(row['MON_A_USD_AMOUNT']),
                    get_numeric_value(row['MON_A_USD_YOY']),
                    get_numeric_value(row['ACC_E_CNY_AMOUNT']),
                    get_numeric_value(row['ACC_E_CNY_YOY']),
                    get_numeric_value(row['ACC_E_USD_AMOUNT']),
                    get_numeric_value(row['ACC_E_USD_YOY']),
                    get_numeric_value(row['MON_E_CNY_AMOUNT']),
                    get_numeric_value(row['MON_E_CNY_YOY']),
                    get_numeric_value(row['MON_E_USD_AMOUNT']),
                    get_numeric_value(row['MON_E_USD_YOY']),
                    get_numeric_value(row['ACC_I_CNY_AMOUNT']),
                    get_numeric_value(row['ACC_I_CNY_YOY']),
                    get_numeric_value(row['ACC_I_USD_AMOUNT']),
                    get_numeric_value(row['ACC_I_USD_YOY']),
                    get_numeric_value(row['MON_I_CNY_AMOUNT']),
                    get_numeric_value(row['MON_I_CNY_YOY']),
                    get_numeric_value(row['MON_I_USD_AMOUNT']),
                    get_numeric_value(row['MON_I_USD_YOY']),
                    get_numeric_value(row['ACC_E_AMOUNT']),
                    get_string_value(row['ACC_E_AMOUNT_UNIT']),
                    get_numeric_value(row['ACC_E_AMOUNT_YOY']),
                    get_numeric_value(row['MON_E_AMOUNT']),
                    get_string_value(row['MON_E_AMOUNT_UNIT']),
                    get_numeric_value(row['MON_E_AMOUNT_YOY']),
                    get_numeric_value(row['ACC_I_AMOUNT']),
                    get_string_value(row['ACC_I_AMOUNT_UNIT']),
                    get_numeric_value(row['ACC_I_AMOUNT_YOY']),
                    get_numeric_value(row['MON_I_AMOUNT']),
                    get_string_value(row['MON_I_AMOUNT_UNIT']),
                    get_numeric_value(row['MON_I_AMOUNT_YOY']),
                    get_string_value(row['RANK_MARKERS']),
                    get_string_value(row['DATA_SOURCE']),
                    get_string_value(row['EMPHASIS_OR_EMERGING_MARK']),
                    get_string_value(row['CREATE_TIME'])
                )
                batch_data.append(data_row)
            
            # 执行批量插入
            cursor.executemany(insert_sql, batch_data)
            conn.commit()
            
            inserted_rows += len(batch_data)
            progress = (inserted_rows / total_rows) * 100
            print(f"  已插入 {inserted_rows}/{total_rows} 条记录 ({progress:.1f}%)")
        
        print(f"数据插入完成！共插入 {inserted_rows} 条记录")
        
        # 关闭连接
        cursor.close()
        conn.close()
        
        return True, inserted_rows
        
    except cx_Oracle.DatabaseError as e:
        print(f"数据库错误: {e}")
        return False, 0
    except Exception as e:
        print(f"错误: {e}")
        return False, 0
    finally:
        try:
            if 'conn' in locals():
                conn.close()
        except:
            pass

def batch_import_to_oracle(csv_dir, db_config, truncate_table=False):
    """
    批量导入CSV文件到Oracle数据库
    """
    # 获取所有CSV文件
    csv_files = []
    for file in os.listdir(csv_dir):
        if file.endswith('.csv') and file.startswith('T_STATISTICAL_CUS_TOTAL_BEIJING_'):
            csv_files.append(file)
    
    if not csv_files:
        print(f"在目录 {csv_dir} 中没有找到北京CSV文件")
        return
    
    print(f"找到 {len(csv_files)} 个北京CSV文件")
    
    # 统计信息
    total_files = 0
    successful_files = 0
    total_records = 0
    
    # 清空表（可选）
    if truncate_table:
        try:
            print("正在清空目标表...")
            conn = cx_Oracle.connect(**db_config)
            cursor = conn.cursor()
            cursor.execute("TRUNCATE TABLE T_STATISTICAL_CUS_TOTAL_CS")
            conn.commit()
            cursor.close()
            conn.close()
            print("目标表已清空")
        except Exception as e:
            print(f"清空表时出错: {e}")
            return
    
    # 按文件名排序导入
    for csv_file in sorted(csv_files):
        total_files += 1
        
        csv_path = os.path.join(csv_dir, csv_file)
        
        print(f"\n{'='*80}")
        print(f"导入文件 {total_files}/{len(csv_files)}: {csv_file}")
        print(f"{'='*80}")
        
        try:
            success, record_count = import_single_csv_to_oracle(csv_path, db_config)
            if success:
                successful_files += 1
                total_records += record_count
                print(f"成功导入: {csv_file} ({record_count} 条记录)")
            else:
                print(f"导入失败: {csv_file}")
        except Exception as e:
            print(f"导入出错 {csv_file}: {e}")
    
    print(f"\n{'='*80}")
    print("批量导入完成！")
    print(f"{'='*80}")
    print(f"总文件数: {total_files}")
    print(f"成功导入: {successful_files}")
    print(f"失败文件: {total_files - successful_files}")
    print(f"总记录数: {total_records}")
    
    # 验证数据
    if successful_files > 0:
        try:
            print("\n正在验证数据...")
            conn = cx_Oracle.connect(**db_config)
            cursor = conn.cursor()
            
            # 检查总记录数
            cursor.execute("SELECT COUNT(*) FROM T_STATISTICAL_CUS_TOTAL_CS WHERE DATA_SOURCE = '01'")
            db_count = cursor.fetchone()[0]
            print(f"数据库中北京数据记录数: {db_count}")
            
            # 按统计类型分组统计
            cursor.execute("""
                SELECT STAT_TYPE, STAT_NAME, COUNT(*) 
                FROM T_STATISTICAL_CUS_TOTAL_CS 
                WHERE DATA_SOURCE = '01'
                GROUP BY STAT_TYPE, STAT_NAME 
                ORDER BY STAT_TYPE
            """)
            
            print("\n按统计类型分组统计（北京数据）:")
            for row in cursor.fetchall():
                print(f"  {row[0]} ({row[1]}): {row[2]} 条记录")
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            print(f"验证数据时出错: {e}")

def main():
    """
    主函数：完整的北京海关数据批量处理流程
    """
    # 配置路径
    input_directory = "北京海关统计数据_最终版"
    output_directory = "北京海关数据_批量转换结果"
    
    # 数据库连接配置
    db_config = {
        'user': 'manifest_dcb',
        'password': 'manifest_dcb',
        'dsn': '192.168.1.151/TEST'
    }
    
    print("=" * 100)
    print("北京海关统计数据批量处理系统")
    print("=" * 100)
    print(f"输入目录: {input_directory}")
    print(f"输出目录: {output_directory}")
    print(f"开始时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 第一步：批量转换Excel文件为CSV
    print("第一步：批量转换Excel文件为CSV")
    print("-" * 50)
    batch_transform_excel_files(input_directory, output_directory)
    
    # 第二步：批量导入CSV文件到Oracle数据库
    print("\n第二步：批量导入CSV文件到Oracle数据库")
    print("-" * 50)
    batch_import_to_oracle(output_directory, db_config, truncate_table=False)
    
    print(f"\n结束时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 100)
    print("北京海关数据批量处理完成！")
    print("您现在可以在Oracle数据库表 T_STATISTICAL_CUS_TOTAL_CS 中查询北京海关数据")
    print("数据源标识：DATA_SOURCE = '01'")
    print("=" * 100)

if __name__ == '__main__':
    main()