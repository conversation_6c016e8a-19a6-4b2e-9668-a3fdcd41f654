"""
北京海关数据批量导入Oracle数据库 - 修复版
修复日期格式转换问题
"""

import pandas as pd
import os
import sys
from datetime import datetime
from pathlib import Path

# 检查是否安装了cx_Oracle
try:
    import cx_Oracle
    ORACLE_AVAILABLE = True
except ImportError:
    ORACLE_AVAILABLE = False
    print("警告：未安装cx_Oracle模块，将跳过Oracle导入功能")
    print("如需导入数据库，请运行：pip install cx_Oracle")

def import_single_csv_to_oracle(csv_file_path, db_config, table_name="T_STATISTICAL_CUS_TOTAL_CS"):
    """
    将单个CSV文件导入Oracle数据库 - 修复版
    """
    if not ORACLE_AVAILABLE:
        print(f"跳过文件 {csv_file_path}：cx_Oracle模块未安装")
        return False, 0
        
    try:
        # 读取CSV文件
        print(f"正在读取CSV文件: {os.path.basename(csv_file_path)}")
        df = pd.read_csv(csv_file_path, encoding='utf-8-sig')
        
        print(f"CSV文件读取成功")
        print(f"总记录数: {len(df)}")
        print(f"CSV列数: {len(df.columns)}")
        
        # 检查期望的列结构
        expected_columns = [
            "STAT_DATE", "STAT_TYPE", "STAT_NAME", "STAT_CODE", "STAT_CONTENT_RAW",
            "STAT_CONTENT_CLEANSE", "ACC_A_CNY_AMOUNT", "ACC_A_CNY_YOY", "ACC_A_USD_AMOUNT",
            "ACC_A_USD_YOY", "MON_A_CNY_AMOUNT", "MON_A_CNY_YOY", "MON_A_USD_AMOUNT",
            "MON_A_USD_YOY", "ACC_E_CNY_AMOUNT", "ACC_E_CNY_YOY", "ACC_E_USD_AMOUNT",
            "ACC_E_USD_YOY", "MON_E_CNY_AMOUNT", "MON_E_CNY_YOY", "MON_E_USD_AMOUNT",
            "MON_E_USD_YOY", "ACC_I_CNY_AMOUNT", "ACC_I_CNY_YOY", "ACC_I_USD_AMOUNT",
            "ACC_I_USD_YOY", "MON_I_CNY_AMOUNT", "MON_I_CNY_YOY", "MON_I_USD_AMOUNT",
            "MON_I_USD_YOY", "ACC_E_AMOUNT", "ACC_E_AMOUNT_UNIT", "ACC_E_AMOUNT_YOY",
            "MON_E_AMOUNT", "MON_E_AMOUNT_UNIT", "MON_E_AMOUNT_YOY", "ACC_I_AMOUNT",
            "ACC_I_AMOUNT_UNIT", "ACC_I_AMOUNT_YOY", "MON_I_AMOUNT", "MON_I_AMOUNT_UNIT",
            "MON_I_AMOUNT_YOY", "RANK_MARKERS", "DATA_SOURCE", "EMPHASIS_OR_EMERGING_MARK",
            "CREATE_TIME"
        ]
        
        print(f"期望列数: {len(expected_columns)}")
        
        # 检查缺失的列
        missing_columns = [col for col in expected_columns if col not in df.columns]
        if missing_columns:
            print(f"警告：CSV文件缺失以下列: {missing_columns}")
        
        # 检查多余的列
        extra_columns = [col for col in df.columns if col not in expected_columns]
        if extra_columns:
            print(f"警告：CSV文件包含多余列: {extra_columns}")
        
        # 连接Oracle数据库
        print(f"正在连接Oracle数据库...")
        conn = cx_Oracle.connect(**db_config)
        cursor = conn.cursor()
        
        print("数据库连接成功")
        
        # 准备插入SQL - 使用TO_DATE函数进行日期转换，完整的46个字段
        insert_sql = f"""
        INSERT INTO {table_name} (
            STAT_DATE, STAT_TYPE, STAT_NAME, STAT_CODE, STAT_CONTENT_RAW, STAT_CONTENT_CLEANSE,
            ACC_A_CNY_AMOUNT, ACC_A_CNY_YOY, ACC_A_USD_AMOUNT, ACC_A_USD_YOY,
            MON_A_CNY_AMOUNT, MON_A_CNY_YOY, MON_A_USD_AMOUNT, MON_A_USD_YOY,
            ACC_E_CNY_AMOUNT, ACC_E_CNY_YOY, ACC_E_USD_AMOUNT, ACC_E_USD_YOY,
            MON_E_CNY_AMOUNT, MON_E_CNY_YOY, MON_E_USD_AMOUNT, MON_E_USD_YOY,
            ACC_I_CNY_AMOUNT, ACC_I_CNY_YOY, ACC_I_USD_AMOUNT, ACC_I_USD_YOY,
            MON_I_CNY_AMOUNT, MON_I_CNY_YOY, MON_I_USD_AMOUNT, MON_I_USD_YOY,
            ACC_E_AMOUNT, ACC_E_AMOUNT_UNIT, ACC_E_AMOUNT_YOY,
            MON_E_AMOUNT, MON_E_AMOUNT_UNIT, MON_E_AMOUNT_YOY,
            ACC_I_AMOUNT, ACC_I_AMOUNT_UNIT, ACC_I_AMOUNT_YOY,
            MON_I_AMOUNT, MON_I_AMOUNT_UNIT, MON_I_AMOUNT_YOY,
            RANK_MARKERS, DATA_SOURCE, EMPHASIS_OR_EMERGING_MARK, CREATE_TIME
        ) VALUES (
            CASE WHEN :1 IS NOT NULL THEN TO_DATE(:1, 'YYYY-MM-DD') ELSE NULL END,
            :2, :3, :4, :5, :6,
            :7, :8, :9, :10,
            :11, :12, :13, :14,
            :15, :16, :17, :18,
            :19, :20, :21, :22,
            :23, :24, :25, :26,
            :27, :28, :29, :30,
            :31, :32, :33,
            :34, :35, :36,
            :37, :38, :39,
            :40, :41, :42,
            :43, :44, :45,
            CASE WHEN :46 IS NOT NULL THEN TO_DATE(:46, 'YYYY-MM-DD HH24:MI:SS') ELSE NULL END
        )
        """
        
        # 批量插入数据
        batch_size = 100
        total_rows = len(df)
        inserted_rows = 0
        
        print(f"开始插入数据（批量大小: {batch_size}）...")
        
        for start_idx in range(0, total_rows, batch_size):
            end_idx = min(start_idx + batch_size, total_rows)
            batch_df = df.iloc[start_idx:end_idx]
            
            # 准备批量数据
            batch_data = []
            for _, row in batch_df.iterrows():
                # 处理数值字段的空值
                def get_numeric_value(value):
                    if pd.isna(value) or value == '' or str(value) == 'nan':
                        return None
                    try:
                        return float(value)
                    except (ValueError, TypeError):
                        return None
                
                # 处理字符串字段的空值
                def get_string_value(value):
                    if pd.isna(value) or value == '' or str(value) == 'nan':
                        return None
                    return str(value).strip()
                
                # 修复的日期处理函数
                def get_date_value(value):
                    if pd.isna(value) or value == '' or str(value) == 'nan':
                        return None
                    try:
                        date_str = str(value).strip()
                        # 处理 2025/1/1 或 2025/01/01 格式
                        if '/' in date_str:
                            parts = date_str.split('/')
                            if len(parts) == 3:
                                year = parts[0].zfill(4)
                                month = parts[1].zfill(2)
                                day = parts[2].zfill(2)
                                return f"{year}-{month}-{day}"
                        # 处理已经是 YYYY-MM-DD 格式的
                        elif '-' in date_str and len(date_str) == 10:
                            return date_str
                        # 处理 YYYYMMDD 格式
                        elif len(date_str) == 8 and date_str.isdigit():
                            return f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
                        return None
                    except Exception as e:
                        print(f"日期转换错误: {value} -> {e}")
                        return None
                
                # 修复的日期时间处理函数
                def get_datetime_value(value):
                    if pd.isna(value) or value == '' or str(value) == 'nan':
                        return None
                    try:
                        datetime_str = str(value).strip()
                        
                        # 处理 2025/8/4 13:48:40 格式
                        if '/' in datetime_str and ' ' in datetime_str:
                            date_part, time_part = datetime_str.split(' ', 1)
                            parts = date_part.split('/')
                            if len(parts) == 3:
                                year = parts[0].zfill(4)
                                month = parts[1].zfill(2)
                                day = parts[2].zfill(2)
                                return f"{year}-{month}-{day} {time_part}"
                        
                        # 处理只有日期的情况 2025/8/4
                        elif '/' in datetime_str and ' ' not in datetime_str:
                            parts = datetime_str.split('/')
                            if len(parts) == 3:
                                year = parts[0].zfill(4)
                                month = parts[1].zfill(2)
                                day = parts[2].zfill(2)
                                return f"{year}-{month}-{day} 00:00:00"
                        
                        # 处理已经是标准格式的 YYYY-MM-DD HH:MM:SS
                        elif '-' in datetime_str and ' ' in datetime_str:
                            return datetime_str
                        
                        # 处理只有日期的标准格式 YYYY-MM-DD
                        elif '-' in datetime_str and ' ' not in datetime_str and len(datetime_str) == 10:
                            return f"{datetime_str} 00:00:00"
                        
                        return None
                    except Exception as e:
                        print(f"日期时间转换错误: {value} -> {e}")
                        return None
                
                # 构建数据行 - 确保正确的46个字段顺序
                try:
                    data_row = (
                        # 1. STAT_DATE
                        get_date_value(row.get('STAT_DATE')),
                        # 2-6. 基础信息字段
                        get_string_value(row.get('STAT_TYPE')),
                        get_string_value(row.get('STAT_NAME')),
                        get_string_value(row.get('STAT_CODE')),
                        get_string_value(row.get('STAT_CONTENT_RAW')),
                        get_string_value(row.get('STAT_CONTENT_CLEANSE')),
                        # 7-10. 累计进出口（人民币和美元）
                        get_numeric_value(row.get('ACC_A_CNY_AMOUNT')),
                        get_numeric_value(row.get('ACC_A_CNY_YOY')),
                        get_numeric_value(row.get('ACC_A_USD_AMOUNT')),
                        get_numeric_value(row.get('ACC_A_USD_YOY')),
                        # 11-14. 当月进出口（人民币和美元）
                        get_numeric_value(row.get('MON_A_CNY_AMOUNT')),
                        get_numeric_value(row.get('MON_A_CNY_YOY')),
                        get_numeric_value(row.get('MON_A_USD_AMOUNT')),
                        get_numeric_value(row.get('MON_A_USD_YOY')),
                        # 15-18. 累计出口（人民币和美元）
                        get_numeric_value(row.get('ACC_E_CNY_AMOUNT')),
                        get_numeric_value(row.get('ACC_E_CNY_YOY')),
                        get_numeric_value(row.get('ACC_E_USD_AMOUNT')),
                        get_numeric_value(row.get('ACC_E_USD_YOY')),
                        # 19-22. 当月出口（人民币和美元）
                        get_numeric_value(row.get('MON_E_CNY_AMOUNT')),
                        get_numeric_value(row.get('MON_E_CNY_YOY')),
                        get_numeric_value(row.get('MON_E_USD_AMOUNT')),
                        get_numeric_value(row.get('MON_E_USD_YOY')),
                        # 23-26. 累计进口（人民币和美元）
                        get_numeric_value(row.get('ACC_I_CNY_AMOUNT')),
                        get_numeric_value(row.get('ACC_I_CNY_YOY')),
                        get_numeric_value(row.get('ACC_I_USD_AMOUNT')),
                        get_numeric_value(row.get('ACC_I_USD_YOY')),
                        # 27-30. 当月进口（人民币和美元）
                        get_numeric_value(row.get('MON_I_CNY_AMOUNT')),
                        get_numeric_value(row.get('MON_I_CNY_YOY')),
                        get_numeric_value(row.get('MON_I_USD_AMOUNT')),
                        get_numeric_value(row.get('MON_I_USD_YOY')),
                        # 31-33. 累计出口数量
                        get_numeric_value(row.get('ACC_E_AMOUNT')),
                        get_string_value(row.get('ACC_E_AMOUNT_UNIT')),
                        get_numeric_value(row.get('ACC_E_AMOUNT_YOY')),
                        # 34-36. 当月出口数量
                        get_numeric_value(row.get('MON_E_AMOUNT')),
                        get_string_value(row.get('MON_E_AMOUNT_UNIT')),
                        get_numeric_value(row.get('MON_E_AMOUNT_YOY')),
                        # 37-39. 累计进口数量
                        get_numeric_value(row.get('ACC_I_AMOUNT')),
                        get_string_value(row.get('ACC_I_AMOUNT_UNIT')),
                        get_numeric_value(row.get('ACC_I_AMOUNT_YOY')),
                        # 40-42. 当月进口数量
                        get_numeric_value(row.get('MON_I_AMOUNT')),
                        get_string_value(row.get('MON_I_AMOUNT_UNIT')),
                        get_numeric_value(row.get('MON_I_AMOUNT_YOY')),
                        # 43-45. 其他标识字段
                        get_string_value(row.get('RANK_MARKERS')),
                        get_string_value(row.get('DATA_SOURCE')),
                        get_string_value(row.get('EMPHASIS_OR_EMERGING_MARK')),
                        # 46. CREATE_TIME
                        get_datetime_value(row.get('CREATE_TIME'))
                    )
                    
                    # 验证数据行长度
                    if len(data_row) != 46:
                        print(f"错误：数据行长度不正确，期望46个字段，实际{len(data_row)}个字段")
                        print(f"数据行内容: {data_row}")
                        continue
                    
                    batch_data.append(data_row)
                    
                except Exception as e:
                    print(f"构建数据行时出错，行索引 {_}: {e}")
                    continue
            
            # 执行批量插入
            cursor.executemany(insert_sql, batch_data)
            conn.commit()
            
            inserted_rows += len(batch_data)
            progress = (inserted_rows / total_rows) * 100
            print(f"  已插入 {inserted_rows}/{total_rows} 条记录 ({progress:.1f}%)")
        
        print(f"数据插入完成！共插入 {inserted_rows} 条记录")
        
        # 关闭连接
        cursor.close()
        conn.close()
        
        return True, inserted_rows
        
    except Exception as e:
        print(f"错误详细信息: {e}")
        if 'cx_Oracle' in str(type(e)):
            print(f"数据库错误: {e}")
        else:
            print(f"处理错误: {e}")
        return False, 0
    finally:
        try:
            if 'conn' in locals():
                conn.close()
        except:
            pass

def batch_import_beijing_to_oracle(csv_dir, db_config, clear_existing=False):
    """
    批量导入北京CSV文件到Oracle数据库
    """
    if not ORACLE_AVAILABLE:
        print("❌ 无法执行数据库导入：cx_Oracle模块未安装")
        print("请安装cx_Oracle模块后重试：pip install cx_Oracle")
        return
    
    # 获取所有北京CSV文件
    csv_files = []
    for file in os.listdir(csv_dir):
        if file.endswith('.csv') and file.startswith('T_STATISTICAL_CUS_TOTAL_BEIJING_'):
            csv_files.append(file)
    
    if not csv_files:
        print(f"在目录 {csv_dir} 中没有找到北京CSV文件")
        return
    
    print(f"找到 {len(csv_files)} 个北京CSV文件")
    
    # 统计信息
    total_files = 0
    successful_files = 0
    total_records = 0
    failed_files = []
    
    # 可选：清空北京现有数据
    if clear_existing:
        try:
            print("正在清空北京现有数据...")
            conn = cx_Oracle.connect(**db_config)
            cursor = conn.cursor()
            cursor.execute("DELETE FROM T_STATISTICAL_CUS_TOTAL_CS WHERE DATA_SOURCE = '02'")
            deleted_count = cursor.rowcount
            conn.commit()
            cursor.close()
            conn.close()
            print(f"已清空北京现有数据：{deleted_count} 条记录")
        except Exception as e:
            print(f"清空北京数据时出错: {e}")
            return
    
    # 按文件名排序导入
    for csv_file in sorted(csv_files):
        total_files += 1
        
        csv_path = os.path.join(csv_dir, csv_file)
        
        print(f"\n{'='*80}")
        print(f"导入文件 {total_files}/{len(csv_files)}: {csv_file}")
        print(f"{'='*80}")
        
        try:
            success, record_count = import_single_csv_to_oracle(csv_path, db_config)
            if success:
                successful_files += 1
                total_records += record_count
                print(f"✅ 成功导入: {csv_file} ({record_count} 条记录)")
            else:
                failed_files.append(csv_file)
                print(f"❌ 导入失败: {csv_file}")
        except Exception as e:
            print(f"❌ 导入出错 {csv_file}: {e}")
            failed_files.append(csv_file)
    
    print(f"\n{'='*80}")
    print("批量导入完成！")
    print(f"{'='*80}")
    print(f"总文件数: {total_files}")
    print(f"成功导入: {successful_files}")
    print(f"失败文件: {len(failed_files)}")
    print(f"总记录数: {total_records}")
    
    if failed_files:
        print(f"\n失败的文件:")
        for f in failed_files:
            print(f"  - {f}")
    
    # 验证数据
    if successful_files > 0 and ORACLE_AVAILABLE:
        try:
            print("\n正在验证数据...")
            conn = cx_Oracle.connect(**db_config)
            cursor = conn.cursor()
            
            # 检查北京数据总记录数
            cursor.execute("SELECT COUNT(*) FROM T_STATISTICAL_CUS_TOTAL_CS WHERE DATA_SOURCE = '02'")
            beijing_count = cursor.fetchone()[0]
            print(f"数据库中北京数据记录数: {beijing_count}")
            
            # 按统计类型分组统计
            cursor.execute("""
                SELECT STAT_TYPE, STAT_NAME, COUNT(*) 
                FROM T_STATISTICAL_CUS_TOTAL_CS 
                WHERE DATA_SOURCE = '02'
                GROUP BY STAT_TYPE, STAT_NAME 
                ORDER BY STAT_TYPE
            """)
            
            print("\n按统计类型分组统计（北京数据）:")
            for row in cursor.fetchall():
                print(f"  {row[0]} ({row[1]}): {row[2]} 条记录")
            
            # 按日期分组统计（最近几个月）
            cursor.execute("""
                SELECT STAT_DATE, COUNT(*) 
                FROM T_STATISTICAL_CUS_TOTAL_CS 
                WHERE DATA_SOURCE = '02'
                GROUP BY STAT_DATE 
                ORDER BY STAT_DATE DESC
                FETCH FIRST 10 ROWS ONLY
            """)
            
            print("\n按日期分组统计（北京数据，最近10个月）:")
            for row in cursor.fetchall():
                print(f"  {row[0]}: {row[1]} 条记录")
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            print(f"验证数据时出错: {e}")

def debug_csv_structure(csv_file_path):
    """
    调试CSV文件结构
    """
    try:
        df = pd.read_csv(csv_file_path, encoding='utf-8-sig')
        filename = os.path.basename(csv_file_path)
        
        print(f"\n=== 调试CSV文件结构: {filename} ===")
        print(f"行数: {len(df)}")
        print(f"列数: {len(df.columns)}")
        
        print(f"\nCSV文件列名:")
        for i, col in enumerate(df.columns, 1):
            print(f"  {i:2d}. {col}")
        
        # 显示第一行数据
        if len(df) > 0:
            print(f"\n第一行数据:")
            for i, (col, value) in enumerate(zip(df.columns, df.iloc[0]), 1):
                print(f"  {i:2d}. {col}: {repr(value)}")
        
        return True
        
    except Exception as e:
        print(f"调试CSV文件时出错: {e}")
        return False

def debug_database_table_structure(db_config, table_name="T_STATISTICAL_CUS_TOTAL_CS"):
    """
    调试数据库表结构
    """
    if not ORACLE_AVAILABLE:
        print("❌ 无法调试表结构：cx_Oracle模块未安装")
        return False
    
    try:
        conn = cx_Oracle.connect(**db_config)
        cursor = conn.cursor()
        
        # 查询表结构
        cursor.execute(f"""
            SELECT COLUMN_NAME, DATA_TYPE, NULLABLE, COLUMN_ID
            FROM USER_TAB_COLUMNS 
            WHERE TABLE_NAME = '{table_name}'
            ORDER BY COLUMN_ID
        """)
        
        columns = cursor.fetchall()
        
        print(f"\n=== 数据库表结构: {table_name} ===")
        print(f"字段总数: {len(columns)}")
        
        for col in columns:
            print(f"  {col[3]:2d}. {col[0]} ({col[1]}) {'NULL' if col[2] == 'Y' else 'NOT NULL'}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"调试表结构时出错: {e}")
        return False

def check_database_connection(db_config):
    """
    测试数据库连接
    """
    if not ORACLE_AVAILABLE:
        print("❌ 无法测试数据库连接：cx_Oracle模块未安装")
        return False
    
    try:
        print("正在测试数据库连接...")
        conn = cx_Oracle.connect(**db_config)
        cursor = conn.cursor()
        
        # 测试查询
        cursor.execute("SELECT COUNT(*) FROM T_STATISTICAL_CUS_TOTAL_CS")
        total_count = cursor.fetchone()[0]
        
        # 检查表结构
        cursor.execute("""
            SELECT COUNT(*) FROM USER_TAB_COLUMNS 
            WHERE TABLE_NAME = 'T_STATISTICAL_CUS_TOTAL_CS'
        """)
        column_count = cursor.fetchone()[0]
        
        cursor.close()
        conn.close()
        
        print(f"✅ 数据库连接成功！")
        print(f"表 T_STATISTICAL_CUS_TOTAL_CS 当前记录数: {total_count}")
        print(f"表字段数: {column_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def test_date_conversion():
    """
    测试日期转换功能
    """
    print("测试日期转换功能...")
    
    test_cases = [
        "2025/1/1",
        "2025/01/01", 
        "2025/8/4 13:48:40",
        "2025/08/04 13:48:40",
        "2025-01-01",
        "2025-08-04 13:48:40",
        "",
        None,
        "nan"
    ]
    
    def get_date_value(value):
        if pd.isna(value) or value == '' or str(value) == 'nan':
            return None
        try:
            date_str = str(value).strip()
            if '/' in date_str:
                parts = date_str.split('/')
                if len(parts) == 3:
                    year = parts[0].zfill(4)
                    month = parts[1].zfill(2)
                    day = parts[2].zfill(2)
                    return f"{year}-{month}-{day}"
            elif '-' in date_str and len(date_str) == 10:
                return date_str
            elif len(date_str) == 8 and date_str.isdigit():
                return f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
            return None
        except Exception as e:
            print(f"日期转换错误: {value} -> {e}")
            return None
    
    def get_datetime_value(value):
        if pd.isna(value) or value == '' or str(value) == 'nan':
            return None
        try:
            datetime_str = str(value).strip()
            
            if '/' in datetime_str and ' ' in datetime_str:
                date_part, time_part = datetime_str.split(' ', 1)
                parts = date_part.split('/')
                if len(parts) == 3:
                    year = parts[0].zfill(4)
                    month = parts[1].zfill(2)
                    day = parts[2].zfill(2)
                    return f"{year}-{month}-{day} {time_part}"
            
            elif '/' in datetime_str and ' ' not in datetime_str:
                parts = datetime_str.split('/')
                if len(parts) == 3:
                    year = parts[0].zfill(4)
                    month = parts[1].zfill(2)
                    day = parts[2].zfill(2)
                    return f"{year}-{month}-{day} 00:00:00"
            
            elif '-' in datetime_str and ' ' in datetime_str:
                return datetime_str
            
            elif '-' in datetime_str and ' ' not in datetime_str and len(datetime_str) == 10:
                return f"{datetime_str} 00:00:00"
            
            return None
        except Exception as e:
            print(f"日期时间转换错误: {value} -> {e}")
            return None
    
    for test_case in test_cases:
        if ' ' in str(test_case):
            result = get_datetime_value(test_case)
            print(f"DateTime: '{test_case}' -> '{result}'")
        else:
            result = get_date_value(test_case)
            print(f"Date: '{test_case}' -> '{result}'")

def main():
    """
    主函数
    """
    # 数据库连接配置（与浙江相同）
    db_config = {
        'user': 'manifest_dcb',
        'password': 'manifest_dcb',
        'dsn': '192.168.1.151/TEST'
    }
    
    # CSV文件目录
    csv_directory = "北京海关数据_完整转换结果"
    
    print("=" * 100)
    print("北京海关统计数据批量导入Oracle数据库 - 修复版")
    print("=" * 100)
    print(f"CSV目录: {csv_directory}")
    print(f"数据库: {db_config['dsn']}")
    print(f"用户: {db_config['user']}")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 测试日期转换功能
    test_date_conversion()
    print()
    
    # 检查CSV目录
    if not os.path.exists(csv_directory):
        print(f"❌ 错误：CSV目录不存在 - {csv_directory}")
        print("请先运行 beijing_customs_comprehensive_import.py 生成CSV文件")
        return
    
    # 测试数据库连接
    print("第一步：测试数据库连接")
    print("-" * 50)
    if not check_database_connection(db_config):
        print("❌ 数据库连接失败，请检查配置")
        return
    
    # 调试数据库表结构
    print("\n第二步：调试数据库表结构")
    print("-" * 50)
    debug_database_table_structure(db_config)
    
    # 找一个示例CSV文件进行调试
    print("\n第三步：调试CSV文件结构")
    print("-" * 50)
    csv_files = [f for f in os.listdir(csv_directory) if f.endswith('.csv') and f.startswith('T_STATISTICAL_CUS_TOTAL_BEIJING_')]
    if csv_files:
        sample_csv = os.path.join(csv_directory, csv_files[0])
        debug_csv_structure(sample_csv)
    
    # 询问是否清空现有北京数据
    print(f"\n第四步：数据导入")
    print("-" * 50)
    
    # 默认不清空现有数据，追加导入
    clear_existing = False
    print(f"导入模式: {'清空后导入' if clear_existing else '追加导入'}")
    
    # 批量导入
    batch_import_beijing_to_oracle(csv_directory, db_config, clear_existing)
    
    print(f"\n结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 100)
    
    if ORACLE_AVAILABLE:
        print("北京海关数据导入完成！")
        print("您现在可以在Oracle数据库表 T_STATISTICAL_CUS_TOTAL_CS 中查询北京海关数据")
        print("北京数据标识：DATA_SOURCE = '02'")
    else:
        print("请安装cx_Oracle模块后重新运行以完成数据库导入")
    
    
    print("=" * 100)
    
    if ORACLE_AVAILABLE:
        print("北京海关数据导入完成！")
        print("您现在可以在Oracle数据库表 T_STATISTICAL_CUS_TOTAL_CS 中查询北京海关数据")
        print("北京数据标识：DATA_SOURCE = '02'")
    else:
        print("请安装cx_Oracle模块后重新运行以完成数据库导入")
    
    print("=" * 100)

if __name__ == '__main__':
    main()