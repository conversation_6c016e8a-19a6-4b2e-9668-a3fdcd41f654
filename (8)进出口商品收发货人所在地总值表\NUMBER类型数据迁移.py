import cx_Oracle as oracle
import logging
import os
import configparser
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    filename='number_data_sync.log'
)
logger = logging.getLogger('data_sync')

# 获取配置文件路径
script_dir = os.path.dirname(os.path.abspath(__file__))
config_path = os.path.join(script_dir, 'db_config.ini')

def get_db_config(section):
    """从配置文件读取数据库配置"""
    if not os.path.exists(config_path):
        # 创建默认配置文件
        config = configparser.ConfigParser()
        config['TEST_DB'] = {
            'username': 'manifest',
            'password': 'manifest',
            'host': 'ip',
            'port': '1522',
            'service_name': 'TEST'
        }
        config['PROD_DB'] = {
            'username': 'manifest2',
            'password': 'manifest',
            'host': 'ip',
            'port': '1521',
            'service_name': 'test2'
        }
        with open(config_path, 'w') as f:
            config.write(f)
        print(f"已创建配置文件: {config_path}")
        
    config = configparser.ConfigParser()
    config.read(config_path)
    
    if section not in config:
        raise ValueError(f"配置文件中未找到 {section} 节")
    
    return {
        'username': config[section]['username'],
        'password': config[section]['password'],
        'host': config[section]['host'],
        'port': config[section]['port'],
        'service_name': config[section]['service_name']
    }

def get_connection_string(db_config):
    """根据配置生成数据库连接字符串"""
    return f"{db_config['username']}/{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['service_name']}"

def check_table_exists(table_name, conn):
    """检查表是否存在"""
    cursor = conn.cursor()
    try:
        cursor.execute(f"""
        SELECT COUNT(*) FROM user_tables WHERE table_name = '{table_name.upper()}'
        """)
        result = cursor.fetchone()[0]
        return result > 0
    finally:
        cursor.close()

def get_column_types(table_name, conn):
    """获取表中各列的数据类型"""
    cursor = conn.cursor()
    try:
        cursor.execute(f"""
        SELECT column_name, data_type
        FROM all_tab_columns 
        WHERE table_name = '{table_name.upper()}'
        ORDER BY column_id
        """)
        return {row[0]: row[1] for row in cursor.fetchall()}
    finally:
        cursor.close()

def create_table_like_source(source_table, target_table, source_conn, target_conn):
    """根据源表创建目标表"""
    # 使用CTAS (Create Table As Select)语法来创建表
    # 这样可以确保表结构完全一致
    target_cursor = target_conn.cursor()
    try:
        # 先删除表（如果存在）
        try:
            target_cursor.execute(f"DROP TABLE {target_table}")
            target_conn.commit()
            print(f"已删除目标表 {target_table}")
        except:
            # 表不存在，忽略错误
            target_conn.rollback()
        
        # 创建表结构（不包含数据）
        create_sql = f"""
        CREATE TABLE {target_table} AS 
        SELECT * FROM {source_table}@{get_db_link_name()} 
        WHERE 1=0
        """
        
        # 执行创建表
        print("开始创建表...")
        print(f"SQL: {create_sql}")
        target_cursor.execute(create_sql)
        target_conn.commit()
        print(f"成功创建表 {target_table}")
        
        # 获取表上的默认值信息并应用
        apply_default_values(source_table, target_table, source_conn, target_conn)
        
        return True
    except Exception as e:
        target_conn.rollback()
        print(f"创建表失败: {str(e)}")
        logger.error(f"创建表失败: {str(e)}")
        return False
    finally:
        target_cursor.close()

def get_db_link_name():
    """获取或创建数据库链接名称"""
    return "TEST_TO_PROD_LINK"

def create_database_link(source_db_config, target_conn):
    """创建从目标数据库到源数据库的链接"""
    cursor = target_conn.cursor()
    try:
        # 先删除已存在的链接
        try:
            cursor.execute(f"DROP DATABASE LINK {get_db_link_name()}")
            target_conn.commit()
        except:
            target_conn.rollback()
            
        # 创建数据库链接
        create_link_sql = f"""
        CREATE DATABASE LINK {get_db_link_name()}
        CONNECT TO {source_db_config['username']} IDENTIFIED BY {source_db_config['password']}
        USING '(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST={source_db_config['host']})(PORT={source_db_config['port']}))(CONNECT_DATA=(SERVICE_NAME={source_db_config['service_name']})))'
        """
        cursor.execute(create_link_sql)
        target_conn.commit()
        print(f"成功创建数据库链接 {get_db_link_name()}")
        return True
    except Exception as e:
        target_conn.rollback()
        print(f"创建数据库链接失败: {str(e)}")
        logger.error(f"创建数据库链接失败: {str(e)}")
        return False
    finally:
        cursor.close()

def apply_default_values(source_table, target_table, source_conn, target_conn):
    """应用默认值约束"""
    source_cursor = source_conn.cursor()
    target_cursor = target_conn.cursor()
    try:
        # 获取源表的默认值信息
        source_cursor.execute(f"""
        SELECT column_name, data_default
        FROM all_tab_columns
        WHERE table_name = '{source_table.upper()}'
        AND data_default IS NOT NULL
        """)
        default_values = source_cursor.fetchall()
        
        # 应用默认值到目标表
        for col_name, default_value in default_values:
            try:
                alter_sql = f"ALTER TABLE {target_table} MODIFY {col_name} DEFAULT {default_value}"
                target_cursor.execute(alter_sql)
                target_conn.commit()
                print(f"为{col_name}列设置默认值: {default_value}")
            except Exception as e:
                target_conn.rollback()
                print(f"设置默认值失败 ({col_name}): {str(e)}")
                logger.error(f"设置默认值失败 ({col_name}): {str(e)}")
    finally:
        source_cursor.close()
        target_cursor.close()

def validate_number_fields(source_table, source_conn):
    """验证NUMBER字段中的数据格式"""
    source_cursor = source_conn.cursor()
    try:
        # 获取所有NUMBER类型的列
        source_cursor.execute(f"""
        SELECT column_name
        FROM all_tab_columns
        WHERE table_name = '{source_table.upper()}'
        AND data_type = 'NUMBER'
        """)
        number_columns = [row[0] for row in source_cursor.fetchall()]
        
        if not number_columns:
            print("未找到NUMBER类型列")
            return True
            
        # 检查每个NUMBER列中是否有无效值
        for col in number_columns:
            try:
                check_sql = f"""
                SELECT COUNT(*) FROM {source_table}
                WHERE {col} IS NOT NULL 
                AND NOT REGEXP_LIKE(TO_CHAR({col}), '^[+-]?\\d*\\.?\\d*$')
                """
                source_cursor.execute(check_sql)
                invalid_count = source_cursor.fetchone()[0]
                
                if invalid_count > 0:
                    print(f"警告: 列 {col} 中有 {invalid_count} 行包含无效的NUMBER格式")
                else:
                    print(f"列 {col} 中的数据格式有效")
            except Exception as e:
                print(f"检查列 {col} 时出错: {str(e)}")
        
        return True
    except Exception as e:
        print(f"验证NUMBER字段失败: {str(e)}")
        logger.error(f"验证NUMBER字段失败: {str(e)}")
        return False
    finally:
        source_cursor.close()

def direct_insert_select(source_table, target_table, target_conn, where_clause=None):
    """使用INSERT...SELECT直接从源表插入数据到目标表"""
    target_cursor = target_conn.cursor()
    try:
        # 构建WHERE子句
        where_condition = f"WHERE {where_clause}" if where_clause else ""
        
        # 构建INSERT语句
        insert_sql = f"""
        INSERT INTO {target_table}
        SELECT * FROM {source_table}@{get_db_link_name()}
        {where_condition}
        """
        
        print(f"执行SQL: {insert_sql}")
        target_cursor.execute(insert_sql)
        rows_affected = target_cursor.rowcount
        target_conn.commit()
        
        print(f"成功插入 {rows_affected} 行数据")
        return rows_affected
    except Exception as e:
        target_conn.rollback()
        print(f"直接插入数据失败: {str(e)}")
        logger.error(f"直接插入数据失败: {str(e)}")
        return 0
    finally:
        target_cursor.close()

def sync_data(source_table, target_table, where_clause=None, is_first_sync=False):
    """同步数据从测试环境到生产环境"""
    print(f"开始同步数据: {source_table} -> {target_table}")
    print(f"条件: {where_clause if where_clause else '全量数据'}")
    
    start_time = datetime.now()
    
    # 连接数据库
    source_conn = None
    target_conn = None
    
    try:
        # 连接源数据库（测试环境）
        test_db_config = get_db_config('TEST_DB')
        source_conn = oracle.connect(get_connection_string(test_db_config))
        print("已连接到测试环境数据库")
        
        # 连接目标数据库（生产环境）
        prod_db_config = get_db_config('PROD_DB')
        target_conn = oracle.connect(get_connection_string(prod_db_config))
        print("已连接到生产环境数据库")
        
        # 设置NLS参数确保一致的数字格式
        source_cursor = source_conn.cursor()
        target_cursor = target_conn.cursor()
        
        try:
            # 设置相同的NLS参数
            nls_sql = "ALTER SESSION SET NLS_NUMERIC_CHARACTERS = '. '"
            source_cursor.execute(nls_sql)
            target_cursor.execute(nls_sql)
            source_conn.commit()
            target_conn.commit()
            print("已设置一致的数字格式参数")
        finally:
            source_cursor.close()
            target_cursor.close()
        
        # 验证NUMBER字段
        print("验证源表的NUMBER字段...")
        validate_number_fields(source_table, source_conn)
        
        # 创建数据库链接（如果需要）
        print("创建数据库链接...")
        if not create_database_link(test_db_config, target_conn):
            raise ValueError("创建数据库链接失败，无法继续")
        
        # 检查目标表是否存在，不存在则创建
        table_exists = check_table_exists(target_table, target_conn)
        if not table_exists:
            print(f"目标表 {target_table} 不存在，正在创建...")
            success = create_table_like_source(source_table, target_table, source_conn, target_conn)
            if not success:
                raise ValueError("创建表失败，中止同步")
        
        # 如果是第一次同步，先清空目标表
        if is_first_sync and table_exists:
            target_cursor = target_conn.cursor()
            try:
                target_cursor.execute(f"DELETE FROM {target_table}")
                target_conn.commit()
                print(f"已清空目标表 {target_table}")
            except Exception as e:
                target_conn.rollback()
                print(f"清空表失败: {str(e)}")
            finally:
                target_cursor.close()
        
        # 直接使用INSERT...SELECT语句插入数据
        rows_affected = direct_insert_select(source_table, target_table, target_conn, where_clause)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        print(f"同步完成! 总共插入 {rows_affected} 行数据")
        print(f"开始时间: {start_time}, 结束时间: {end_time}, 耗时: {duration} 秒")
            
    except Exception as e:
        print(f"同步过程中发生错误: {str(e)}")
        logger.error(f"同步过程中发生错误: {str(e)}")
    finally:
        if source_conn:
            source_conn.close()
        if target_conn:
            target_conn.close()

if __name__ == "__main__":
    # 简单交互式配置
    print("=== NUMBER类型数据同步工具 ===")
    print("1. 全量同步 - 第一次操作")
    print("2. 增量同步 - 后续操作")
    choice = input("请选择操作类型 [1/2]: ") or "1"
    
    if choice == "1":
        # 全量同步
        source_table = input("请输入源表名称 [CS_SINGLEWINDOW_PRODUCT_CODE]: ") or "CS_SINGLEWINDOW_PRODUCT_CODE"
        target_table = input("请输入目标表名称 [T_BC_SW_TS_CODE]: ") or "T_BC_SW_TS_CODE"
        sync_data(source_table, target_table, is_first_sync=True)
    else:
        # 增量同步
        source_table = input("请输入源表名称 [CS_SINGLEWINDOW_PRODUCT_CODE]: ") or "CS_SINGLEWINDOW_PRODUCT_CODE"
        target_table = input("请输入目标表名称 [T_BC_SW_TS_CODE]: ") or "T_BC_SW_TS_CODE"
        
        # 默认使用createtime > 最近12小时的数据
        where_clause = input("请输入过滤条件 [createtime > sysdate - 0.5]: ") or "createtime > sysdate - 0.5"
        sync_data(source_table, target_table, where_clause=where_clause, is_first_sync=False) 