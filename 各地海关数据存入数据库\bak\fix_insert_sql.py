"""
修正INSERT SQL语句，使用正确的字段顺序
"""

def get_correct_field_order():
    """
    根据CSV结构文件返回正确的字段顺序
    """
    # 从T_STATISTICAL_CUS_TOTAL_CS结构和注释.csv中提取的字段顺序
    fields_from_csv = [
        'ACC_A_CNY_AMOUNT',      # 2
        'ACC_A_CNY_YOY',         # 3
        'ACC_A_USD_AMOUNT',      # 4
        'ACC_A_USD_YOY',         # 5
        'ACC_E_AMOUNT',          # 6
        'ACC_E_AMOUNT_UNIT',     # 7
        'ACC_E_AMOUNT_YOY',      # 8
        'ACC_E_CNY_AMOUNT',      # 9
        'ACC_E_CNY_YOY',         # 10
        'ACC_E_USD_AMOUNT',      # 11
        'ACC_E_USD_YOY',         # 12
        'ACC_I_AMOUNT',          # 13
        'ACC_I_AMOUNT_UNIT',     # 14
        'ACC_I_AMOUNT_YOY',      # 15
        'ACC_I_CNY_AMOUNT',      # 16
        'ACC_I_CNY_YOY',         # 17
        'ACC_I_USD_AMOUNT',      # 18
        'ACC_I_USD_YOY',         # 19
        'CREATE_TIME',           # 20
        'DATA_SOURCE',           # 21
        'EMPHASIS_OR_EMERGING_MARK', # 29
        'MON_A_CNY_AMOUNT',      # 31
        'MON_A_CNY_YOY',         # 32
        'MON_A_USD_AMOUNT',      # 33
        'MON_A_USD_YOY',         # 34
        'MON_E_AMOUNT',          # 35
        'MON_E_AMOUNT_UNIT',     # 36
        'MON_E_AMOUNT_YOY',      # 37
        'MON_E_CNY_AMOUNT',      # 38
        'MON_E_CNY_YOY',         # 39
        'MON_E_USD_AMOUNT',      # 40
        'MON_E_USD_YOY',         # 41
        'MON_I_AMOUNT',          # 42
        'MON_I_AMOUNT_UNIT',     # 43
        'MON_I_AMOUNT_YOY',      # 44
        'MON_I_CNY_AMOUNT',      # 45
        'MON_I_CNY_YOY',         # 46
        'MON_I_USD_AMOUNT',      # 47
        'MON_I_USD_YOY',         # 48
        'RANK_MARKERS',          # 49
        'STAT_CODE',             # 52
        'STAT_CONTENT_CLEANSE',  # 53
        'STAT_CONTENT_RAW',      # 54
        'STAT_DATE',             # 55
        'STAT_NAME',             # 56
        'STAT_TYPE'              # 57
    ]
    
    return fields_from_csv

def generate_correct_insert_sql():
    """
    生成正确的INSERT SQL语句
    """
    fields = get_correct_field_order()
    placeholders = [f":{i+1}" for i in range(len(fields))]
    
    insert_sql = f"""INSERT INTO T_STATISTICAL_CUS_TOTAL_CS (
    {', '.join(fields)}
) VALUES (
    {', '.join(placeholders)}
)"""
    
    return insert_sql, fields

def main():
    print("生成正确的INSERT SQL语句")
    print("=" * 80)
    
    insert_sql, fields = generate_correct_insert_sql()
    
    print("字段顺序：")
    for i, field in enumerate(fields, 1):
        print(f"{i:2d}. {field}")
    
    print(f"\n总字段数: {len(fields)}")
    
    print("\nINSERT SQL语句：")
    print(insert_sql)
    
    # 保存到文件
    with open('correct_insert_sql.txt', 'w', encoding='utf-8') as f:
        f.write("字段顺序：\n")
        for i, field in enumerate(fields, 1):
            f.write(f"{i:2d}. {field}\n")
        f.write(f"\nINSERT SQL语句：\n")
        f.write(insert_sql)
    
    print(f"\n已保存到: correct_insert_sql.txt")

if __name__ == '__main__':
    main()