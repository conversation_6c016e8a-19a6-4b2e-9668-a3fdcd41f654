# Oracle客户端配置说明

## 安装依赖

在运行Python脚本之前，请确保安装以下依赖：

```bash
pip install cx_Oracle pandas
```

## Oracle客户端配置

由于cx_Oracle需要Oracle客户端库，请确保系统已安装Oracle Instant Client：

### Windows系统：
1. 下载Oracle Instant Client Basic包
2. 解压到某个目录（如：C:\oracle\instantclient_19_10）
3. 将该目录添加到系统PATH环境变量

### Linux系统：
```bash
# 安装Oracle Instant Client
sudo apt-get install libaio1
wget https://download.oracle.com/otn_software/linux/instantclient/196000/oracle-instantclient19.10-basic-*********.0-1.x86_64.rpm
sudo rpm -ivh oracle-instantclient19.10-basic-*********.0-1.x86_64.rpm

# 或者使用DEB包
sudo apt-get install alien
sudo alien oracle-instantclient19.10-basic-*********.0-1.x86_64.rpm
sudo dpkg -i oracle-instantclient19.10-basic_*********.0-2_amd64.deb
```

## 环境变量配置

### Windows：
```cmd
set PATH=C:\oracle\instantclient_19_10;%PATH%
set ORACLE_HOME=C:\oracle\instantclient_19_10
```

### Linux：
```bash
export LD_LIBRARY_PATH=/usr/lib/oracle/19.10/client64/lib:$LD_LIBRARY_PATH
export ORACLE_HOME=/usr/lib/oracle/19.10/client64
```

## 运行脚本

配置完成后，运行导入脚本：

```bash
python import_to_oracle.py
```

## 常见问题解决

1. **DLL加载失败**：确保Oracle Instant Client路径正确
2. **连接失败**：检查网络连接和数据库服务状态
3. **权限问题**：确保数据库用户有插入权限

## 验证导入

导入完成后，可以在Oracle数据库中执行以下查询验证数据：

```sql
-- 检查总记录数
SELECT COUNT(*) FROM T_STATISTICAL_CUS_TOTAL_CS;

-- 检查数据分布
SELECT STAT_TYPE, STAT_NAME, COUNT(*) 
FROM T_STATISTICAL_CUS_TOTAL_CS 
GROUP BY STAT_TYPE, STAT_NAME 
ORDER BY STAT_TYPE;

-- 检查数据源
SELECT DATA_SOURCE, COUNT(*) 
FROM T_STATISTICAL_CUS_TOTAL_CS 
GROUP BY DATA_SOURCE;
```