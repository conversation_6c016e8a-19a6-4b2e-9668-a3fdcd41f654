#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
海关数据分析脚本 - 提取贸易方式数据
用于从进出口商品贸易方式总值表中提取特定贸易方式的数据，生成"贸易方式情况"数据
单位: 统一为亿元，数据四舍五入到一位小数
"""

import os
import pandas as pd
import numpy as np
import re

def read_march_zhejiang_data():
    """
    读取浙江3月数据
    """
    march_file = r"20250530\2025年1-4月 -浙江\2025年1-3月浙江省进出口情况 (2).xlsx"
    
    if not os.path.exists(march_file):
        print(f"浙江3月数据文件不存在: {march_file}")
        return None
    
    print(f"读取浙江3月数据: {march_file}")
    
    try:
        # 读取数据
        df = pd.read_excel(march_file, sheet_name='贸易方式')
        
        # 浙江数据格式：第2列是进出口额，第5列是进出口同比
        import_export_col = 1  # 进出口额列
        yoy_col = 4  # 同比列
        
        # 提取各贸易方式的3月数据
        march_data = {}
        
        # 总值（合计）
        for i in range(len(df)):
            cell_value = str(df.iloc[i, 0]).strip()
            if cell_value == '合计':
                # 累计数据
                cumulative_value = df.iloc[i, import_export_col]
                try:
                    if isinstance(cumulative_value, str):
                        cumulative_value = cumulative_value.replace(',', '')
                    march_data['total_cumulative'] = float(cumulative_value)
                except:
                    march_data['total_cumulative'] = 0
                
                # 累计同比
                cumulative_yoy = df.iloc[i, yoy_col]
                try:
                    if isinstance(cumulative_yoy, str):
                        cumulative_yoy = cumulative_yoy.replace('%', '')
                    march_data['total_cumulative_yoy'] = float(cumulative_yoy)
                except:
                    march_data['total_cumulative_yoy'] = 0
                break
        
        # 一般贸易
        for i in range(len(df)):
            cell_value = str(df.iloc[i, 0]).strip()
            if '一般贸易' in cell_value:
                # 累计数据
                cumulative_value = df.iloc[i, import_export_col]
                try:
                    if isinstance(cumulative_value, str):
                        cumulative_value = cumulative_value.replace(',', '')
                    march_data['general_trade_cumulative'] = float(cumulative_value)
                except:
                    march_data['general_trade_cumulative'] = 0
                
                # 累计同比
                cumulative_yoy = df.iloc[i, yoy_col]
                try:
                    if isinstance(cumulative_yoy, str):
                        cumulative_yoy = cumulative_yoy.replace('%', '')
                    march_data['general_trade_cumulative_yoy'] = float(cumulative_yoy)
                except:
                    march_data['general_trade_cumulative_yoy'] = 0
                break
        
        # 加工贸易（来料加工+进料加工）
        processing_trade_cumulative = 0
        processing_trade_yoy = 0
        processing_count = 0
        
        for i in range(len(df)):
            cell_value = str(df.iloc[i, 0]).strip()
            if '来料加工' in cell_value or '进料加工' in cell_value:
                # 累计数据
                cumulative_value = df.iloc[i, import_export_col]
                try:
                    if isinstance(cumulative_value, str):
                        cumulative_value = cumulative_value.replace(',', '')
                    processing_trade_cumulative += float(cumulative_value)
                except:
                    pass
                
                # 累计同比（取平均值）
                cumulative_yoy = df.iloc[i, yoy_col]
                try:
                    if isinstance(cumulative_yoy, str):
                        cumulative_yoy = cumulative_yoy.replace('%', '')
                    if cumulative_yoy != '*':
                        processing_trade_yoy += float(cumulative_yoy)
                        processing_count += 1
                except:
                    pass
        
        march_data['processing_trade_cumulative'] = processing_trade_cumulative
        march_data['processing_trade_cumulative_yoy'] = processing_trade_yoy / processing_count if processing_count > 0 else 0
        
        # 保税贸易（保税监管场所进出境货物 + 海关特殊监管区域物流货物）
        bonded_trade_cumulative = 0
        bonded_trade_yoy = 0
        bonded_count = 0
        
        for i in range(len(df)):
            cell_value = str(df.iloc[i, 0]).strip()
            if '保税监管场所' in cell_value or '海关特殊监管区域' in cell_value:
                # 累计数据
                cumulative_value = df.iloc[i, import_export_col]
                try:
                    if isinstance(cumulative_value, str):
                        cumulative_value = cumulative_value.replace(',', '')
                    bonded_trade_cumulative += float(cumulative_value)
                except:
                    pass
                
                # 累计同比（取平均值）
                cumulative_yoy = df.iloc[i, yoy_col]
                try:
                    if isinstance(cumulative_yoy, str):
                        cumulative_yoy = cumulative_yoy.replace('%', '')
                    if cumulative_yoy != '*':
                        bonded_trade_yoy += float(cumulative_yoy)
                        bonded_count += 1
                except:
                    pass
        
        march_data['bonded_trade_cumulative'] = bonded_trade_cumulative
        march_data['bonded_trade_cumulative_yoy'] = bonded_trade_yoy / bonded_count if bonded_count > 0 else 0
        
        return march_data
        
    except Exception as e:
        print(f"读取浙江3月数据时出错: {e}")
        return None

def read_march_jiangsu_data():
    """
    读取江苏3月数据，包括单月数据、累计数据和累计同比
    """
    # 3月数据文件路径
    march_file = r"20250530\2025年1-4月 - 江苏\2025年3月江苏省主要进出口数据.xls"
    
    if not os.path.exists(march_file):
        print(f"3月数据文件不存在: {march_file}")
        return None
    
    print(f"读取江苏3月数据: {march_file}")
    
    try:
        # 读取数据
        df = pd.read_excel(march_file, sheet_name='贸易方式')
        
        # 江苏数据格式：第2列是单月数据，第8列是累计数据（人民币），第9列是累计同比
        monthly_col = 1  # 单月数据列
        cumulative_col = 7  # 累计数据列（人民币）
        cumulative_yoy_col = 8  # 累计同比列
        
        # 提取各贸易方式的3月数据
        march_data = {}
        
        # 总值
        for i in range(len(df)):
            cell_value = str(df.iloc[i, 0]).strip()
            if cell_value == '总值':
                # 单月数据
                monthly_value = df.iloc[i, monthly_col]
                try:
                    if isinstance(monthly_value, str):
                        monthly_value = monthly_value.replace(',', '')
                    march_data['total'] = float(monthly_value)
                except:
                    march_data['total'] = 0
                
                # 累计数据
                cumulative_value = df.iloc[i, cumulative_col]
                try:
                    if isinstance(cumulative_value, str):
                        cumulative_value = cumulative_value.replace(',', '')
                    march_data['total_cumulative'] = float(cumulative_value)
                except:
                    march_data['total_cumulative'] = 0
                
                # 累计同比
                cumulative_yoy = df.iloc[i, cumulative_yoy_col]
                try:
                    if isinstance(cumulative_yoy, str):
                        cumulative_yoy = cumulative_yoy.replace('%', '')
                    march_data['total_cumulative_yoy'] = float(cumulative_yoy)
                except:
                    march_data['total_cumulative_yoy'] = 0
                break
        
        # 一般贸易
        for i in range(len(df)):
            cell_value = str(df.iloc[i, 0]).strip()
            if cell_value == '一般贸易':
                # 单月数据
                monthly_value = df.iloc[i, monthly_col]
                try:
                    if isinstance(monthly_value, str):
                        monthly_value = monthly_value.replace(',', '')
                    march_data['general_trade'] = float(monthly_value)
                except:
                    march_data['general_trade'] = 0
                
                # 累计数据
                cumulative_value = df.iloc[i, cumulative_col]
                try:
                    if isinstance(cumulative_value, str):
                        cumulative_value = cumulative_value.replace(',', '')
                    march_data['general_trade_cumulative'] = float(cumulative_value)
                except:
                    march_data['general_trade_cumulative'] = 0
                
                # 累计同比
                cumulative_yoy = df.iloc[i, cumulative_yoy_col]
                try:
                    if isinstance(cumulative_yoy, str):
                        cumulative_yoy = cumulative_yoy.replace('%', '')
                    march_data['general_trade_cumulative_yoy'] = float(cumulative_yoy)
                except:
                    march_data['general_trade_cumulative_yoy'] = 0
                break
        
        # 加工贸易
        for i in range(len(df)):
            cell_value = str(df.iloc[i, 0]).strip()
            if cell_value == '加工贸易':
                # 单月数据
                monthly_value = df.iloc[i, monthly_col]
                try:
                    if isinstance(monthly_value, str):
                        monthly_value = monthly_value.replace(',', '')
                    march_data['processing_trade'] = float(monthly_value)
                except:
                    march_data['processing_trade'] = 0
                
                # 累计数据
                cumulative_value = df.iloc[i, cumulative_col]
                try:
                    if isinstance(cumulative_value, str):
                        cumulative_value = cumulative_value.replace(',', '')
                    march_data['processing_trade_cumulative'] = float(cumulative_value)
                except:
                    march_data['processing_trade_cumulative'] = 0
                
                # 累计同比
                cumulative_yoy = df.iloc[i, cumulative_yoy_col]
                try:
                    if isinstance(cumulative_yoy, str):
                        cumulative_yoy = cumulative_yoy.replace('%', '')
                    march_data['processing_trade_cumulative_yoy'] = float(cumulative_yoy)
                except:
                    march_data['processing_trade_cumulative_yoy'] = 0
                break
        
        # 保税物流
        for i in range(len(df)):
            cell_value = str(df.iloc[i, 0]).strip()
            if cell_value == '保税物流':
                # 单月数据
                monthly_value = df.iloc[i, monthly_col]
                try:
                    if isinstance(monthly_value, str):
                        monthly_value = monthly_value.replace(',', '')
                    march_data['bonded_trade'] = float(monthly_value)
                except:
                    march_data['bonded_trade'] = 0
                
                # 累计数据
                cumulative_value = df.iloc[i, cumulative_col]
                try:
                    if isinstance(cumulative_value, str):
                        cumulative_value = cumulative_value.replace(',', '')
                    march_data['bonded_trade_cumulative'] = float(cumulative_value)
                except:
                    march_data['bonded_trade_cumulative'] = 0
                
                # 累计同比
                cumulative_yoy = df.iloc[i, cumulative_yoy_col]
                try:
                    if isinstance(cumulative_yoy, str):
                        cumulative_yoy = cumulative_yoy.replace('%', '')
                    march_data['bonded_trade_cumulative_yoy'] = float(cumulative_yoy)
                except:
                    march_data['bonded_trade_cumulative_yoy'] = 0
                break
        
        print(f"3月数据读取成功:")
        print(f"  总值: 单月={march_data.get('total', 0)}, 累计={march_data.get('total_cumulative', 0)}, 累计同比={march_data.get('total_cumulative_yoy', 0)}%")
        print(f"  一般贸易: 单月={march_data.get('general_trade', 0)}, 累计={march_data.get('general_trade_cumulative', 0)}, 累计同比={march_data.get('general_trade_cumulative_yoy', 0)}%")
        print(f"  加工贸易: 单月={march_data.get('processing_trade', 0)}, 累计={march_data.get('processing_trade_cumulative', 0)}, 累计同比={march_data.get('processing_trade_cumulative_yoy', 0)}%")
        print(f"  保税物流: 单月={march_data.get('bonded_trade', 0)}, 累计={march_data.get('bonded_trade_cumulative', 0)}, 累计同比={march_data.get('bonded_trade_cumulative_yoy', 0)}%")
        return march_data
        
    except Exception as e:
        print(f"读取3月数据时出错: {e}")
        return None

def calculate_monthly_yoy(april_value, march_value):
    """
    计算月度环比增长率（4月相对于3月）
    """
    if march_value == 0:
        return 0
    return round((april_value / march_value - 1) * 100, 1)

def calculate_correct_monthly_yoy(current_month_value, current_cumulative_value, current_cumulative_yoy, march_cumulative_value, march_cumulative_yoy):
    """
    正确计算月度同比增长率
    
    Args:
        current_month_value: 当月（4月）单月数值
        current_cumulative_value: 当月（4月）累计数值
        current_cumulative_yoy: 当月（4月）累计同比增长率
        march_cumulative_value: 3月累计数值
        march_cumulative_yoy: 3月累计同比增长率
    
    Returns:
        月度同比增长率（百分比）
    """
    try:
        # 计算去年4月累计数值
        last_year_april_cumulative = current_cumulative_value / (1 + current_cumulative_yoy / 100)
        
        # 计算去年3月累计数值
        last_year_march_cumulative = march_cumulative_value / (1 + march_cumulative_yoy / 100)
        
        # 计算去年4月单月数值
        last_year_april_monthly = last_year_april_cumulative - last_year_march_cumulative
        
        # 计算4月单月同比增长率
        if last_year_april_monthly <= 0:
            return 0
        
        monthly_yoy = (current_month_value / last_year_april_monthly - 1) * 100
        return round(monthly_yoy, 1)
        
    except Exception as e:
        print(f"计算月度同比增长率时出错: {e}")
        return 0

def detect_table_format(file_path, sheet_name=None):
    """
    检测Excel文件的表格格式
    
    Args:
        file_path: Excel文件路径
    
    Returns:
        dict: 包含格式信息和列索引的字典
    """
    try:
        # 读取Excel文件
        try:
            if sheet_name:
                df = pd.read_excel(file_path, sheet_name=sheet_name, engine='xlrd')
            else:
                df = pd.read_excel(file_path, engine='xlrd')
        except:
            try:
                if sheet_name:
                    df = pd.read_excel(file_path, sheet_name=sheet_name, engine='openpyxl')
                else:
                    df = pd.read_excel(file_path, engine='openpyxl')
            except:
                if sheet_name:
                    df = pd.read_excel(file_path, sheet_name=sheet_name)
                else:
                    df = pd.read_excel(file_path)
        
        # 分析表格格式
        format_info = {
            'file_path': file_path,
            'shape': df.shape,
            'columns': df.shape[1],
            'format_type': None,
            'monthly_rmb_col': None,
            'monthly_yoy_col': None,
            'cumulative_rmb_col': None,
            'cumulative_yoy_col': None
        }
        
        # 根据地区和sheet名称优先判断格式类型，避免按列数判断导致的冲突
        # 江苏省数据特殊处理
        if '江苏' in file_path and sheet_name == "贸易方式":
            format_info['format_type'] = 'jiangsu_columns'
            format_info['cumulative_rmb_col'] = 7  # 累计进出口总值（亿元）
            format_info['cumulative_yoy_col'] = 8  # 累计同比增长率
            format_info['monthly_rmb_col'] = 1     # 单月进出口总值（亿元）
            format_info['monthly_yoy_col'] = None  # 江苏数据没有单月同比列
            print("检测到江苏格式")
        # 浙江省数据特殊处理
        elif '浙江' in file_path and sheet_name == "贸易方式":
            format_info['format_type'] = 'zhejiang_columns'
            format_info['cumulative_rmb_col'] = 1  # 累计进出口总值（万元）
            format_info['cumulative_yoy_col'] = 4  # 累计同比增长率
            format_info['monthly_rmb_col'] = 1     # 单月进出口总值（万元）
            format_info['monthly_yoy_col'] = None  # 浙江数据没有单月同比列
            print("检测到浙江格式")
        # 广州数据特殊处理
        elif '广州' in file_path or sheet_name == "贸易方式进出口总值表":
            format_info['format_type'] = 'complex_guangzhou_columns'
            format_info['cumulative_rmb_col'] = 2  # 累计进出口总值（万元）
            format_info['cumulative_yoy_col'] = 3  # 累计同比增长率
            format_info['monthly_rmb_col'] = 6     # 单月进出口总值（万元）
            format_info['monthly_yoy_col'] = 7     # 单月同比增长率
            print("检测到广州格式")
        # 北京数据特殊处理
        elif '北京' in file_path:
            format_info['format_type'] = 'complex_beijing_columns'
            format_info['cumulative_rmb_col'] = 1  # 累计进出口总值（万元）
            format_info['cumulative_yoy_col'] = 2  # 累计同比增长率
            format_info['monthly_rmb_col'] = 5     # 单月进出口总值（万元）
            format_info['monthly_yoy_col'] = 6     # 单月同比增长率
            print("检测到北京格式")
        # 按列数判断其他格式
        elif df.shape[1] >= 25:
            # 其他地区的25列格式，默认使用广州格式
            format_info['format_type'] = 'complex_guangzhou_columns'
            format_info['cumulative_rmb_col'] = 2  # 累计进出口总值（万元）
            format_info['cumulative_yoy_col'] = 3  # 累计同比增长率
            format_info['monthly_rmb_col'] = 6     # 单月进出口总值（万元）
            format_info['monthly_yoy_col'] = 7     # 单月同比增长率
            print("检测到25列复杂格式（其他地区）")
            
        elif df.shape[1] == 24:  # 24列格式（北京等地区）
            format_info['format_type'] = 'complex_24_columns'
            # 北京格式：进出口数据在B-I列（索引1-8）
            # B-E列（索引1-4）：累计数据
            # F-I列（索引5-8）：单月数据
            format_info['cumulative_rmb_col'] = 1  # 累计人民币
            format_info['cumulative_yoy_col'] = 2  # 累计同比
            format_info['monthly_rmb_col'] = 5     # 单月人民币
            format_info['monthly_yoy_col'] = 6     # 单月同比
            print("检测到24列复杂格式（北京等地区）")
            
        elif df.shape[1] >= 10 and df.shape[1] < 20:  # 10-19列格式（上海等地区）
            format_info['format_type'] = 'complex_shanghai_columns'
            # 上海格式：列1和列2是当月数据，列3和列4是累计数据
            # 根据用户提供的结构：2025年04月-2025年04月（当月）在列1-2，2025年01月-2025年04月（累计）在列3-4
            format_info['cumulative_rmb_col'] = 3  # 累计进出口总值（万元）
            format_info['cumulative_yoy_col'] = 4  # 累计同比增长率
            format_info['monthly_rmb_col'] = 1     # 单月进出口总值（万元）
            format_info['monthly_yoy_col'] = 2     # 单月同比增长率
            print("检测到上海复杂格式（10-19列）")
            
        elif df.shape[1] >= 6 and df.shape[1] <= 10:  # 8列格式（全国）或江苏格式
            if '江苏' in file_path or sheet_name == "贸易方式":
                format_info['format_type'] = 'jiangsu_columns'
                # 江苏格式：第2列是单月数据，第3列是累计数据，第4列是累计同比
                format_info['cumulative_rmb_col'] = 2  # 累计进出口总值（索引2对应第3列）
                format_info['cumulative_yoy_col'] = 3  # 累计同比增长率（索引3对应第4列）
                format_info['monthly_rmb_col'] = 1     # 单月进出口总值（索引1对应第2列）
                format_info['monthly_yoy_col'] = None     # 江苏数据没有单月同比列
                print("检测到江苏格式")
            else:
                format_info['format_type'] = 'simple_8_columns'
                # 全国格式：列结构为 [空值, 贸易方式名称, 4月进出口, 1至4月进出口, 4月出口, 1至4月出口, 4月进口, 1至4月进口]
                # 根据调试结果：列0空值，列1贸易方式名称，列2单月进出口，列3累计进出口
                format_info['cumulative_rmb_col'] = 3  # 1至4月进出口总值（索引3对应第4列）
                format_info['cumulative_yoy_col'] = None  # 全国数据没有同比列，需要计算
                format_info['monthly_rmb_col'] = 2     # 4月进出口总值（索引2对应第3列）
                format_info['monthly_yoy_col'] = None     # 全国数据没有同比列，需要计算
                print("检测到8列简单格式（全国）")
            
        else:
            format_info['format_type'] = 'unknown'
            # 默认设置
            format_info['cumulative_rmb_col'] = 3
            format_info['cumulative_yoy_col'] = 4
            format_info['monthly_rmb_col'] = 1
            format_info['monthly_yoy_col'] = 2
            print(f"未知格式，列数: {df.shape[1]}，使用默认设置")
        
        return format_info
        
    except Exception as e:
        print(f"检测表格格式时出错: {e}")
        return None

def analyze_trade_mode_data(file_path, data_type='monthly', region='全国', sheet_name=None):
    """
    分析贸易方式情况数据
    
    Args:
        file_path: Excel文件路径
        data_type: 'monthly'表示分析单月数据，'cumulative'表示分析累计数据
        region: 地区名称，默认为'全国'
    
    Returns:
        包含一般贸易、加工贸易、保税贸易及总计数据的字典
    """
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return None
    
    # 自动检测表格格式
    format_info = detect_table_format(file_path, sheet_name)
    if not format_info:
        print("无法检测表格格式")
        return None
    
    # 根据数据类型选择列索引
    if data_type == 'monthly':
        import_export_col = format_info['monthly_rmb_col']
        yoy_col = format_info['monthly_yoy_col']
    else:  # cumulative
        import_export_col = format_info['cumulative_rmb_col']
        yoy_col = format_info['cumulative_yoy_col']
    
    # 江苏省数据的特殊处理 - 根据数据类型选择不同列
    march_data = None
    cumulative_col = None
    cumulative_yoy_col = None
    
    if region == '江苏':
        if data_type == 'cumulative':
            import_export_col = 7  # 江苏省累计数据在第8列（索引7）
            yoy_col = 8  # 江苏省累计同比在第9列（索引8）
            cumulative_col = 7  # 累计数据列
            cumulative_yoy_col = 8  # 累计同比列
        else:
            import_export_col = 1  # 江苏省单月数据在第2列（索引1）
            yoy_col = None  # 江苏省单月同比需要计算
            cumulative_col = 7  # 累计数据列（用于计算正确的单月同比）
            cumulative_yoy_col = 8  # 累计同比列（用于计算正确的单月同比）
            # 读取3月数据用于计算环比
            march_data = read_march_jiangsu_data()
            if march_data:
                print("成功读取3月数据，将用于计算环比增长率")
            else:
                print("无法读取3月数据，将使用默认同比计算")
        print(f"江苏省数据（{data_type}）：使用第 {import_export_col + 1} 列作为进出口总额列，同比列: {yoy_col + 1 if yoy_col is not None else '需计算'}")
    elif region == '浙江':
        if data_type == 'cumulative':
            import_export_col = 1  # 浙江省累计数据在第2列（索引1）
            yoy_col = 4  # 浙江省累计同比在第5列（索引4）
            cumulative_col = 1  # 累计数据列
            cumulative_yoy_col = 4  # 累计同比列
        else:
            import_export_col = 1  # 浙江省单月数据在第2列（索引1）
            yoy_col = None  # 浙江省单月同比需要计算
            cumulative_col = 1  # 累计数据列（用于计算正确的单月同比）
            cumulative_yoy_col = 4  # 累计同比列（用于计算正确的单月同比）
            # 读取3月数据用于计算环比
            march_data = read_march_zhejiang_data()
            if march_data:
                print("成功读取浙江3月数据，将用于计算环比增长率")
            else:
                print("无法读取浙江3月数据，将使用默认同比计算")
        print(f"浙江省数据（{data_type}）：使用第 {import_export_col + 1} 列作为进出口总额列，同比列: {yoy_col + 1 if yoy_col is not None else '需计算'}")
    else:
        # 对于非江苏地区，从format_info获取累计列信息
        cumulative_col = format_info.get('cumulative_rmb_col')
        cumulative_yoy_col = format_info.get('cumulative_yoy_col')
    
    print(f"使用{data_type}数据列: 人民币={import_export_col}, 同比={yoy_col}")
    
    # 读取Excel文件
    try:
        # 尝试使用不同的引擎读取文件
        try:
            if sheet_name:
                df = pd.read_excel(file_path, sheet_name=sheet_name, engine='xlrd')
            else:
                df = pd.read_excel(file_path, engine='xlrd')
        except:
            try:
                if sheet_name:
                    df = pd.read_excel(file_path, sheet_name=sheet_name, engine='openpyxl')
                else:
                    df = pd.read_excel(file_path, engine='openpyxl')
            except:
                if sheet_name:
                    df = pd.read_excel(file_path, sheet_name=sheet_name)
                else:
                    df = pd.read_excel(file_path)
        
        print(f"成功读取文件: {file_path}")
        print(f"数据形状: {df.shape}")
        
        # 确定单位：检查文件中是否包含"单位：亿元"
        unit_is_billion = False
        for i in range(min(10, len(df))):
            row_str = str(df.iloc[i].values)
            if '单位' in row_str and '亿元' in row_str:
                unit_is_billion = True
                print("检测到单位为亿元")
                break
        
        # 查找总值行和各贸易方式行
        total_row_index = None
        general_trade_row_index = None
        processing_trade_row_indices = []  # 来料加工和进料加工贸易行
        bonded_trade_row_indices = []  # 保税监管场所和海关特殊监管区域行
        
        # 查找总值行 - 可能在不同位置
        for i in range(len(df)):
            row_values = [str(val).strip() if pd.notna(val) else "" for val in df.iloc[i].values]
            row_str = " ".join(row_values)
            
            # 查找总值行 - 可能在第一列或其他列
            if '总值' in row_str:
                if region == '江苏':
                    print(f"江苏省找到总值行候选: {i}, 内容: {df.iloc[i].values[:5]}")
                # 确认这是总值行而不是标题行
                data_value = df.iloc[i, import_export_col]
                is_valid_data = False
                try:
                    if isinstance(data_value, str):
                        # 处理江苏省数据的逗号分隔符
                        if region == '江苏':
                            numeric_value = float(data_value.replace(',', ''))
                        else:
                            numeric_value = float(data_value)
                        is_valid_data = numeric_value > 0
                    elif isinstance(data_value, (int, float)):
                        is_valid_data = pd.notna(data_value) and data_value > 0
                except (ValueError, TypeError):
                    is_valid_data = False
                
                if is_valid_data:
                    # 对于广州数据，需要找到"广东省广州市"行下的"总值"行
                    if region == '广州':
                        # 检查当前行是否在"广东省广州市"区域内
                        found_guangzhou_section = False
                        guangzhou_start_row = None
                        guangzhou_end_row = None
                        
                        # 找到"广东省广州市"的起始行
                        for check_row in range(len(df)):
                            if '广东省广州市' in str(df.iloc[check_row, 0]):
                                guangzhou_start_row = check_row
                                break
                        
                        if guangzhou_start_row is not None:
                            # 找到广州数据的结束行（下一个"广东省"开头的行）
                            for check_row in range(guangzhou_start_row + 1, len(df)):
                                cell_value = str(df.iloc[check_row, 0]).strip()
                                if cell_value.startswith('广东省') and '广州市' not in cell_value:
                                    guangzhou_end_row = check_row
                                    break
                            
                            # 如果没找到结束行，使用文件末尾
                            if guangzhou_end_row is None:
                                guangzhou_end_row = len(df)
                            
                            # 检查当前总值行是否在广州数据区域内
                            if guangzhou_start_row <= i < guangzhou_end_row:
                                total_row_index = i
                                print(f"找到广州总值行: {i} (广州数据区域: {guangzhou_start_row}-{guangzhou_end_row})")
                                break
                    else:
                        total_row_index = i
                        print(f"找到总值行: {i}")
                        break
        
        # 如果没有找到总值行，可能是因为表格没有明确的"总值"行
        # 在这种情况下，我们可以计算所有贸易方式的总和
        if total_row_index is None:
            print("未找到明确的总值行，将计算所有贸易方式的总和")
        
        # 查找各贸易方式行
        for i in range(len(df)):
            row = df.iloc[i]
            
            # 检查前几列中是否包含特定贸易方式（扩大搜索范围以适应不同格式）
            trade_type = None
            search_cols = min(5, df.shape[1])
            # 江苏数据只在第一列查找贸易方式名称
            if region == '江苏':
                search_cols = 1
            
            for col in range(search_cols):
                if pd.notna(row.iloc[col]) and isinstance(row.iloc[col], str):
                    cell_value = row.iloc[col].strip()
                    
                    # 查找一般贸易行
                    if '一般贸易' in cell_value:
                        print(f"在行 {i} 列 {col} 找到一般贸易: '{cell_value}'")
                        print(f"import_export_col: {import_export_col}")
                        print(f"该行数据: {df.iloc[i].values}")
                        print(f"目标列的值: {df.iloc[i, import_export_col]}")
                        print(f"目标列的类型: {type(df.iloc[i, import_export_col])}")
                        # 确认这是数据行而不是标题行（检查数值列而不是名称列）
                        data_value = df.iloc[i, import_export_col]
                        is_valid_data = False
                        try:
                            if isinstance(data_value, str):
                                # 江苏和浙江数据可能包含逗号分隔符
                                if region in ['江苏', '浙江']:
                                    data_value = data_value.replace(',', '')
                                numeric_value = float(data_value)
                                is_valid_data = numeric_value > 0
                            elif isinstance(data_value, (int, float)):
                                is_valid_data = pd.notna(data_value) and data_value > 0
                        except (ValueError, TypeError):
                            is_valid_data = False
                        
                        if is_valid_data:
                            # 对于广州数据，只取"广东省广州市"下的一般贸易数据
                            if region == '广州':
                                # 检查当前行是否在"广东省广州市"区域内
                                guangzhou_start_row = None
                                guangzhou_end_row = None
                                
                                # 找到"广东省广州市"的起始行
                                for check_row in range(len(df)):
                                    if '广东省广州市' in str(df.iloc[check_row, 0]):
                                        guangzhou_start_row = check_row
                                        break
                                
                                if guangzhou_start_row is not None:
                                    # 找到广州数据的结束行（下一个"广东省"开头的行）
                                    for check_row in range(guangzhou_start_row + 1, len(df)):
                                        cell_value = str(df.iloc[check_row, 0]).strip()
                                        if cell_value.startswith('广东省') and '广州市' not in cell_value:
                                            guangzhou_end_row = check_row
                                            break
                                    
                                    # 如果没找到结束行，使用文件末尾
                                    if guangzhou_end_row is None:
                                        guangzhou_end_row = len(df)
                                    
                                    # 检查当前一般贸易行是否在广州数据区域内
                                    if guangzhou_start_row <= i < guangzhou_end_row:
                                        general_trade_row_index = i
                                        trade_type = '一般贸易'
                                        print(f"找到广州一般贸易行: {i} (广州数据区域: {guangzhou_start_row}-{guangzhou_end_row})")
                                        print(f"行 {i} ({trade_type}) 的值: {data_value}")
                            else:
                                general_trade_row_index = i
                                trade_type = '一般贸易'
                                print(f"找到一般贸易行: {i}")
                                print(f"行 {i} ({trade_type}) 的值: {data_value}")
                        else:
                            print(f"一般贸易行 {i} 不符合数据行条件，数值: {data_value}")
                        if region != '广州' or general_trade_row_index is not None:  # 非广州数据或已找到广州数据就退出
                            break
                    
                    # 查找加工贸易行
                    # 江苏数据：只统计"加工贸易"主行，避免重复统计子项
                    # 浙江数据：统计"来料加工装配贸易"和"进料加工贸易"
                    elif (region == '江苏' and cell_value.strip() == '加工贸易') or (region == '浙江' and ('来料加工装配贸易' in cell_value or '进料加工贸易' in cell_value)) or (region not in ['江苏', '浙江'] and ('来料加工' in cell_value or '进料加工' in cell_value)):
                        # 确认这是数据行而不是标题行
                        data_value = df.iloc[i, import_export_col]
                        is_valid_data = False
                        try:
                            if isinstance(data_value, str):
                                # 江苏和浙江数据可能包含逗号分隔符
                                if region in ['江苏', '浙江']:
                                    data_value = data_value.replace(',', '')
                                numeric_value = float(data_value)
                                is_valid_data = numeric_value > 0
                            elif isinstance(data_value, (int, float)):
                                is_valid_data = pd.notna(data_value) and data_value > 0
                        except (ValueError, TypeError):
                            is_valid_data = False
                        
                        if is_valid_data:
                            # 对于广州数据，只取"广东省广州市"下的加工贸易数据
                            if region == '广州':
                                # 检查当前行是否在"广东省广州市"区域内
                                guangzhou_start_row = None
                                guangzhou_end_row = None
                                
                                # 找到"广东省广州市"的起始行
                                for check_row in range(len(df)):
                                    if '广东省广州市' in str(df.iloc[check_row, 0]):
                                        guangzhou_start_row = check_row
                                        break
                                
                                if guangzhou_start_row is not None:
                                    # 找到广州数据的结束行（下一个"广东省"开头的行）
                                    for check_row in range(guangzhou_start_row + 1, len(df)):
                                        cell_value_check = str(df.iloc[check_row, 0]).strip()
                                        if cell_value_check.startswith('广东省') and '广州市' not in cell_value_check:
                                            guangzhou_end_row = check_row
                                            break
                                    
                                    # 如果没找到结束行，使用文件末尾
                                    if guangzhou_end_row is None:
                                        guangzhou_end_row = len(df)
                                    
                                    # 检查当前加工贸易行是否在广州数据区域内
                                    if guangzhou_start_row <= i < guangzhou_end_row:
                                        processing_trade_row_indices.append(i)
                                        trade_type = cell_value
                                        print(f"找到广州加工贸易行: {i} - {cell_value} (广州数据区域: {guangzhou_start_row}-{guangzhou_end_row})")
                                        print(f"行 {i} ({trade_type}) 的值: {data_value}")
                            else:
                                processing_trade_row_indices.append(i)
                                trade_type = cell_value
                                print(f"找到加工贸易行: {i} - {cell_value}")
                                print(f"行 {i} ({trade_type}) 的值: {data_value}")
                        break
                    
                    # 查找保税贸易行
                    # 江苏数据：只统计"保税物流"这一行，避免重复统计子项
                    # 浙江数据：统计"保税监管场所进出境货物"和"海关特殊监管区域物流货物"
                    elif (region == '江苏' and cell_value.strip() == '保税物流') or (region == '浙江' and ('保税监管场所进出境货物' in cell_value or '海关特殊监管区域物流货物' in cell_value)) or (region not in ['江苏', '浙江'] and ('保税监管场所进出境货物' in cell_value or '海关特殊监管区域物流货物' in cell_value or '保税物流' in cell_value)):
                        # 确认这是数据行而不是标题行
                        data_value = df.iloc[i, import_export_col]
                        is_valid_data = False
                        try:
                            if isinstance(data_value, str):
                                # 江苏和浙江数据可能包含逗号分隔符
                                if region in ['江苏', '浙江']:
                                    data_value = data_value.replace(',', '')
                                numeric_value = float(data_value)
                                is_valid_data = numeric_value > 0
                            elif isinstance(data_value, (int, float)):
                                is_valid_data = pd.notna(data_value) and data_value > 0
                        except (ValueError, TypeError):
                            is_valid_data = False
                        
                        if is_valid_data:
                            # 对于广州数据，只取"广东省广州市"下的保税贸易数据，且只统计"保税物流"
                            if region == '广州':
                                # 广州地区只统计"保税物流"
                                if '保税物流' in cell_value:
                                    # 检查当前行是否在"广东省广州市"区域内
                                    guangzhou_start_row = None
                                    guangzhou_end_row = None
                                    
                                    # 找到"广东省广州市"的起始行
                                    for check_row in range(len(df)):
                                        if '广东省广州市' in str(df.iloc[check_row, 0]):
                                            guangzhou_start_row = check_row
                                            break
                                    
                                    if guangzhou_start_row is not None:
                                        # 找到广州数据的结束行（下一个"广东省"开头的行）
                                        for check_row in range(guangzhou_start_row + 1, len(df)):
                                            cell_value_check = str(df.iloc[check_row, 0]).strip()
                                            if cell_value_check.startswith('广东省') and '广州市' not in cell_value_check:
                                                guangzhou_end_row = check_row
                                                break
                                        
                                        # 如果没找到结束行，使用文件末尾
                                        if guangzhou_end_row is None:
                                            guangzhou_end_row = len(df)
                                        
                                        # 检查当前保税贸易行是否在广州数据区域内
                                        if guangzhou_start_row <= i < guangzhou_end_row:
                                            bonded_trade_row_indices.append(i)
                                            trade_type = cell_value
                                            print(f"找到广州保税贸易行: {i} - {cell_value} (广州数据区域: {guangzhou_start_row}-{guangzhou_end_row})")
                                            print(f"行 {i} ({trade_type}) 的值: {data_value}")
                            else:
                                # 其他地区统计所有三种保税贸易类型
                                bonded_trade_row_indices.append(i)
                                trade_type = cell_value
                                print(f"找到保税贸易行: {i} - {cell_value}")
                                print(f"行 {i} ({trade_type}) 的值: {data_value}")
                        break
        
        # 提取数据
        # 对于浙江数据，只要找到一般贸易行就可以进入数据提取逻辑
        if general_trade_row_index is not None and (region == '浙江' or processing_trade_row_indices or bonded_trade_row_indices):
            # 如果没有找到总值行，计算所有贸易方式的总和
            if total_row_index is None:
                # 收集所有贸易方式行
                all_trade_rows = []
                for i in range(len(df)):
                    data_value = df.iloc[i, import_export_col]
                    is_valid_data = False
                    try:
                        if isinstance(data_value, str):
                            numeric_value = float(data_value)
                            is_valid_data = numeric_value > 0
                        elif isinstance(data_value, (int, float)):
                            is_valid_data = pd.notna(data_value) and data_value > 0
                    except (ValueError, TypeError):
                        is_valid_data = False
                    
                    if is_valid_data:
                        # 检查是否是贸易方式行（而不是汇总行或标题行）
                        is_trade_row = False
                        for col in range(min(3, df.shape[1])):
                            if pd.notna(df.iloc[i, col]) and isinstance(df.iloc[i, col], str) and len(df.iloc[i, col].strip()) > 0:
                                is_trade_row = True
                                break
                        if is_trade_row:
                            all_trade_rows.append(i)
                
                # 计算总值
                def convert_to_float(value):
                    if isinstance(value, str) and region == '江苏':
                        return float(value.replace(',', ''))
                    return float(value)
                
                total_value = sum(convert_to_float(df.iloc[i, import_export_col]) for i in all_trade_rows)
                print(f"计算得到的总值: {total_value}")
            else:
                # 使用找到的总值行
                raw_value = df.iloc[total_row_index, import_export_col]
                if isinstance(raw_value, str) and region == '江苏':
                    total_value = float(raw_value.replace(',', ''))
                else:
                    total_value = float(raw_value)
                print(f"总值行的值: {total_value}")
            
            # 转换为亿元
            if unit_is_billion:
                total_value_billion = round(total_value, 1)  # 已经是亿元，只需四舍五入
            elif region == '江苏':
                total_value_billion = round(total_value, 1)  # 江苏数据已经是亿元单位
            else:
                total_value_billion = round(total_value / 10000, 1)  # 从万元转换为亿元
            
            # 尝试获取总值的同比增长率
            total_yoy = 0  # 默认值
            if total_row_index is not None:
                try:
                    # 江苏和浙江地区单月数据使用正确的同比计算方法
                    if (region == '江苏' or region == '浙江') and data_type == 'monthly' and march_data:
                        # 获取4月累计数据和累计同比
                        april_cumulative_value = df.iloc[total_row_index, cumulative_col] if cumulative_col is not None else 0
                        april_cumulative_yoy = df.iloc[total_row_index, cumulative_yoy_col] if cumulative_yoy_col is not None else 0
                        
                        # 处理数据格式
                        if isinstance(april_cumulative_value, str):
                            april_cumulative_value = float(april_cumulative_value.replace(',', ''))
                        else:
                            april_cumulative_value = float(april_cumulative_value)
                            
                        if isinstance(april_cumulative_yoy, str):
                            april_cumulative_yoy = float(april_cumulative_yoy.replace('%', ''))
                        else:
                            april_cumulative_yoy = float(april_cumulative_yoy)
                        
                        # 获取3月累计数据和累计同比
                        march_cumulative_value = march_data.get('total_cumulative', 0)
                        march_cumulative_yoy = march_data.get('total_cumulative_yoy', 0)
                        
                        # 使用正确的同比计算方法
                        total_yoy = calculate_correct_monthly_yoy(
                            total_value, april_cumulative_value, april_cumulative_yoy,
                            march_cumulative_value, march_cumulative_yoy
                        )
                        print(f"总值正确同比计算: 4月单月={total_value}, 4月累计={april_cumulative_value}, 4月累计同比={april_cumulative_yoy}%")
                        print(f"                  3月累计={march_cumulative_value}, 3月累计同比={march_cumulative_yoy}%, 计算结果={total_yoy}%")
                    elif yoy_col is not None:
                        # 使用自动检测的同比增长率列
                        yoy_value = df.iloc[total_row_index, yoy_col]
                        if pd.notna(yoy_value):
                            # 如果是字符串，去掉百分号
                            if isinstance(yoy_value, str):
                                yoy_value = yoy_value.replace('%', '')
                            total_yoy = round(float(yoy_value), 1)
                    else:
                        # 全国数据：同比数据在下一行的同一列
                        if total_row_index + 1 < len(df):
                            yoy_value = df.iloc[total_row_index + 1, import_export_col]
                            if pd.notna(yoy_value):
                                # 如果是字符串，去掉百分号
                                if isinstance(yoy_value, str):
                                    yoy_value = yoy_value.replace('%', '')
                                total_yoy = round(float(yoy_value), 1)
                except Exception as e:
                    print(f"获取总值同比增长率时出错: {e}")
            
            # 一般贸易数据
            print(f"准备读取一般贸易数据，行索引: {general_trade_row_index}, 列索引: {import_export_col}")
            print(f"该位置的值: {df.iloc[general_trade_row_index, import_export_col]}")
            
            # 检查该位置是否为数值
            try:
                general_trade_value_str = str(df.iloc[general_trade_row_index, import_export_col])
                # 江苏数据可能包含逗号分隔符
                if region == '江苏':
                    general_trade_value_str = general_trade_value_str.replace(',', '')
                general_trade_value = float(general_trade_value_str)
            except (ValueError, TypeError):
                print(f"错误：位置({general_trade_row_index}, {import_export_col})不能转换为数值，值为: {df.iloc[general_trade_row_index, import_export_col]}")
                return None
            if unit_is_billion:
                general_trade_value_billion = round(general_trade_value, 1)
            elif region == '江苏':
                general_trade_value_billion = round(general_trade_value, 1)  # 江苏数据已经是亿元单位
            else:
                general_trade_value_billion = round(general_trade_value / 10000, 1)
            
            # 尝试获取一般贸易的同比增长率
            general_trade_yoy = 0  # 默认值
            try:
                # 江苏和浙江地区单月数据使用正确的同比计算方法
                if (region == '江苏' or region == '浙江') and data_type == 'monthly' and march_data:
                    # 获取4月累计数据和累计同比
                    april_cumulative_value = df.iloc[general_trade_row_index, cumulative_col] if cumulative_col is not None else 0
                    april_cumulative_yoy = df.iloc[general_trade_row_index, cumulative_yoy_col] if cumulative_yoy_col is not None else 0
                    
                    # 处理数据格式
                    if isinstance(april_cumulative_value, str):
                        april_cumulative_value = float(april_cumulative_value.replace(',', ''))
                    else:
                        april_cumulative_value = float(april_cumulative_value)
                        
                    if isinstance(april_cumulative_yoy, str):
                        april_cumulative_yoy = float(april_cumulative_yoy.replace('%', ''))
                    else:
                        april_cumulative_yoy = float(april_cumulative_yoy)
                    
                    # 获取3月累计数据和累计同比
                    march_cumulative_value = march_data.get('general_trade_cumulative', 0)
                    march_cumulative_yoy = march_data.get('general_trade_cumulative_yoy', 0)
                    
                    # 使用正确的同比计算方法
                    general_trade_yoy = calculate_correct_monthly_yoy(
                        general_trade_value, april_cumulative_value, april_cumulative_yoy,
                        march_cumulative_value, march_cumulative_yoy
                    )
                    print(f"一般贸易正确同比计算: 4月单月={general_trade_value}, 4月累计={april_cumulative_value}, 4月累计同比={april_cumulative_yoy}%")
                    print(f"                      3月累计={march_cumulative_value}, 3月累计同比={march_cumulative_yoy}%, 计算结果={general_trade_yoy}%")
                elif yoy_col is not None:
                    # 使用自动检测的同比增长率列
                    yoy_value = df.iloc[general_trade_row_index, yoy_col]
                    if pd.notna(yoy_value):
                        # 如果是字符串，去掉百分号
                        if isinstance(yoy_value, str):
                            yoy_value = yoy_value.replace('%', '')
                        general_trade_yoy = round(float(yoy_value), 1)
                else:
                    # 全国数据：同比数据在下一行的同一列
                    if general_trade_row_index + 1 < len(df):
                        yoy_value = df.iloc[general_trade_row_index + 1, import_export_col]
                        if pd.notna(yoy_value):
                            # 如果是字符串，去掉百分号
                            if isinstance(yoy_value, str):
                                yoy_value = yoy_value.replace('%', '')
                            general_trade_yoy = round(float(yoy_value), 1)
            except Exception as e:
                print(f"获取一般贸易同比增长率时出错: {e}")
                
            general_trade_share = round(general_trade_value / total_value * 100, 1)
            
            # 加工贸易数据
            processing_trade_values = []
            processing_trade_yoys = []
            
            if processing_trade_row_indices:
                for idx in processing_trade_row_indices:
                    try:
                        value_str = str(df.iloc[idx, import_export_col])
                        # 江苏和浙江数据可能包含逗号分隔符
                        if region == '江苏' or region == '浙江':
                            value_str = value_str.replace(',', '')
                        value = float(value_str)
                        
                        # 尝试获取同比增长率
                        yoy = 0  # 默认值
                        try:
                            # 江苏和浙江地区单月数据使用正确的同比计算方法
                            if (region == '江苏' or region == '浙江') and data_type == 'monthly' and march_data:
                                # 获取4月累计数据和累计同比
                                april_cumulative_value = df.iloc[idx, cumulative_col] if cumulative_col is not None else 0
                                april_cumulative_yoy = df.iloc[idx, cumulative_yoy_col] if cumulative_yoy_col is not None else 0
                                
                                # 处理数据格式
                                if isinstance(april_cumulative_value, str):
                                    april_cumulative_value = float(april_cumulative_value.replace(',', ''))
                                else:
                                    april_cumulative_value = float(april_cumulative_value)
                                    
                                if isinstance(april_cumulative_yoy, str):
                                    april_cumulative_yoy = float(april_cumulative_yoy.replace('%', ''))
                                else:
                                    april_cumulative_yoy = float(april_cumulative_yoy)
                                
                                # 获取3月累计数据和累计同比
                                march_cumulative_value = march_data.get('processing_trade_cumulative', 0)
                                march_cumulative_yoy = march_data.get('processing_trade_cumulative_yoy', 0)
                                
                                # 使用正确的同比计算方法
                                yoy = calculate_correct_monthly_yoy(
                                    value, april_cumulative_value, april_cumulative_yoy,
                                    march_cumulative_value, march_cumulative_yoy
                                )
                                print(f"加工贸易正确同比计算: 4月单月={value}, 4月累计={april_cumulative_value}, 4月累计同比={april_cumulative_yoy}%")
                                print(f"                      3月累计={march_cumulative_value}, 3月累计同比={march_cumulative_yoy}%, 计算结果={yoy}%")
                            elif yoy_col is not None:
                                # 使用自动检测的同比增长率列
                                yoy_value = df.iloc[idx, yoy_col]
                                if pd.notna(yoy_value):
                                    # 如果是字符串，去掉百分号
                                    if isinstance(yoy_value, str):
                                        yoy_value = yoy_value.replace('%', '')
                                    yoy = round(float(yoy_value), 1)
                            else:
                                # 全国数据：同比数据在下一行的同一列
                                if idx + 1 < len(df):
                                    yoy_value = df.iloc[idx + 1, import_export_col]
                                    if pd.notna(yoy_value):
                                        # 如果是字符串，去掉百分号
                                        if isinstance(yoy_value, str):
                                            yoy_value = yoy_value.replace('%', '')
                                        yoy = round(float(yoy_value), 1)
                        except Exception as e:
                            print(f"获取行{idx}的同比增长率时出错: {e}")
                            
                        processing_trade_values.append(value)
                        processing_trade_yoys.append(yoy)
                        print(f"加工贸易行 {idx}: 值={value}, 同比={yoy}")
                    except Exception as e:
                        print(f"处理加工贸易行{idx}时出错: {e}")
                
                # 计算加工贸易合计
                processing_trade_value = sum(processing_trade_values)
                if unit_is_billion:
                    processing_trade_value_billion = round(processing_trade_value, 1)
                elif region == '江苏':
                    processing_trade_value_billion = round(processing_trade_value, 1)  # 江苏数据已经是亿元单位
                else:
                    processing_trade_value_billion = round(processing_trade_value / 10000, 1)
                
                # 计算加工贸易去年的值
                processing_trade_last_year = 0
                for i in range(len(processing_trade_values)):
                    # 根据今年的值和同比增长率计算去年的值
                    yoy_rate = processing_trade_yoys[i] / 100
                    last_year_value = processing_trade_values[i] / (1 + yoy_rate) if yoy_rate != -1 else 0
                    processing_trade_last_year += last_year_value
                
                # 计算加工贸易整体同比增长率
                processing_trade_weighted_yoy = round((processing_trade_value / processing_trade_last_year - 1) * 100, 1) if processing_trade_last_year > 0 else 0
                
                processing_trade_share = round(processing_trade_value / total_value * 100, 1)
            else:
                print("未找到加工贸易行，使用默认值0")
                processing_trade_value = 0
                processing_trade_value_billion = 0
                processing_trade_weighted_yoy = 0
                processing_trade_share = 0
                processing_trade_last_year = 0
            
            # 保税贸易数据
            bonded_trade_values = []
            bonded_trade_yoys = []
            
            if bonded_trade_row_indices:
                for idx in bonded_trade_row_indices:
                    try:
                        value_str = str(df.iloc[idx, import_export_col])
                        # 江苏和浙江数据可能包含逗号分隔符
                        if region == '江苏' or region == '浙江':
                            value_str = value_str.replace(',', '')
                        value = float(value_str)
                        
                        # 尝试获取同比增长率
                        yoy = 0  # 默认值
                        try:
                            # 江苏和浙江地区单月数据使用正确的同比计算方法
                            if (region == '江苏' or region == '浙江') and data_type == 'monthly' and march_data:
                                # 获取4月累计数据和累计同比
                                april_cumulative_value = df.iloc[idx, cumulative_col] if cumulative_col is not None else 0
                                april_cumulative_yoy = df.iloc[idx, cumulative_yoy_col] if cumulative_yoy_col is not None else 0
                                
                                # 处理数据格式
                                if isinstance(april_cumulative_value, str):
                                    april_cumulative_value = float(april_cumulative_value.replace(',', ''))
                                else:
                                    april_cumulative_value = float(april_cumulative_value)
                                    
                                if isinstance(april_cumulative_yoy, str):
                                    april_cumulative_yoy = float(april_cumulative_yoy.replace('%', ''))
                                else:
                                    april_cumulative_yoy = float(april_cumulative_yoy)
                                
                                # 获取3月累计数据和累计同比
                                march_cumulative_value = march_data.get('bonded_trade_cumulative', 0)
                                march_cumulative_yoy = march_data.get('bonded_trade_cumulative_yoy', 0)
                                
                                # 使用正确的同比计算方法
                                yoy = calculate_correct_monthly_yoy(
                                    value, april_cumulative_value, april_cumulative_yoy,
                                    march_cumulative_value, march_cumulative_yoy
                                )
                                print(f"保税贸易正确同比计算: 4月单月={value}, 4月累计={april_cumulative_value}, 4月累计同比={april_cumulative_yoy}%")
                                print(f"                      3月累计={march_cumulative_value}, 3月累计同比={march_cumulative_yoy}%, 计算结果={yoy}%")
                            elif yoy_col is not None:
                                # 使用自动检测的同比增长率列
                                yoy_value = df.iloc[idx, yoy_col]
                                if pd.notna(yoy_value):
                                    # 如果是字符串，去掉百分号
                                    if isinstance(yoy_value, str):
                                        yoy_value = yoy_value.replace('%', '')
                                    yoy = round(float(yoy_value), 1)
                            else:
                                # 全国数据：同比数据在下一行的同一列
                                if idx + 1 < len(df):
                                    yoy_value = df.iloc[idx + 1, import_export_col]
                                    if pd.notna(yoy_value):
                                        # 如果是字符串，去掉百分号
                                        if isinstance(yoy_value, str):
                                            yoy_value = yoy_value.replace('%', '')
                                        yoy = round(float(yoy_value), 1)
                        except Exception as e:
                            print(f"获取行{idx}的同比增长率时出错: {e}")
                            
                        bonded_trade_values.append(value)
                        bonded_trade_yoys.append(yoy)
                        print(f"保税贸易行 {idx}: 值={value}, 同比={yoy}")
                    except Exception as e:
                        print(f"处理保税贸易行{idx}时出错: {e}")
                
                # 计算保税贸易合计
                bonded_trade_value = sum(bonded_trade_values)
                if unit_is_billion:
                    bonded_trade_value_billion = round(bonded_trade_value, 1)
                elif region == '江苏':
                    bonded_trade_value_billion = round(bonded_trade_value, 1)  # 江苏数据已经是亿元单位
                else:
                    bonded_trade_value_billion = round(bonded_trade_value / 10000, 1)
                
                # 计算保税贸易去年的值
                bonded_trade_last_year = 0
                for i in range(len(bonded_trade_values)):
                    # 根据今年的值和同比增长率计算去年的值
                    yoy_rate = bonded_trade_yoys[i] / 100
                    last_year_value = bonded_trade_values[i] / (1 + yoy_rate) if yoy_rate != -1 else 0
                    bonded_trade_last_year += last_year_value
                
                # 计算保税贸易整体同比增长率
                bonded_trade_weighted_yoy = round((bonded_trade_value / bonded_trade_last_year - 1) * 100, 1) if bonded_trade_last_year > 0 else 0
                
                bonded_trade_share = round(bonded_trade_value / total_value * 100, 1)
            else:
                print("未找到保税贸易行，使用默认值0")
                bonded_trade_value = 0
                bonded_trade_value_billion = 0
                bonded_trade_weighted_yoy = 0
                bonded_trade_share = 0
                bonded_trade_last_year = 0
            
            # 返回分析结果
            return {
                "region": region,
                "general_trade": {
                    "value": general_trade_value,
                    "value_billion": general_trade_value_billion,
                    "yoy": general_trade_yoy,
                    "share": general_trade_share,
                    "last_year_value": general_trade_value / (1 + general_trade_yoy / 100) if general_trade_yoy != -100 else 0
                },
                "processing_trade": {
                    "value": processing_trade_value,
                    "value_billion": processing_trade_value_billion,
                    "yoy": processing_trade_weighted_yoy,
                    "share": processing_trade_share,
                    "last_year_value": processing_trade_last_year
                },
                "bonded_trade": {
                    "value": bonded_trade_value,
                    "value_billion": bonded_trade_value_billion,
                    "yoy": bonded_trade_weighted_yoy,
                    "share": bonded_trade_share,
                    "last_year_value": bonded_trade_last_year
                },
                "total": {
                    "value": total_value,
                    "value_billion": total_value_billion,
                    "yoy": total_yoy,
                    "share": 100.0,
                    "last_year_value": total_value / (1 + total_yoy / 100) if total_yoy != -100 else 0
                }
            }
            
        else:
            print("未能找到所需的数据行")
            return None
        
    except Exception as e:
        import traceback
        print(f"处理文件时出错: {e}")
        print(traceback.format_exc())
        return None

def get_region_from_filename(file_path):
    """从文件名中提取地区信息"""
    filename = os.path.basename(file_path)
    
    # 尝试从文件名中提取地区信息
    regions = ["全国", "北京", "上海", "广州", "江苏", "浙江"]
    for region in regions:
        if region in filename:
            return region
    
    # 如果文件名中没有明确的地区信息，从路径中提取
    for region in regions:
        if region in file_path:
            return region
    
    # 默认返回全国
    return "全国"

def main(file_path=None):
    """主函数"""
    # 指定文件路径
    if file_path is None:
        file_path = r"C:\Users\<USER>\Desktop\海关数据\小超海关统计\全国进出口\20250530\全国\（5）2025年4月进出口商品贸易方式总值表（人民币值）.xls"
    
    # 从文件名中获取地区信息
    region = get_region_from_filename(file_path)
    print(f"分析地区: {region}")
    
    # 分析单月数据
    print("分析单月数据:")
    monthly_data = analyze_trade_mode_data(file_path, data_type='monthly', region=region)
    
    if monthly_data:
        # 创建总体汇总表格
        result_data = {
            "贸易方式": ["一般贸易", "加工贸易", "保税贸易", "总计"],
            "进出口额(亿元)": [
                monthly_data["general_trade"]["value_billion"],
                monthly_data["processing_trade"]["value_billion"],
                monthly_data["bonded_trade"]["value_billion"],
                monthly_data["total"]["value_billion"]
            ],
            "同比增长(%)": [
                monthly_data["general_trade"]["yoy"],
                monthly_data["processing_trade"]["yoy"],
                monthly_data["bonded_trade"]["yoy"],
                monthly_data["total"]["yoy"]
            ],
            "占比(%)": [
                monthly_data["general_trade"]["share"],
                monthly_data["processing_trade"]["share"],
                monthly_data["bonded_trade"]["share"],
                monthly_data["total"]["share"]
            ]
        }
        
        # 设置pandas显示选项
        pd.set_option('display.float_format', '{:.1f}'.format)
        
        # 创建DataFrame并输出
        result_df = pd.DataFrame(result_data)
        print(f"\n{region} - 单月数据:")
        print(result_df.to_string(index=False))
    
    # 分析累计数据
    print("\n分析累计数据:")
    cumulative_data = analyze_trade_mode_data(file_path, data_type='cumulative', region=region)
    
    if cumulative_data:
        # 创建总体汇总表格
        result_data = {
            "贸易方式": ["一般贸易", "加工贸易", "保税贸易", "总计"],
            "进出口额(亿元)": [
                cumulative_data["general_trade"]["value_billion"],
                cumulative_data["processing_trade"]["value_billion"],
                cumulative_data["bonded_trade"]["value_billion"],
                cumulative_data["total"]["value_billion"]
            ],
            "同比增长(%)": [
                cumulative_data["general_trade"]["yoy"],
                cumulative_data["processing_trade"]["yoy"],
                cumulative_data["bonded_trade"]["yoy"],
                cumulative_data["total"]["yoy"]
            ],
            "占比(%)": [
                cumulative_data["general_trade"]["share"],
                cumulative_data["processing_trade"]["share"],
                cumulative_data["bonded_trade"]["share"],
                cumulative_data["total"]["share"]
            ]
        }
        
        # 设置pandas显示选项
        pd.set_option('display.float_format', '{:.1f}'.format)
        
        # 创建DataFrame并输出
        result_df = pd.DataFrame(result_data)
        print(f"\n{region} - 累计数据:")
        print(result_df.to_string(index=False))

def analyze_single_region(file_path, region_name):
    """分析单个地区的数据并返回结果"""
    if not os.path.exists(file_path):
        print(f"{region_name}数据文件不存在: {file_path}")
        return None, None
    
    # 广州、江苏和浙江数据需要指定特定的sheet页
    sheet_name = None
    if region_name == "广州":
        sheet_name = "贸易方式进出口总值表"
    elif region_name == "江苏":
        sheet_name = "贸易方式"
    elif region_name == "浙江":
        sheet_name = "贸易方式"
    
    # 分析累计数据
    cumulative_data = analyze_trade_mode_data(file_path, data_type='cumulative', region=region_name, sheet_name=sheet_name)
    
    # 分析单月数据
    monthly_data = analyze_trade_mode_data(file_path, data_type='monthly', region=region_name, sheet_name=sheet_name)
    
    return cumulative_data, monthly_data

def create_combined_table(all_data, data_type):
    """创建合并的表格数据"""
    # 创建表格数据
    table_data = []
    
    # 表头
    headers = ["地区", "一般贸易", "", "", "加工贸易", "", "", "保税贸易", "", ""]
    sub_headers = ["", "进出口额", "同比/%", "占比/%", "进出口额", "同比/%", "占比/%", "进出口额", "同比/%", "占比/%"]
    
    # 添加数据行
    for region_name, data in all_data.items():
        if data:
            row = [
                region_name,
                data["general_trade"]["value_billion"],
                data["general_trade"]["yoy"],
                data["general_trade"]["share"],
                data["processing_trade"]["value_billion"],
                data["processing_trade"]["yoy"],
                data["processing_trade"]["share"],
                data["bonded_trade"]["value_billion"],
                data["bonded_trade"]["yoy"],
                data["bonded_trade"]["share"]
            ]
            table_data.append(row)
    
    return headers, sub_headers, table_data

def print_combined_table(headers, sub_headers, table_data, data_type):
    """打印合并的表格"""
    print(f"\n{data_type}地区\t一般贸易\t\t\t加工贸易\t\t\t保税贸易\t\t")
    print("\t进出口额\t同比/%\t占比/%\t进出口额\t同比/%\t占比/%\t进出口额\t同比/%\t占比/%")
    
    for row in table_data:
        print(f"{row[0]}\t{row[1]:.1f}\t{row[2]:.1f}\t{row[3]:.1f}\t{row[4]:.1f}\t{row[5]:.1f}\t{row[6]:.1f}\t{row[7]:.1f}\t{row[8]:.1f}\t{row[9]:.1f}")

if __name__ == "__main__":
    # 定义各地区文件路径
    regions_data = {
        "全国": r"C:\Users\<USER>\Desktop\海关数据\小超海关统计\全国进出口\20250530\全国\（5）2025年4月进出口商品贸易方式总值表（人民币值）.xls",
        "北京": r"C:\Users\<USER>\Desktop\海关数据\小超海关统计\全国进出口\20250530\2025年1-4月 - 北京\（5）北京地区进出口商品贸易方式总值表（2025年1-4月）.xls",
        "上海": r"C:\Users\<USER>\Desktop\海关数据\小超海关统计\全国进出口\20250530\2025年1-4月 - 上海\本市1：2025年1至4月进出口商品贸易方式总值表（人民币值）.xls",
        "广州": r"C:\Users\<USER>\Desktop\海关数据\小超海关统计\全国进出口\20250530\2025年1-4月 - 广州\2025年1-4月广州关区所辖7地市进出口综合统计资料.xls",
        "江苏": r"C:\Users\<USER>\Desktop\海关数据\小超海关统计\全国进出口\20250530\2025年1-4月 - 江苏\2025年4月江苏省主要进出口数据.xls",
        "浙江": r"C:\Users\<USER>\Desktop\海关数据\小超海关统计\全国进出口\20250530\2025年1-4月 -浙江\2025年1-4月浙江省进出口情况.xlsx"
    }
    
    # 收集所有地区的数据
    all_cumulative_data = {}
    all_monthly_data = {}
    
    for region_name, file_path in regions_data.items():
        cumulative_data, monthly_data = analyze_single_region(file_path, region_name)
        all_cumulative_data[region_name] = cumulative_data
        all_monthly_data[region_name] = monthly_data
    
    # 创建并显示累积数据表格
    cumulative_headers, cumulative_sub_headers, cumulative_table_data = create_combined_table(all_cumulative_data, "累积")
    print_combined_table(cumulative_headers, cumulative_sub_headers, cumulative_table_data, "累积")
    
    # 创建并显示单月数据表格
    monthly_headers, monthly_sub_headers, monthly_table_data = create_combined_table(all_monthly_data, "单月")
    print_combined_table(monthly_headers, monthly_sub_headers, monthly_table_data, "当月")