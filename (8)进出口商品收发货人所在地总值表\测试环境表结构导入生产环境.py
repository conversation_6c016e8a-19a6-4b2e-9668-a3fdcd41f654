import cx_Oracle as oracle
import pandas as pd
import logging
import os
import configparser
import sys

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    filename='table_migration.log'
)
logger = logging.getLogger('table_migration')

# 获取配置文件路径
script_dir = os.path.dirname(os.path.abspath(__file__))
config_path = os.path.join(script_dir, 'db_config.ini')

def get_db_config(section):
    """从配置文件读取数据库配置"""
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
    config = configparser.ConfigParser()
    config.read(config_path)
    
    if section not in config:
        raise ValueError(f"配置文件中未找到 {section} 节")
    
    return {
        'username': config[section]['username'],
        'password': config[section]['password'],
        'host': config[section]['host'],
        'port': config[section]['port'],
        'service_name': config[section]['service_name']
    }

def get_connection_string(db_config):
    """根据配置生成数据库连接字符串"""
    return f"{db_config['username']}/{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['service_name']}"

def get_table_structure(table_name, source_conn):
    """获取表结构"""
    cursor = source_conn.cursor()
    try:
        # 获取表的列信息
        columns_sql = f"""
        SELECT column_name, data_type, data_length, data_precision, data_scale, nullable
        FROM all_tab_columns
        WHERE table_name = '{table_name.upper()}'
        ORDER BY column_id
        """
        cursor.execute(columns_sql)
        columns = cursor.fetchall()
        
        if not columns:
            raise ValueError(f"在测试环境中未找到表 {table_name}")
            
        # 获取表的主键信息
        pk_sql = f"""
        SELECT cols.column_name
        FROM all_constraints cons, all_cons_columns cols
        WHERE cons.constraint_type = 'P'
        AND cons.constraint_name = cols.constraint_name
        AND cons.owner = cols.owner
        AND cons.table_name = '{table_name.upper()}'
        """
        cursor.execute(pk_sql)
        primary_keys = [row[0] for row in cursor.fetchall()]
        
        return columns, primary_keys
    finally:
        cursor.close()

def create_table_in_target(table_name, columns, primary_keys, target_conn):
    """在目标数据库创建表"""
    cursor = target_conn.cursor()
    try:
        # 构建创建表的SQL语句
        column_definitions = []
        for col in columns:
            column_name, data_type, data_length, data_precision, data_scale, nullable = col
            
            # 处理数据类型
            if data_type == 'NUMBER' and data_precision is not None:
                if data_scale and int(data_scale) > 0:
                    type_def = f"{data_type}({data_precision},{data_scale})"
                else:
                    type_def = f"{data_type}({data_precision})"
            elif data_type in ('CHAR', 'VARCHAR2', 'NVARCHAR2'):
                type_def = f"{data_type}({data_length})"
            else:
                type_def = data_type
                
            # 处理NULL约束
            null_constraint = "NULL" if nullable == 'Y' else "NOT NULL"
            
            column_definitions.append(f"{column_name} {type_def} {null_constraint}")
        
        # 添加主键约束（如果有）
        if primary_keys:
            pk_constraint = f", CONSTRAINT PK_{table_name} PRIMARY KEY ({', '.join(primary_keys)})"
        else:
            pk_constraint = ""
            
        create_table_sql = f"""
        CREATE TABLE {table_name} (
            {', '.join(column_definitions)}{pk_constraint}
        )
        """
        
        # 先尝试删除表（如果存在）
        try:
            cursor.execute(f"DROP TABLE {table_name}")
            print(f"已删除目标环境中的表 {table_name}")
        except oracle.DatabaseError:
            # 表不存在，忽略错误
            pass
            
        # 创建表
        cursor.execute(create_table_sql)
        target_conn.commit()
        print(f"在生产环境中成功创建表 {table_name}")
        return True
    except Exception as e:
        target_conn.rollback()
        print(f"创建表 {table_name} 失败: {str(e)}")
        logger.error(f"创建表 {table_name} 失败: {str(e)}")
        return False
    finally:
        cursor.close()

def migrate_table_structure(source_table_name, target_table_name=None):
    """将表结构从测试环境迁移到生产环境"""
    if target_table_name is None:
        target_table_name = source_table_name
        
    source_conn = None
    target_conn = None
    
    try:
        # 连接源数据库（测试环境）
        test_db_config = get_db_config('TEST_DB')
        source_conn = oracle.connect(get_connection_string(test_db_config))
        print(f"已连接到测试环境数据库")
        
        # 连接目标数据库（生产环境）
        prod_db_config = get_db_config('PROD_DB')
        target_conn = oracle.connect(get_connection_string(prod_db_config))
        print(f"已连接到生产环境数据库")
        
        # 获取表结构
        print(f"正在获取测试环境表 {source_table_name} 的结构...")
        columns, primary_keys = get_table_structure(source_table_name, source_conn)
        
        # 在目标数据库创建表
        print(f"正在生产环境中创建表 {target_table_name}...")
        success = create_table_in_target(target_table_name, columns, primary_keys, target_conn)
        
        if success:
            print(f"表结构迁移成功！源表: {source_table_name}, 目标表: {target_table_name}")
        else:
            print(f"表结构迁移失败！")
            
    except Exception as e:
        print(f"表结构迁移过程中发生错误: {str(e)}")
        logger.error(f"表结构迁移过程中发生错误: {str(e)}")
    finally:
        # 关闭连接
        if source_conn:
            source_conn.close()
        if target_conn:
            target_conn.close()

def main():
    if len(sys.argv) > 1:
        source_table = sys.argv[1]
        target_table = sys.argv[2] if len(sys.argv) > 2 else source_table
        migrate_table_structure(source_table, target_table)
    else:
        # 默认迁移CS_SINGLEWINDOW_PRODUCT_CODE表到T_BC_SW_TS_CODE
        migrate_table_structure('CS_SINGLEWINDOW_PRODUCT_CODE', 'T_BC_SW_TS_CODE')
        
if __name__ == "__main__":
    main() 