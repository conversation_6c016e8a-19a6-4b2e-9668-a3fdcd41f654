import pandas as pd
import os

def test_sql_parameters_without_db(csv_file_path):
    """
    测试SQL参数而不连接数据库
    """
    try:
        print(f"=== 测试SQL参数（无数据库连接）===")
        print(f"文件: {csv_file_path}")
        
        # 读取CSV文件
        df = pd.read_csv(csv_file_path, encoding='utf-8-sig')
        print(f"读取到 {len(df)} 条记录，{len(df.columns)} 个字段")
        
        if len(df) == 0:
            print("CSV文件为空")
            return
        
        # SQL语句
        table_name = 'T_STATISTICAL_CUS_TOTAL_CS'
        insert_sql = f"""
        INSERT INTO {table_name} (
            STAT_DATE, STAT_TYPE, STAT_NAME, STAT_CODE, STAT_CONTENT_RAW, STAT_CONTENT_CLEANSE,
            ACC_A_CNY_AMOUNT, ACC_A_CNY_YOY, ACC_A_USD_AMOUNT, ACC_A_USD_YOY,
            MON_A_CNY_AMOUNT, MON_A_CNY_YOY, MON_A_USD_AMOUNT, MON_A_USD_YOY,
            ACC_E_CNY_AMOUNT, ACC_E_CNY_YOY, ACC_E_USD_AMOUNT, ACC_E_USD_YOY,
            MON_E_CNY_AMOUNT, MON_E_CNY_YOY, MON_E_USD_AMOUNT, MON_E_USD_YOY,
            ACC_I_CNY_AMOUNT, ACC_I_CNY_YOY, ACC_I_USD_AMOUNT, ACC_I_USD_YOY,
            MON_I_CNY_AMOUNT, MON_I_CNY_YOY, MON_I_USD_AMOUNT, MON_I_USD_YOY,
            ACC_E_AMOUNT, ACC_E_AMOUNT_UNIT, ACC_E_AMOUNT_YOY,
            MON_E_AMOUNT, MON_E_AMOUNT_UNIT, MON_E_AMOUNT_YOY,
            ACC_I_AMOUNT, ACC_I_AMOUNT_UNIT, ACC_I_AMOUNT_YOY,
            MON_I_AMOUNT, MON_I_AMOUNT_UNIT, MON_I_AMOUNT_YOY,
            RANK_MARKERS, DATA_SOURCE, EMPHASIS_OR_EMERGING_MARK, CREATE_TIME
        ) VALUES (
            :1, :2, :3, :4, :5, :6,
            :7, :8, :9, :10,
            :11, :12, :13, :14,
            :15, :16, :17, :18,
            :19, :20, :21, :22,
            :23, :24, :25, :26,
            :27, :28, :29, :30,
            :31, :32, :33,
            :34, :35, :36,
            :37, :38, :39,
            :40, :41, :42,
            :43, :44, :45, :46
        )
        """
        
        # 分析SQL
        import re
        bind_vars = re.findall(r':(\d+)', insert_sql)
        unique_bind_vars = sorted(set(int(var) for var in bind_vars))
        
        print(f"\nSQL分析:")
        print(f"绑定变量数量: {len(unique_bind_vars)}")
        print(f"绑定变量范围: {min(unique_bind_vars)} - {max(unique_bind_vars)}")
        
        # 数据处理函数
        def get_numeric_value(value):
            if pd.isna(value) or value == '' or str(value).lower() == 'nan':
                return None
            try:
                return float(value)
            except (ValueError, TypeError):
                return None
        
        def get_string_value(value):
            if pd.isna(value) or value == '' or str(value).lower() == 'nan':
                return None
            return str(value).strip()
        
        def get_date_value(value):
            if pd.isna(value) or value == '' or str(value).lower() == 'nan':
                return None
            try:
                date_str = str(value).strip()
                if '/' in date_str and len(date_str.split('/')) == 3:
                    parts = date_str.split('/')
                    year, month, day = parts[0], parts[1].zfill(2), parts[2].zfill(2)
                    return f"{year}-{month}-{day}"
                elif '-' in date_str:
                    return date_str
                return str(value)
            except:
                return str(value) if not pd.isna(value) else None
        
        def get_datetime_value(value):
            if pd.isna(value) or value == '' or str(value).lower() == 'nan':
                return None
            try:
                datetime_str = str(value).strip()
                if '/' in datetime_str:
                    if ' ' in datetime_str:
                        date_part, time_part = datetime_str.split(' ', 1)
                        parts = date_part.split('/')
                        if len(parts) == 3:
                            year, month, day = parts[0], parts[1].zfill(2), parts[2].zfill(2)
                            return f"{year}-{month}-{day} {time_part}"
                    else:
                        parts = datetime_str.split('/')
                        if len(parts) == 3:
                            year, month, day = parts[0], parts[1].zfill(2), parts[2].zfill(2)
                            return f"{year}-{month}-{day} 00:00:00"
                elif '-' in datetime_str:
                    if ' ' not in datetime_str:
                        return f"{datetime_str} 00:00:00"
                    return datetime_str
                return str(value)
            except:
                return str(value) if not pd.isna(value) else None
        
        # 测试前3条记录的参数构建
        print(f"\n测试参数构建:")
        
        for index in range(min(3, len(df))):
            row = df.iloc[index]
            
            # 构建数据行（46个参数）
            data_row = [
                get_date_value(row['STAT_DATE']),
                get_string_value(row['STAT_TYPE']),
                get_string_value(row['STAT_NAME']),
                get_string_value(row['STAT_CODE']),
                get_string_value(row['STAT_CONTENT_RAW']),
                get_string_value(row['STAT_CONTENT_CLEANSE']),
                get_numeric_value(row['ACC_A_CNY_AMOUNT']),
                get_numeric_value(row['ACC_A_CNY_YOY']),
                get_numeric_value(row['ACC_A_USD_AMOUNT']),
                get_numeric_value(row['ACC_A_USD_YOY']),
                get_numeric_value(row['MON_A_CNY_AMOUNT']),
                get_numeric_value(row['MON_A_CNY_YOY']),
                get_numeric_value(row['MON_A_USD_AMOUNT']),
                get_numeric_value(row['MON_A_USD_YOY']),
                get_numeric_value(row['ACC_E_CNY_AMOUNT']),
                get_numeric_value(row['ACC_E_CNY_YOY']),
                get_numeric_value(row['ACC_E_USD_AMOUNT']),
                get_numeric_value(row['ACC_E_USD_YOY']),
                get_numeric_value(row['MON_E_CNY_AMOUNT']),
                get_numeric_value(row['MON_E_CNY_YOY']),
                get_numeric_value(row['MON_E_USD_AMOUNT']),
                get_numeric_value(row['MON_E_USD_YOY']),
                get_numeric_value(row['ACC_I_CNY_AMOUNT']),
                get_numeric_value(row['ACC_I_CNY_YOY']),
                get_numeric_value(row['ACC_I_USD_AMOUNT']),
                get_numeric_value(row['ACC_I_USD_YOY']),
                get_numeric_value(row['MON_I_CNY_AMOUNT']),
                get_numeric_value(row['MON_I_CNY_YOY']),
                get_numeric_value(row['MON_I_USD_AMOUNT']),
                get_numeric_value(row['MON_I_USD_YOY']),
                get_numeric_value(row['ACC_E_AMOUNT']),
                get_string_value(row['ACC_E_AMOUNT_UNIT']),
                get_numeric_value(row['ACC_E_AMOUNT_YOY']),
                get_numeric_value(row['MON_E_AMOUNT']),
                get_string_value(row['MON_E_AMOUNT_UNIT']),
                get_numeric_value(row['MON_E_AMOUNT_YOY']),
                get_numeric_value(row['ACC_I_AMOUNT']),
                get_string_value(row['ACC_I_AMOUNT_UNIT']),
                get_numeric_value(row['ACC_I_AMOUNT_YOY']),
                get_numeric_value(row['MON_I_AMOUNT']),
                get_string_value(row['MON_I_AMOUNT_UNIT']),
                get_numeric_value(row['MON_I_AMOUNT_YOY']),
                get_string_value(row['RANK_MARKERS']),
                get_string_value(row['DATA_SOURCE']),
                get_string_value(row['EMPHASIS_OR_EMERGING_MARK']),
                get_datetime_value(row['CREATE_TIME'])
            ]
            
            print(f"\n记录 {index+1}:")
            print(f"  参数数量: {len(data_row)}")
            print(f"  SQL绑定变量数量: {len(unique_bind_vars)}")
            print(f"  参数匹配: {'✅' if len(data_row) == len(unique_bind_vars) else '❌'}")
            
            # 显示关键参数
            key_params = [
                (1, 'STAT_DATE', data_row[0]),
                (2, 'STAT_TYPE', data_row[1]),
                (6, 'STAT_CONTENT_CLEANSE', data_row[5]),
                (37, 'ACC_I_AMOUNT', data_row[36]),
                (46, 'CREATE_TIME', data_row[45])
            ]
            
            print(f"  关键参数:")
            for param_num, field_name, value in key_params:
                print(f"    参数{param_num} ({field_name}): {value} ({type(value).__name__})")
        
        print(f"\n✅ 参数测试完成")
        print(f"SQL绑定变量数量: {len(unique_bind_vars)}")
        print(f"数据参数数量: {len(data_row)}")
        print(f"参数匹配状态: {'✅ 匹配' if len(data_row) == len(unique_bind_vars) else '❌ 不匹配'}")
        
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")

if __name__ == "__main__":
    # 测试文件
    test_file = '北京海关数据_完整转换结果/T_STATISTICAL_CUS_TOTAL_BEIJING_2024_(5)_（5）北京地区进出口商品贸易方式总值表（2024年1-10月）.csv'
    
    if os.path.exists(test_file):
        test_sql_parameters_without_db(test_file)
    else:
        # 查找第一个可用文件
        csv_dir = '北京海关数据_完整转换结果'
        if os.path.exists(csv_dir):
            csv_files = [f for f in os.listdir(csv_dir) if f.endswith('.csv')]
            if csv_files:
                first_file = os.path.join(csv_dir, csv_files[0])
                print(f"使用第一个可用文件: {csv_files[0]}")
                test_sql_parameters_without_db(first_file)
            else:
                print("未找到CSV文件")
        else:
            print(f"目录不存在: {csv_dir}")
