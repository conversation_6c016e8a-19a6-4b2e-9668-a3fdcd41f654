# 文件名: (15_16)统一-国家x商品入库.py
# 这是一个完整、可运行的、合并了(15)和(16)号表处理逻辑的最终版本。

import pandas as pd
import cx_Oracle
import os
import re
import sys
from tqdm import tqdm
from pathlib import Path
import numpy as np

def get_db_connection():
    """建立并返回数据库连接"""
    try:
        connection = cx_Oracle.connect("manifest_dcb/manifest_dcb@*************/TEST")
        return connection
    except Exception as e:
        print(f"数据库连接失败: {e}")
        sys.exit(1)

def get_unit_from_dataframe(df):
    """从DataFrame中动态查找单位信息"""
    for _, row in df.head(5).iterrows():
        for cell in row:
            if isinstance(cell, str) and '单位：' in cell:
                return cell.split('：')[1].strip()
    return "不明"

def parse_country_product_data(file_path):
    """
    【融合】一个通用的解析器，能够处理(15)和(16)号表相似的Excel二维表格结构。
    """
    filename = os.path.basename(file_path)
    date_match = re.search(r'(\d{4})年(?:1-)?(\d{1,2})月', filename)
    if not date_match:
        print(f"警告: 无法从文件名 '{filename}' 中提取日期。")
        return None
        
    year, month = date_match.groups()
    current_month_str = f"{year}-{int(month):02d}-01"
    
    try:
        df_raw = pd.read_excel(file_path, header=None, dtype=str, engine=None)
    except Exception as e:
        print(f"错误: 使用pandas读取文件失败: {file_path}, 错误: {e}")
        return None

    unit = get_unit_from_dataframe(df_raw)
    
    header_row_idx = 3
    data_start_row_idx = 5
    
    country_headers = df_raw.iloc[header_row_idx].values
    df_data = df_raw.iloc[data_start_row_idx:]

    processed_data = []
    for _, row in df_data.iterrows():
        hs_code_desc = row.iloc[1]
        if pd.isna(hs_code_desc) or not str(hs_code_desc).strip() or '总' in str(hs_code_desc) or '计' in str(hs_code_desc):
            continue

        for col_idx in range(2, len(country_headers), 2):
            country_name = country_headers[col_idx]
            if pd.isna(country_name) or '合计' in str(country_name):
                continue

            value_month = pd.to_numeric(row.iloc[col_idx], errors='coerce')
            value_ytd = pd.to_numeric(row.iloc[col_idx + 1], errors='coerce')

            if pd.notna(value_month) or pd.notna(value_ytd):
                processed_data.append({
                    "hs_code_desc": str(hs_code_desc).strip(),
                    "country": str(country_name).strip(),
                    "value_month": value_month,
                    "value_ytd": value_ytd,
                })
    
    if not processed_data:
        return None
        
    df = pd.DataFrame(processed_data)
    df['current_month'] = pd.to_datetime(current_month_str)
    df['unit'] = unit
    return df

def upsert_to_15_16(connection, df, trade_direction):
    """
    【新】使用MERGE语句将数据插入或更新到统一表 CUS_TRADE_COUNTRY_PRODUCT_MON
    """
    if df is None or df.empty:
        return 0

    cursor = connection.cursor()
    
    df['trade_direction'] = trade_direction
    df = df.replace({np.nan: None})
    # 为了让executemany能正确匹配占位符，将列名转为大写
    df.columns = [col.upper() for col in df.columns]
    data_to_merge = df.to_dict('records')

    # 【新】MERGE语句，目标是新表，ON条件和INSERT部分都增加了TRADE_DIRECTION
    merge_sql = """
    MERGE INTO CUS_TRADE_COUNTRY_PRODUCT_MON dest
    USING (
        SELECT
            :CURRENT_MONTH AS current_month,
            :CURRENCY_TYPE AS currency_type,
            :COUNTRY AS country,
            :HS_CODE_DESC AS hs_code_desc,
            :TRADE_DIRECTION AS trade_direction
        FROM dual
    ) src ON (
        dest.current_month = src.current_month AND
        dest.currency_type = src.currency_type AND
        dest.country = src.country AND
        dest.hs_code_desc = src.hs_code_desc AND
        dest.trade_direction = src.trade_direction
    )
    WHEN MATCHED THEN
        UPDATE SET
            dest.value_month = :VALUE_MONTH,
            dest.value_ytd = :VALUE_YTD,
            dest.unit = :UNIT
    WHEN NOT MATCHED THEN
        INSERT (
            current_month, currency_type, country, hs_code_desc, trade_direction,
            value_month, value_ytd, unit
        ) VALUES (
            :CURRENT_MONTH, :CURRENCY_TYPE, :COUNTRY, :HS_CODE_DESC, :TRADE_DIRECTION,
            :VALUE_MONTH, :VALUE_YTD, :UNIT
        )
    """
    
    try:
        cursor.executemany(merge_sql, data_to_merge)
        connection.commit()
        print(f"        -> 成功合并 {cursor.rowcount} 条 [{trade_direction}] 记录.")
        return cursor.rowcount
    except Exception as e:
        print(f"        [!] 执行批量合并时发生严重错误: {e}")
        connection.rollback()
        return 0
    finally:
        cursor.close()

def process_directory(directory_path, trade_direction, connection):
    """【通用】处理指定目录下的所有Excel文件"""
    base_dir = Path(directory_path)
    if not base_dir.exists():
        print(f"错误: 数据目录不存在 -> {base_dir}，跳过处理。")
        return

    files_to_process = sorted(list(base_dir.rglob('*.xlsx')))
    
    print(f"\n--- 开始处理目录 ({trade_direction}): {base_dir.name} ---")
    
    for file_path in tqdm(files_to_process, desc=f"处理 {base_dir.name}"):
        if file_path.name.startswith('~'):
            continue
            
        df = parse_country_product_data(str(file_path))
        
        if df is not None and not df.empty:
            df['currency_type'] = "人民币" if "人民币" in str(file_path) else "美元"
            upsert_to_15_16(connection, df, trade_direction)
        else:
            print(f"    -> 文件 {file_path.name} 解析为空或失败，跳过。")

def main():
    """【新】主执行函数，自动处理人民币和美元的进出口四个目录"""
    base_dir = os.getcwd()
    # (15) 出口目录
    exp_rmb_dir_15 = os.path.join(base_dir, '进出口商品统计表_人民币值', '(15)对部分国家(地区)出口商品类章金额表_人民币值')
    exp_usd_dir_15 = os.path.join(base_dir, '进出口商品统计表_美元值', '(15)对部分国家(地区)出口商品类章金额表_美元值')
    # (16) 进口目录
    imp_rmb_dir_16 = os.path.join(base_dir, '进出口商品统计表_人民币值', '(16)自部分国家(地区)进口商品类章金额表_人民币值')
    imp_usd_dir_16 = os.path.join(base_dir, '进出口商品统计表_美元值', '(16)自部分国家(地区)进口商品类章金额表_美元值')

    connection = None
    try:
        connection = get_db_connection()
        print("数据库连接成功。")
        
        # 按顺序处理所有目录，并传入正确的贸易方向
        process_directory(exp_rmb_dir_15, '出口', connection)
        process_directory(exp_usd_dir_15, '出口', connection)
        process_directory(imp_rmb_dir_16, '进口', connection)
        process_directory(imp_usd_dir_16, '进口', connection)

        print("\n--- (15)和(16)所有目录处理完毕 ---")

    except Exception as e:
        print(f"\n处理过程中发生未预料的错误: {e}")
    finally:
        if connection:
            connection.close()
            print("数据库连接已关闭。")

if __name__ == "__main__":
    main() 