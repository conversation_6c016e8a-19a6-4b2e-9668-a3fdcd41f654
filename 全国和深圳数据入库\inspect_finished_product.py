import pandas as pd

try:
    # 数据分析师的成品文件
    analyst_file = 'T_STATISTICAL_CUS_TOTAL（1-5月全国）.xlsx'
    sheet_name = '主出商品'

    # 读取指定的sheet页
    df = pd.read_excel(analyst_file, sheet_name=sheet_name)

    # 打印列名和前5行
    print(f"--- 分析师成品 '{analyst_file}' (Sheet: {sheet_name}) ---")
    print("\n列名:")
    print(df.columns.tolist())
    print("\n数据预览 (前5行):")
    print(df.head())

except FileNotFoundError:
    print(f"错误: 文件 '{analyst_file}' 未找到。")
except ValueError:
    print(f"错误: 在文件中找不到名为 '{sheet_name}' 的sheet页。")
except Exception as e:
    print(f"读取文件时出现错误: {e}") 