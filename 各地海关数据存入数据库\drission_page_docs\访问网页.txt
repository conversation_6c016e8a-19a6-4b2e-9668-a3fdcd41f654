Title: 🛰️ 访问网页
URL: https://www.drissionpage.cn/browser_control/visit/

🛰️ 访问网页
云服务器38元/年起，大模型限免超7000万 tokens广告


本节介绍 Tab 对象访问网页的相关内容。
✅️️ 连接方法​
📌 get()​
该方法用于跳转到一个网址。当连接失败时，程序会进行重试。
可指定本地文件路径。
参数名称	类型	默认值	说明
url	str	必填	目标 url，可指向本地文件路径
show_errmsg	bool	False	连接出错时是否显示和抛出异常
retry	int	None	重试次数，为None时使用页面参数，默认3
interval	float	None	重试间隔（秒），为None时使用页面参数，默认2
timeout	float	None	加载超时时间（秒）
-------	-------	---	------ 以下参数仅 s 模式有效 ------
params	dict	None	url 请求参数
data	dict
str	None	携带的数据
json	dict
str	None	要发送的 JSON 数据，会自动设置 Content-Type 为'application/json'
headers	dict	None	请求头
cookies	dict
CookieJar	None	cookies 信息
files	Any	None	要上传的文件，可以是一个字典，其中键是文件名，值是文件对象或文件路径
auth	Any	None	身份认证信息
allow_redirects	bool	True	是否允许重定向
proxies	dict	None	代理信息
hooks	Any	None	回调方法
stream	bool	None	是否使用流式传输
verify	bool
str	None	是否验证 SSL 证书
cert	str
Tuple[str, str]	None	SSL 客户端证书文件的路径(.pem 格式)，或('cert', 'key')元组
返回类型	说明
bool	访问是否成功
示例：
from DrissionPage import Chromium

tab = Chromium().latest_tab
tab.get('http://DrissionPage.cn')

📌 post()​
此方法用内置的Session对象以 POST 方式发送请求。
因为post()是使用requests的post()方法发送请求，参数和用法与requests一致。
此方法返回请求结果Response对象。
s 模式时，post()后结果还可用页面对象的html或json属性获取。
参数名称	类型	默认值	说明
url	str	必填	目标 url，可指向本地文件路径
show_errmsg	bool	False	连接出错时是否显示和抛出异常
retry	int	None	重试次数，为None时使用页面参数，默认3
interval	float	None	重试间隔（秒），为None时使用页面参数，默认2
timeout	float	None	加载超时时间（秒）
params	dict	None	url 请求参数
data	dict
str	None	携带的数据
json	dict
str	None	要发送的 JSON 数据，会自动设置 Content-Type 为'application/json'
headers	dict	None	请求头
cookies	dict
CookieJar	None	cookies 信息
files	Any	None	要上传的文件，可以是一个字典，其中键是文件名，值是文件对象或文件路径
auth	Any	None	身份认证信息
allow_redirects	bool	True	是否允许重定向
proxies	dict	None	代理信息
hooks	Any	None	回调方法
stream	bool	None	是否使用流式传输
verify	bool
str	None	是否验证 SSL 证书
cert	str
Tuple[str, str]	None	SSL 客户端证书文件的路径(.pem 格式)，或('cert', 'key')元组
返回类型	说明
Response	获取到的Response对象
✅️️ 设置超时和重试​
网络不稳定时，访问页面不一定成功，get()方法内置了超时和重试功能。通过retry、interval、timeout三个参数进行设置。
其中，如不指定timeout参数，该参数会使用ChromiumPage的timeouts属性的page_load参数中的值。
from DrissionPage import Chromium

tab = Chromium().latest_tab
tab.get('http://DrissionPage.cn', retry=1, interval=1, timeout=1.5)

✅️️ 加载模式​
📌 概述​
加载模式是指 d 模式下程序在页面加载阶段的行为模式，有以下三种：
normal()：常规模式，会等待页面加载完毕，超时自动重试或停止，默认使用此模式
eager()：加载完 DOM 或超时即停止加载，不加载页面资源
none()：超时也不会自动停止，除非加载完成
前两种模式下，页面加载过程会阻塞程序，直到加载完毕才执行后面的操作。
none()模式下，只在连接阶段阻塞程序，加载阶段可自行根据情况执行stop_loading()停止加载。
这样提供给用户非常大的自由度，可等到关键数据包或元素出现就主动停止页面加载，大幅提升执行效率。
注意
加载完成是指主文档完成，并不包括由 js 触发的加载和重定向的加载。 当文档加载完成，程序就判断加载完毕，此后发生的重定向或 js 加载数据需用其它逻辑处理。
示例：
from DrissionPage import Chromium

tab = Chromium().latest_tab
tab.set.load_mode.eager()  # 设置为eager模式
tab.get('http://DrissionPage.cn')

📌 模式设置​
可通过 ini 文件、ChromiumOptions对象和页面对象的set.load_mode.****()方法进行设置。
运行时可随时动态设置。
配置对象中设置
from DrissionPage import ChromiumOptions, Chromium

co = ChromiumOptions().set_load_mode('none')
browser = Chromium(co)

运行中设置
from DrissionPage import Chromium

tab = Chromium().latest_tab
tab.set.load_mode.none()

📌 none模式技巧​
示例 1，配合监听器
跟监听器配合，可在获取到需要的数据包时，主动停止加载。
from DrissionPage import Chromium

tab = Chromium().latest_tab
tab.set.load_mode.none()  # 设置加载模式为none

tab.listen.start('api/getkeydata')  # 指定监听目标并启动监听
tab.get('http://www.hao123.com/')  # 访问网站
packet = tab.listen.wait()  # 等待数据包
tab.stop_loading()  # 主动停止加载
print(packet.response.body)  # 打印数据包正文

示例 2，配合元素查找
跟元素查找配合，可在获取到某个指定元素时，主动停止加载。
from DrissionPage import Chromium

tab = Chromium().latest_tab
tab.set.load_mode.none()  # 设置加载模式为none

tab.get('http://www.hao123.com/')  # 访问网站
ele = tab.ele('中国日报')  # 查找text包含“中国日报”的元素
tab.stop_loading()  # 主动停止加载
print(ele.text)  # 打印元素text

示例 2，配合页面特征
可等待到页面到达某种状态时，主动停止加载。比如多级跳转的登录，可等待 title 变化到最终目标网址时停止。
from DrissionPage import Chromium

tab = Chromium().latest_tab
tab.set.load_mode.none()  # 设置加载模式为none

tab.get('http://www.hao123.com/')  # 访问网站
tab.wait.title_change('hao123')  # 等待title变化出现目标文本
tab.stop_loading()  # 主动停止加载