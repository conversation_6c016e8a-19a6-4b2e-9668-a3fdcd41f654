"""
检查和修正海关统计数据的DATA_SOURCE字段
"""

import sys

# 检查是否安装了cx_Oracle
try:
    import cx_Oracle
    ORACLE_AVAILABLE = True
except ImportError:
    ORACLE_AVAILABLE = False
    print("警告：未安装cx_Oracle模块")
    print("如需连接数据库，请运行：pip install cx_Oracle")

def check_data_source_distribution(db_config):
    """
    检查当前数据源分布情况
    """
    if not ORACLE_AVAILABLE:
        print("❌ 无法连接数据库：cx_Oracle模块未安装")
        return
    
    try:
        conn = cx_Oracle.connect(**db_config)
        cursor = conn.cursor()
        
        print("=" * 80)
        print("当前DATA_SOURCE分布情况")
        print("=" * 80)
        
        # 查看数据源分布
        cursor.execute("""
            SELECT DATA_SOURCE, COUNT(*) as 记录数, 
                   MIN(CREATE_TIME) as 最早时间,
                   MAX(CREATE_TIME) as 最新时间
            FROM T_STATISTICAL_CUS_TOTAL_CS 
            GROUP BY DATA_SOURCE 
            ORDER BY DATA_SOURCE
        """)
        
        results = cursor.fetchall()
        print(f"{'DATA_SOURCE':<12} {'记录数':<10} {'最早时间':<20} {'最新时间':<20}")
        print("-" * 80)
        
        for row in results:
            print(f"{row[0]:<12} {row[1]:<10} {str(row[2]):<20} {str(row[3]):<20}")
        
        # 查看各数据源的统计类型分布
        print(f"\n" + "=" * 80)
        print("各DATA_SOURCE的统计类型分布")
        print("=" * 80)
        
        cursor.execute("""
            SELECT DATA_SOURCE, STAT_TYPE, STAT_NAME, COUNT(*) as 记录数
            FROM T_STATISTICAL_CUS_TOTAL_CS 
            GROUP BY DATA_SOURCE, STAT_TYPE, STAT_NAME
            ORDER BY DATA_SOURCE, STAT_TYPE
        """)
        
        results = cursor.fetchall()
        current_source = None
        
        for row in results:
            if row[0] != current_source:
                current_source = row[0]
                print(f"\nDATA_SOURCE = '{current_source}':")
                print(f"{'统计类型':<15} {'统计名称':<15} {'记录数':<10}")
                print("-" * 50)
            
            print(f"{row[1]:<15} {row[2]:<15} {row[3]:<10}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库查询失败: {e}")

def fix_data_source_codes(db_config, execute_fix=False):
    """
    修正DATA_SOURCE编码
    """
    if not ORACLE_AVAILABLE:
        print("❌ 无法连接数据库：cx_Oracle模块未安装")
        return
    
    try:
        conn = cx_Oracle.connect(**db_config)
        cursor = conn.cursor()
        
        print("\n" + "=" * 80)
        print("DATA_SOURCE修正建议")
        print("=" * 80)
        print("根据表注释，正确的DATA_SOURCE编码：")
        print("01：全国")
        print("02：天津")  
        print("03：上海")
        print("04：南京")
        print("05：杭州")
        print("06：宁波")
        print("07：浙江")
        print("08：北京（建议新增）")
        print("-" * 80)
        
        # 检查可能的浙江数据（当前标记为'02'但应该是'07'）
        cursor.execute("""
            SELECT COUNT(*) 
            FROM T_STATISTICAL_CUS_TOTAL_CS 
            WHERE DATA_SOURCE = '02'
              AND CREATE_TIME < '2025/08/04 10:00:00'
        """)
        zhejiang_count = cursor.fetchone()[0]
        
        # 检查可能的北京数据（当前标记为'01'但应该是'08'）
        cursor.execute("""
            SELECT COUNT(*) 
            FROM T_STATISTICAL_CUS_TOTAL_CS 
            WHERE DATA_SOURCE = '01'
              AND CREATE_TIME >= '2025/08/04 10:00:00'
        """)
        beijing_count = cursor.fetchone()[0]
        
        print(f"检测到需要修正的数据：")
        print(f"- 浙江数据（错误标记为'02'天津）：{zhejiang_count} 条")
        print(f"- 北京数据（错误标记为'01'全国）：{beijing_count} 条")
        
        if execute_fix:
            print(f"\n开始执行修正...")
            
            # 修正浙江数据
            if zhejiang_count > 0:
                cursor.execute("""
                    UPDATE T_STATISTICAL_CUS_TOTAL_CS 
                    SET DATA_SOURCE = '07'
                    WHERE DATA_SOURCE = '02'
                      AND CREATE_TIME < '2025/08/04 10:00:00'
                """)
                print(f"✅ 已将 {cursor.rowcount} 条浙江数据的DATA_SOURCE从'02'改为'07'")
            
            # 修正北京数据
            if beijing_count > 0:
                cursor.execute("""
                    UPDATE T_STATISTICAL_CUS_TOTAL_CS 
                    SET DATA_SOURCE = '08'
                    WHERE DATA_SOURCE = '01'
                      AND CREATE_TIME >= '2025/08/04 10:00:00'
                """)
                print(f"✅ 已将 {cursor.rowcount} 条北京数据的DATA_SOURCE从'01'改为'08'")
            
            conn.commit()
            print(f"✅ 修正完成并已提交")
            
        else:
            print(f"\n如需执行修正，请运行：")
            print(f"python check_and_fix_data_source.py --fix")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库操作失败: {e}")

def main():
    """
    主函数
    """
    # 数据库连接配置
    db_config = {
        'user': 'manifest_dcb',
        'password': 'manifest_dcb',
        'dsn': '192.168.1.151/TEST'
    }
    
    print("海关统计数据DATA_SOURCE字段检查和修正工具")
    print("=" * 80)
    
    # 检查命令行参数
    execute_fix = '--fix' in sys.argv
    
    # 检查当前数据源分布
    check_data_source_distribution(db_config)
    
    # 修正数据源编码
    fix_data_source_codes(db_config, execute_fix)
    
    if not execute_fix:
        print(f"\n" + "=" * 80)
        print("注意：当前为检查模式，未执行任何修改")
        print("如需执行修正，请运行：python check_and_fix_data_source.py --fix")
        print("=" * 80)

if __name__ == '__main__':
    main()