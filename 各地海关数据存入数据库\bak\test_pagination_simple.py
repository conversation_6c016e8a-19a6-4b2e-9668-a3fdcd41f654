from DrissionPage import ChromiumPage
import time
import re

def is_target_table(text):
    """检查链接文本是否是我们需要的目标表格"""
    if "北京地区" in text:
        if "（5）" in text and "贸易方式" in text and "总值表" in text and "企业性质" not in text:
            return True, "(5)"
        elif "（6）" in text and "出口" in text and "贸易方式" in text and "企业性质" in text:
            return True, "(6)"
        elif "（7）" in text and "进口" in text and "贸易方式" in text and "企业性质" in text:
            return True, "(7)"
        elif "（8）" in text and "出口" in text and "主要商品" in text and "量值表" in text:
            return True, "(8)"
        elif "（9）" in text and "进口" in text and "主要商品" in text and "量值表" in text:
            return True, "(9)"
    return False, None

def test_pagination_and_collect():
    """测试翻页并收集所有目标数据"""

    print("=== 测试翻页并收集北京海关数据 ===")

    page = ChromiumPage(timeout=30)

    try:
        all_target_links = []

        # 测试前10页
        for page_num in range(1, 11):
            if page_num == 1:
                url = "http://beijing.customs.gov.cn/beijing_customs/434756/434804/2941702/434773/434774/index.html"
            else:
                url = f"http://beijing.customs.gov.cn/beijing_customs/434756/434804/2941702/434773/434774/16673809-{page_num}.html"

            print(f"\n=== 访问第 {page_num} 页 ===")
            print(f"URL: {url}")

            try:
                page.get(url)
                time.sleep(3)

                print(f"页面标题: {page.title}")

                # 检查页面是否有效
                if not page.eles("统计数据"):
                    print(f"第 {page_num} 页无效，停止翻页")
                    break

                # 查找目标链接
                page_target_links = []
                all_links = page.eles('tag:a')

                for link in all_links:
                    link_text = link.text.strip()
                    href = link.attr('href')

                    if not href or not link_text:
                        continue

                    # 检查是否包含目标年份
                    if any(year in link_text for year in ['2024', '2025']):
                        # 使用目标表格检查函数
                        is_target, table_key = is_target_table(link_text)

                        if is_target:
                            # 构建完整URL
                            if href.startswith('/'):
                                full_url = "http://beijing.customs.gov.cn" + href
                            else:
                                full_url = href

                            link_info = {
                                'text': link_text,
                                'url': full_url,
                                'year': '2024' if '2024' in link_text else '2025',
                                'table_key': table_key,
                                'page': page_num
                            }

                            page_target_links.append(link_info)
                            all_target_links.append(link_info)

                            print(f"找到目标: {table_key} - {link_text}")

                print(f"第 {page_num} 页找到 {len(page_target_links)} 个目标链接")

                # 如果连续几页都没有目标链接，可能已经到底了
                if len(page_target_links) == 0 and page_num > 3:
                    print(f"第 {page_num} 页没有目标链接，可能已经到底")
                    break

            except Exception as e:
                print(f"访问第 {page_num} 页时出错: {e}")
                break

            time.sleep(2)  # 防止请求过快

        print(f"\n=== 收集完成 ===")
        print(f"总共找到 {len(all_target_links)} 个目标链接")

        # 按年份统计
        year_2024_count = len([link for link in all_target_links if link['year'] == '2024'])
        year_2025_count = len([link for link in all_target_links if link['year'] == '2025'])

        print(f"2024年数据: {year_2024_count} 个")
        print(f"2025年数据: {year_2025_count} 个")

        # 保存结果
        with open('北京海关数据链接_翻页收集.txt', 'w', encoding='utf-8') as f:
            f.write("页码\t年份\t表格编号\t链接文本\t完整URL\n")
            for link in all_target_links:
                f.write(f"{link['page']}\t{link['year']}\t{link['table_key']}\t{link['text']}\t{link['url']}\n")

        print("结果已保存到: 北京海关数据链接_翻页收集.txt")

        return all_target_links

    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return []

    finally:
        page.quit()

if __name__ == "__main__":
    test_pagination_and_collect()