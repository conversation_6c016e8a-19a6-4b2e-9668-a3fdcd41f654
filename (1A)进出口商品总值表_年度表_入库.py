import pandas as pd
import cx_Oracle
import os
import re
import sys
from tqdm import tqdm
from pathlib import Path
import numpy as np

def get_db_connection():
    """建立并返回数据库连接"""
    try:
        connection = cx_Oracle.connect("manifest_dcb/manifest_dcb@*************/TEST")
        return connection
    except Exception as e:
        print(f"数据库连接失败: {e}")
        sys.exit(1)

def convert_to_float_or_none(value):
    """清理数据，将'-'或空值转为None，其余转为浮点数"""
    if pd.isna(value) or str(value).strip() in ['-', '…']:
        return None
    try:
        return float(str(value).replace(',', ''))
    except (ValueError, TypeError):
        return None

def parse_annual_table(file_path):
    """
    重构后的解析器，专门处理 (1)A 年度表的时间序列格式。
    能够正确处理双层表头，并逐行读取年度数据。
    """
    try:
        df_raw = pd.read_excel(file_path, header=None)

        # 1. 动态查找单位
        unit = "不明"
        header_search_start_row = 0
        for i in range(min(10, len(df_raw))):
            row_text = ' '.join(str(s) for s in df_raw.iloc[i].tolist() if pd.notna(s))
            unit_match = re.search(r'单位[：:]\s*([^\s]+)', row_text)
            if unit_match:
                unit = unit_match.group(1).strip()
                header_search_start_row = i + 1
                break
        
        # 2. 查找双层表头的起始行
        header_start_row = -1
        for i, row in df_raw.iloc[header_search_start_row:].iterrows():
            row_str = ' '.join(str(s) for s in row if pd.notna(s))
            if '年度' in row_str and '进出口' in row_str and '比去年同期±％' in row_str:
                header_start_row = i
                break
        
        if header_start_row == -1:
            print(f"    [!] 错误: 在文件 '{os.path.basename(file_path)}' 中未找到预期的表头行。")
            return None, None

        # 3. 数据从表头的下两行开始
        data_start_row = header_start_row + 2
        df_data = df_raw.iloc[data_start_row:]
        
        processed_data = []
        # 4. 遍历每一行，将其作为一条年度数据
        for _, row in df_data.iterrows():
            # 第一列是年份
            year_val = row.iloc[1]
            if pd.isna(year_val) or not str(year_val).strip().isdigit():
                continue # 如果年份列为空或不是纯数字，则跳过

            data_record = {
                'data_year': str(int(year_val)),
                'ie_value': convert_to_float_or_none(row.iloc[2]),
                'export_value': convert_to_float_or_none(row.iloc[3]),
                'import_value': convert_to_float_or_none(row.iloc[4]),
                'trade_balance': convert_to_float_or_none(row.iloc[5]),
                'ie_yoy': convert_to_float_or_none(row.iloc[6]),
                'export_yoy': convert_to_float_or_none(row.iloc[7]),
                'import_yoy': convert_to_float_or_none(row.iloc[8]),
                'trade_balance_yoy': None # A表中似乎没有贸易差额的同比增长列
            }
            processed_data.append(data_record)
            
        return pd.DataFrame(processed_data), unit

    except Exception as e:
        print(f"    [!] 解析文件 '{os.path.basename(file_path)}' 时出错: {e}")
        return None, None

def get_year_from_filename(filename):
    """从文件名中提取年份"""
    match = re.search(r'(\d{4})', filename)
    if match:
        return match.group(1)
    return None

def upsert_data(connection, df_to_process):
    """使用 MERGE 语句将数据插入或更新到数据库 (DataFrame中应已包含所有需要的数据)"""
    if df_to_process is None or df_to_process.empty:
        print("    -> 无数据传入，跳过数据库操作。")
        return 0

    cursor = connection.cursor()
    df_to_process.replace({np.nan: None}, inplace=True)
    data_to_merge = df_to_process.to_dict('records')

    merge_sql = """
    MERGE INTO temp_cus_year_1a dest
    USING (
        SELECT :data_year AS data_year, :currency_type AS currency_type FROM dual
    ) src ON (
        dest.data_year = src.data_year AND
        dest.currency_type = src.currency_type
    )
    WHEN MATCHED THEN
        UPDATE SET
            dest.unit = :unit,
            dest.ie_value = :ie_value,
            dest.export_value = :export_value,
            dest.import_value = :import_value,
            dest.trade_balance = :trade_balance,
            dest.ie_yoy = :ie_yoy,
            dest.export_yoy = :export_yoy,
            dest.import_yoy = :import_yoy,
            dest.trade_balance_yoy = :trade_balance_yoy,
            dest.created_at = CURRENT_TIMESTAMP
    WHEN NOT MATCHED THEN
        INSERT (
            data_year, currency_type, unit,
            ie_value, export_value, import_value, trade_balance,
            ie_yoy, export_yoy, import_yoy, trade_balance_yoy
        ) VALUES (
            :data_year, :currency_type, :unit,
            :ie_value, :export_value, :import_value, :trade_balance,
            :ie_yoy, :export_yoy, :import_yoy, :trade_balance_yoy
        )
    """
    
    try:
        cursor.executemany(merge_sql, data_to_merge, batcherrors=True)
        error_count = len(cursor.getbatcherrors())
        merged_count = cursor.rowcount
        
        if error_count > 0:
            print(f"    [!] 批量合并时发生 {error_count} 个错误。")

        connection.commit()
        print(f"    -> 成功合并 (插入/更新) {merged_count} 条记录。")
        return merged_count
    except Exception as e:
        print(f"    [!] 执行批量合并时发生严重错误: {e}")
        connection.rollback()
        return 0
    finally:
        cursor.close()

def process_directory(directory_path, connection):
    """处理指定目录下的所有年度Excel文件"""
    base_dir = Path(directory_path)
    if not base_dir.is_dir():
        print(f"错误: 数据目录不存在 -> {base_dir}，跳过处理。")
        return 0

    files_to_process = sorted(list(base_dir.rglob('*.xlsx'))) + sorted(list(base_dir.rglob('*.xls')))
    if not files_to_process:
        print(f"在目录 {base_dir.name} 中未找到任何 Excel 文件。")
        return 0
    
    print(f"\n--- 开始处理目录: {base_dir.name} ---")
    print(f"发现 {len(files_to_process)} 个文件待处理。")
    
    total_merged = 0
    for file_path in tqdm(files_to_process, desc=f"处理 {base_dir.name}"):
        if file_path.name.startswith('~'):
            continue
        
        currency_type = "人民币" if "人民币" in str(file_path) else "美元"
        
        df, unit = parse_annual_table(str(file_path))
        
        if df is not None and not df.empty:
            df['unit'] = unit
            df['currency_type'] = currency_type
            merged_count = upsert_data(connection, df)
            total_merged += merged_count
        else:
            print(f"    -> 文件 {file_path.name} 解析为空或失败，跳过。")
            
    return total_merged

def main():
    """主执行函数，自动处理人民币和美元两个目录"""
    base_dir = os.getcwd()
    rmb_dir = os.path.join(base_dir, "进出口商品统计表_人民币值", "(1)A进出口商品总值表_A年度表_人民币值")
    usd_dir = os.path.join(base_dir, "进出口商品统计表_美元值", "(1)A进出口商品总值表_A年度表_美元值")

    connection = None
    try:
        connection = get_db_connection()
        print("数据库连接成功。")
        
        rmb_merged = process_directory(rmb_dir, connection)
        usd_merged = process_directory(usd_dir, connection)

        print("\n--- 所有目录处理完毕 ---")
        print(f"总计合并 (插入/更新) 了 {rmb_merged + usd_merged} 条记录。")

    except Exception as e:
        print(f"\n处理过程中发生未预料的错误: {e}")
    finally:
        if connection:
            connection.close()
            print("数据库连接已关闭。")

if __name__ == "__main__":
    main() 