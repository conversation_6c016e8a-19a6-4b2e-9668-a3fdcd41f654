import pandas as pd
import os
import glob

# 设置正确的目录
base_dir = r'C:\Users\<USER>\Desktop\海关数据\小超海关统计\全国进出口\各地海关数据存入数据库'
data_dir = os.path.join(base_dir, '北京单月的')

print(f"基础目录: {base_dir}")
print(f"数据目录: {data_dir}")

# 找到所有xls文件
pattern_glob = os.path.join(data_dir, "*.xls")
xls_files = glob.glob(pattern_glob)
print(f"\n找到 {len(xls_files)} 个.xls文件:")

for i, xls_file in enumerate(xls_files):
    print(f"\n=== 文件 {i+1}: {os.path.basename(xls_file)} ===")
    try:
        # 尝试读取文件
        df = pd.read_excel(xls_file, engine='xlrd', header=None)
        print(f"数据形状: {df.shape}")
        print("前10行数据:")
        print(df.head(10))
        print("\n列名:")
        print(df.columns.tolist())
        
        # 尝试理解数据结构
        print("\n数据类型:")
        print(df.dtypes)
        
        # 查看是否有NaN值
        print(f"\nNaN值统计:")
        print(df.isnull().sum())
        
    except Exception as e:
        print(f"读取失败: {e}")
        try:
            df = pd.read_excel(xls_file, engine='openpyxl', header=None)
            print(f"使用openpyxl读取成功")
            print(f"数据形状: {df.shape}")
            print("前10行数据:")
            print(df.head(10))
        except Exception as e2:
            print(f"使用openpyxl也失败: {e2}")