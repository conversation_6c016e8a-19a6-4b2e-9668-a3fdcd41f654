import pandas as pd
import os
import re
import cx_Oracle
import numpy as np

def convert_to_float_or_none(value):
    """清理数据，将'-'或空值转为None，其余转为浮点数"""
    if pd.isna(value) or str(value).strip() in ['-', '…']:
        return None
    try:
        return float(str(value).replace(',', ''))
    except (ValueError, TypeError):
        return None

def parse_table_12(file_path):
    """
    专门为(12)外商投资企业进出口总值表编写的解析器。
    能够处理其复杂的多行表头和10列数据结构。
    返回一个元组 (DataFrame, unit)
    """
    try:
        df_raw = pd.read_excel(file_path, header=None)

        unit = None
        header_search_start_row = 0
        for i in range(min(10, len(df_raw))):
            row_text = ' '.join(str(s) for s in df_raw.iloc[i].tolist() if pd.notna(s))
            unit_match = re.search(r'单位[：:]\s*([^\s]+)', row_text)
            if unit_match:
                unit = unit_match.group(1).strip()
                header_search_start_row = i + 1
                break
        
        header_start_row = -1
        for i, row in df_raw.iloc[header_search_start_row:].iterrows():
            row_str = ' '.join(str(s) for s in row if pd.notna(s))
            # 关键标识改为 "外商投资企业"
            if '外商投资企业' in row_str and '出口' in row_str and '进口' in row_str:
                header_start_row = i
                break
        
        if header_start_row == -1:
            print(f"    [!] 错误: 在文件 '{os.path.basename(file_path)}' 中未找到表头行。")
            return None, None

        data_start_row = header_start_row + 2
        df_data = df_raw.iloc[data_start_row:, 1:12].copy()
        
        processed_data = []
        for i in range(len(df_data)):
            row = df_data.iloc[i].values
            
            location = row[0]
            if pd.isna(location) or str(location).strip().startswith('注'):
                continue
            
            processed_data.append({
                'ENTERPRISE_LOCATION': str(location).strip(),
                'MONTH_IE_VALUE': convert_to_float_or_none(row[1]),
                'YTD_IE_VALUE': convert_to_float_or_none(row[2]),
                'MONTH_EXPORT_VALUE': convert_to_float_or_none(row[3]),
                'YTD_EXPORT_VALUE': convert_to_float_or_none(row[4]),
                'MONTH_IMPORT_VALUE': convert_to_float_or_none(row[5]),
                'YTD_IMPORT_VALUE': convert_to_float_or_none(row[6]),
                'YOY_IE': convert_to_float_or_none(row[7]),
                'YOY_EXPORT': convert_to_float_or_none(row[8]),
                'YOY_IMPORT': convert_to_float_or_none(row[9])
            })

        return pd.DataFrame(processed_data), unit

    except Exception as e:
        print(f"    [!] 解析文件 '{os.path.basename(file_path)}' 时出错: {e}")
        return None, None

def get_current_month_from_filename(filename):
    """从文件名中提取年月"""
    match = re.search(r'(\d{4})年(?:1至)?(\d{1,2})月', filename)
    if match:
        year, month = match.groups()
        return f"{year}{month.zfill(2)}01"
    return None

def batch_process_directory(directory_path, cursor, conn):
    files_to_process = []
    if os.path.isdir(directory_path):
        files_to_process = [f for f in sorted(os.listdir(directory_path)) if f.lower().endswith((".xlsx", ".xls"))]
    
    print(f"\n--- 正在扫描目录: {directory_path} ---")
    print(f"--- 发现 {len(files_to_process)} 个Excel文件 ---")
    
    if not files_to_process:
        print(f"    [!] 警告: 目录不存在或为空，跳过。")
        return

    for filename in files_to_process:
        file_path = os.path.join(directory_path, filename)
        current_month_str = get_current_month_from_filename(filename)
        
        print(f"\n    [*] 正在处理: {filename}")

        if not current_month_str:
            print(f"        [!] 无法从文件名中提取年月，跳过。")
            continue
        
        currency_type = "人民币" if "人民币" in directory_path else "美元"
        
        try:
            df, unit_from_file = parse_table_12(file_path)
            
            if df is None:
                print(f"        -> 文件解析失败(返回None)，跳过。")
                continue

            unit = unit_from_file if unit_from_file else ("万元" if currency_type == "人民币" else "千美元")
            print(f"        -> 文件解析成功，获得 {len(df)} 行数据。单位: {unit}")

            if df.empty:
                print(f"        -> 解析后数据为空，不执行数据库操作。")
                continue
            
            select_keys_query = "SELECT ENTERPRISE_LOCATION FROM temp_cus_mon_12 WHERE CURRENT_MONTH = TO_DATE(:1, 'YYYYMMDD') AND CURRENCY_TYPE = :2"
            cursor.execute(select_keys_query, (current_month_str, currency_type))
            existing_locations = {row[0] for row in cursor.fetchall()}
            print(f"        -> 数据库中已存在 {len(existing_locations)} 条 {current_month_str[:6]}月 {currency_type} 的记录。")

            df_new = df[~df['ENTERPRISE_LOCATION'].isin(existing_locations)]
            if df_new.empty:
                print(f"        -> 无新数据需要插入(数据可能已存在或被过滤)，跳过。")
                continue
            
            print(f"        -> 筛选出 {len(df_new)} 条新数据准备插入。")

            df_new = df_new.copy()
            df_new['CURRENT_MONTH'] = current_month_str
            df_new['CURRENCY_TYPE'] = currency_type
            df_new['UNIT'] = unit
            df_new.replace({np.nan: None}, inplace=True)
            
            data_to_insert = df_new.to_dict('records')
            
            insert_query = """
            INSERT INTO temp_cus_mon_12 (
                ENTERPRISE_LOCATION, MONTH_IE_VALUE, YTD_IE_VALUE, MONTH_EXPORT_VALUE, YTD_EXPORT_VALUE,
                MONTH_IMPORT_VALUE, YTD_IMPORT_VALUE, YOY_IE, YOY_EXPORT, YOY_IMPORT,
                CURRENT_MONTH, CURRENCY_TYPE, UNIT
            ) VALUES (
                :ENTERPRISE_LOCATION, :MONTH_IE_VALUE, :YTD_IE_VALUE, :MONTH_EXPORT_VALUE, :YTD_EXPORT_VALUE,
                :MONTH_IMPORT_VALUE, :YTD_IMPORT_VALUE, :YOY_IE, :YOY_EXPORT, :YOY_IMPORT,
                TO_DATE(:CURRENT_MONTH, 'YYYYMMDD'), :CURRENCY_TYPE, :UNIT
            )
            """
            
            cursor.executemany(insert_query, data_to_insert)
            print(f"        -> 成功将 {cursor.rowcount} 条新记录插入数据库。")

        except cx_Oracle.Error as e:
            print(f"        [!] 数据库错误: {e}")
            conn.rollback()
        except Exception as e:
            print(f"        [!] 未知错误: {e}")
            conn.rollback()

    print(f"\n--- 目录扫描完成 ---")

if __name__ == "__main__":
    base_dir = os.path.dirname(os.path.abspath(__file__))
    rmb_dir = os.path.join(base_dir, "进出口商品统计表_人民币值", "(12)外商投资企业进出口总值表_人民币值")
    usd_dir = os.path.join(base_dir, "进出口商品统计表_美元值", "(12)外商投资企业进出口总值表_美元值")

    conn = None
    try:
        print("正在连接到Oracle数据库...")
        conn = cx_Oracle.connect("manifest_dcb/manifest_dcb@192.168.1.151/TEST")
        cursor = conn.cursor()
        conn.autocommit = False
        print("数据库连接成功。")

        batch_process_directory(rmb_dir, cursor, conn)
        batch_process_directory(usd_dir, cursor, conn)
        
        conn.commit()
        print("\n数据提交成功。")
        
    except Exception as e:
        print(f"\n[!!!] 批量处理期间发生未知错误: {e}")
        if conn: conn.rollback()
    finally:
        if 'cursor' in locals() and cursor: cursor.close()
        if conn: conn.close()
        print("数据库连接已关闭。") 