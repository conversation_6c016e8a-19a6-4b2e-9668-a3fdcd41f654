# %%
# %%
from socket import timeout
from DrissionPage import Chromium, ChromiumOptions
from DrissionPage import SessionPage
from urllib.parse import urlparse
from datetime import datetime
import json
import time
import requests
import time
import os
import re
# %%
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON><PERSON>ipart

from email.mime.text import MIMEText
from email.mime.image import MIMEImage
from email.mime.multipart import MIME<PERSON><PERSON>ipart
from email.mime.application import MIMEApplication

def run_javascript():
    menu_elements = tab.eles('#side-menu', timeout=1)
    if not menu_elements:
        return ""
    a = tab.eles('#side-menu')[0].run_js(
    '''
    function countMenuItems() {
        const topLevelLis = document.querySelectorAll('.nav.nav-second-level.nav-left.collapse.in > li');
        
        let totalCount = 0;
        let outputLines = [];
        
        Array.from(topLevelLis).forEach(li => {
            const title = li.querySelector('span.nav-label')?.textContent?.trim() 
                        || li.querySelector('a')?.title 
                        || li.textContent?.trim();

            if (['操作手册', '版本说明'].includes(title)) return;
            
            const secondLevelUl = li.querySelector('.nav.nav-third-level');
            
            if (secondLevelUl) {
                secondLevelUl.querySelectorAll(':scope > li').forEach(secondLi => {
                    const secondTitle = secondLi.querySelector('span.nav-label')?.textContent?.trim() 
                                     || secondLi.querySelector('a')?.title 
                                     || secondLi.textContent?.trim();
                    
                    const thirdLevelUl = secondLi.querySelector('.nav.nav-forth-level');
                    
                    if (thirdLevelUl) {
                        thirdLevelUl.querySelectorAll(':scope > li').forEach(thirdLi => {
                            const thirdTitle = thirdLi.querySelector('span.nav-label')?.textContent?.trim() 
                                            || thirdLi.textContent?.trim();
                            outputLines.push(`${title} - ${secondTitle} - ${thirdTitle}`);
                            totalCount++;
                        });
                    } else {
                        outputLines.push(`${title} - ${secondTitle} - `);
                        totalCount++;
                    }
                });
            } else {
                outputLines.push(`${title} -  - `);
                totalCount++;
            }
        });
        
        const output = outputLines.join('\\n');
        console.log(`总计：${totalCount}\\n${output}`);
        return `服务事项数量${totalCount}\\n${output}`;
    }
    return countMenuItems();
    ''')
    return a

# %%


# %%
#tab = Chromium().latest_tab

co = ChromiumOptions().auto_port()

tab = Chromium(addr_or_opts=co).latest_tab


# %%

url = 'https://www.singlewindow.cn'
tab.get(url)


# 改成tab的方式
tab.ele('xpath=//*[@id="app"]/div/div[1]/div[1]/div/div[2]/a[3]').click()
tab.ele('xpath=//*[@id="cardTabBtn"]').click()
b = '88888888'
tab.ele('#password').input(b)
tab.ele('xpath=//*[@id="checkboxIntel"]').click()
tab.ele('xpath=//*[@id="loginbutton"]').click()

# %%
time.sleep(20)



# %%

# %%
tab.get('https://sz.singlewindow.cn/index')


return_data = tab.eles('企业管理')[0].parent().run_js(
''' 
// 获取所有模块分组
let asmains = document.querySelectorAll('.asmain');

// 初始化数据存储对象
let moduleData = {};

for (let main of asmains) {
  // 获取当前分组的模块列表和对应的应用块
  let ul = main.querySelector('.asul');
  let liList = ul.querySelectorAll('li.yx');
  let asdiv = main.querySelector('.asdiv');
  let implList = asdiv.querySelectorAll('.impl');

  // 遍历每个模块
  for (let i = 0; i < liList.length; i++) {
    let moduleLi = liList[i];
    let moduleImpl = implList[i]; // 注意可能会越界，假设结构一致

    // 获取模块名称
    let moduleName = moduleLi.innerText.trim();

    // 获取子应用列表
    let nrmcList = moduleImpl.querySelectorAll('div.nrmc');
    let aList = moduleImpl.querySelectorAll('a'); // 获取所有的超链接
    let count = nrmcList.length;
    let apps = [];
    let appNames = []; // 新增：存储应用名称列表

    // 遍历子应用，提取名称和超链接
    for (let j = 0; j < nrmcList.length; j++) {
      let appName = nrmcList[j].innerText.trim();
      let dataHref = aList[j].getAttribute('data-href'); // 提取 data-href 属性
      apps.push({
        name: appName,
        link: dataHref
      });
      appNames.push(appName); // 新增：收集应用名称
    }

    // 存储到数据对象中
    // 修改格式：模块名称（数量）[应用1,应用2,...]
    moduleData[`${moduleName}（${count}）[${appNames.join(',')}]`] = apps;
  }
}

// 如果需要将数据输出为 JSON 格式，可以使用以下代码：
console.log(JSON.stringify(moduleData, null, 2));

  return JSON.stringify(moduleData, null, 2);
''')


# %%


# %%
python_obj = json.loads(return_data)

# %%

all_nums = 0
for i in python_obj:
  #print(i)
  len(python_obj[i])
  all_nums += len(python_obj[i])


# %%

len(python_obj)


python_obj = python_obj 
# 方法一：直接遍历字典的键

nums = 0

for key in python_obj:
    for value in python_obj[key]:

        if value['link'] == 'https://swapp.singlewindow.cn/tswpwebserver/tswp/view/homepage':
            print('特殊处理','特殊物品出入境卫生检疫审批')
            tab.get(value['link'],timeout=60)
            # 特殊处理
            # 特殊处理逻辑
            total_count = 0
            all_lines = []

            # 获取carder元素数量
            carder_count = len(tab.eles('.carder pointer'))
            print(f'找到 {carder_count} 个carder元素')

            # 逐个处理每个carder元素
            for i in range(carder_count):
                try:
                    # 获取当前carder元素
                    carder = tab.eles('.carder pointer')[i]
                    print(f'正在处理第 {i+1} 个carder')
                    
                    # 点击进入子页面
                    carder.click()
                    tab.wait.doc_loaded(30)
                    time.sleep(5)  # 等待页面加载
                    
                    # 运行JavaScript统计
                    result = run_javascript()
                    print('特殊页面result:', result)
                    
                    if result:
                        # 解析服务事项数量和三级目录数据
                        lines = result.split("\n")
                        if "服务事项数量" in lines[0]:
                            count_part = lines[0].split("服务事项数量")[1].strip()
                            total_count += int(count_part)
                        if len(lines) > 1:
                            all_lines.extend(lines[1:])
                    
                    # 返回上一页
                    tab.back()
                    time.sleep(1)
                except Exception as e:
                    print(f'处理第 {i+1} 个carder时出错:', e)
                    continue

            # 合并结果
            if total_count > 0 or all_lines:
                a = f"服务事项数量{total_count}\n" + "\n".join(all_lines)
            else:
                a = ""

            value['result'] = a
            print(value['result'])
            continue



        # if value['link'] == 'http://origin.customs.gov.cn/' or value['link'] == 'http://apq.customs.gov.cn/':
        #     continue
        print('name:',value['name'],'url',value['link'])

        tab.get(value['link'],timeout=60)
        # 判断  有没有权限进入
        new_url = tab.url.replace('https','http')

        # https://swapp.singlewindow.cn/deskserver/sw/deskIndex?menu_id=qsp


        # if new_url == str(value['link']) or tab.url == str(value['link']):
        print('# 可以执行  js   name:',value['name'],'url',value['link'])
        time.sleep(1)
        a = run_javascript()
        time.sleep(1)
        print('a:',a)
        if not a:  # 如果返回空值
            
            url_div = tab.eles('.urlDiv',timeout=1)
            if url_div:
                # 使用更可靠的定位方式查找链接元素
                links = url_div[0].eles('tag:a',timeout=1)  # 获取所有a标签
                links_2 = url_div[0].eles('tag:li',timeout=1)  # 获取所有li标签

                total_count = 0
                all_lines = []
                all_urls = []
                for link in links:
                    onclick = link.attr('onclick')
                    if onclick and 'window.open' in onclick:
                        # 提取URL部分
                        url_start = onclick.find('window.location.origin +') + 23
                        url_end = onclick.find(')', url_start)
                        url = onclick[url_start:url_end].strip("'\" +")
                        
                        # 补全URL
                        if not url.startswith('http'):
                            base_url = tab.url.split('//')[0] + '//' + tab.url.split('//')[1].split('/')[0]
                            url = base_url + '/' + url.lstrip('/')
                        all_urls.append(url)
                        
                for link in links_2:
                    onclick = link.attr('onclick')
                    if onclick and 'window.open' in onclick:
                        # 提取URL部分
                        url_start = onclick.find('window.location.origin +') + 23
                        url_end = onclick.find(')', url_start)
                        url = onclick[url_start:url_end].strip("'\" +")
                        
                        # 补全URL
                        if not url.startswith('http'):
                            base_url = tab.url.split('//')[0] + '//' + tab.url.split('//')[1].split('/')[0]
                            url = base_url + '/' + url.lstrip('/')
                        all_urls.append(url)
                for url in all_urls:
                    # 打开新的标签页并加载URL
                    tab.get(url)
                    time.sleep(1)
                    # 运行JavaScript并获取结果
                    result = run_javascript()
                    print('内部result:',result)
                    if result:
                        # 解析服务事项数量和三级目录数据
                        lines = result.split("\n")
                        if "服务事项数量" in lines[0]:
                            count_part = lines[0].split("服务事项数量")[1].strip()
                            total_count += int(count_part)
                        if len(lines) > 1:
                            all_lines.extend(lines[1:])
                
                # 合并结果
                if total_count > 0 or all_lines:
                    a = f"服务事项数量{total_count}\n" + "\n".join(all_lines)
        value['result'] = a
        print(value['result'])
        
tab.close()






# %%


# 生成HTML表格内容

# 新增：读取上次保存的数据
last_data_file = "last_data.json"
last_python_obj = {}
if os.path.exists(last_data_file):
    with open(last_data_file, 'r', encoding='utf-8') as f:
        last_python_obj = json.load(f)
# ... existing code ...
# 新增统计变量
added_count = 0
removed_count = 0
changed_count = 0
# 新增统计变量
added_domain_count = 0
removed_domain_count = 0




# 生成HTML表格内容
output_content = f"<h2>应用总数：{all_nums}</h2>"

# 统计新增和减少的应用
for key in python_obj:
    if key in last_python_obj:
        # 检查新增的应用
        current_names = {v['name'] for v in python_obj[key]}
        last_names = {v['name'] for v in last_python_obj[key]}
        added = current_names - last_names
        removed = last_names - current_names
        added_count += len(added)
        removed_count += len(removed)
        
        # 检查服务事项数量变化
        for value in python_obj[key]:
            if value['name'] in last_names:
                last_value = next((v for v in last_python_obj[key] if v['name'] == value['name']), None)
                if last_value and value.get('result') != last_value.get('result'):
                    changed_count += 1
    else:
        added_count += len(python_obj[key])


# 统计新增和减少的业务领域
current_domains = set(python_obj.keys())
last_domains = set(last_python_obj.keys()) if last_python_obj else set()

added_domains = current_domains - last_domains
removed_domains = last_domains - current_domains
added_domain_count = len(added_domains)
removed_domain_count = len(removed_domains)



# 添加统计摘要
# 修正后的总行数统计
total_rows = 1
for key in python_obj:
    for value in python_obj[key]:
        result = value.get("result", "")
        if result and "服务事项数量" in result:
            lines = result.split("\n")
            # 只计算实际的三级目录行数
            if len(lines) > 1:
                # 减去第一行"服务事项数量"，并确保至少计为1行
                total_rows += 1
        else:
            # 如果没有结果数据，计为1行
            total_rows += 1

# 收集详细的变更信息
added_apps_details = []
removed_apps_details = []
changed_items_details = []

# 收集业务领域变化详情
for domain in added_domains:
    clean_domain = domain.split('[')[0] if '[' in domain else domain
    added_apps_details.append(f"<li><strong>新增领域</strong>：{clean_domain}</li>")

for domain in removed_domains:
    clean_domain = domain.split('[')[0] if '[' in domain else domain
    removed_apps_details.append(f"<li><strong>移除领域</strong>：{clean_domain}</li>")

# 收集应用变化详情
for key in python_obj:
    clean_key = key.split('[')[0] if '[' in key else key
    
    # 添加新增应用详情
    if key in last_python_obj:
        current_apps = {v['name']: v for v in python_obj[key]}
        last_apps = {v['name']: v for v in last_python_obj[key]}
        
        # 新增应用
        for app_name in set(current_apps.keys()) - set(last_apps.keys()):
            added_apps_details.append(f"<li><strong>新增应用</strong>：{clean_key} - {app_name}</li>")
        
        # 减少应用
        for app_name in set(last_apps.keys()) - set(current_apps.keys()):
            removed_apps_details.append(f"<li><strong>移除应用</strong>：{clean_key} - {app_name}</li>")
        
        # 服务事项变化
        for app_name, app in current_apps.items():
            if app_name in last_apps:
                result = app.get('result', '')
                last_result = last_apps[app_name].get('result', '')
                
                if result and last_result and result != last_result:
                    current_lines = result.split("\n")[1:] if "服务事项数量" in result else []
                    last_lines = last_result.split("\n")[1:] if "服务事项数量" in last_result else []
                    
                    current_items = set(current_lines)
                    last_items = set(last_lines)
                    
                    # 新增服务事项
                    for item in current_items - last_items:
                        if item.strip():
                            parts = [p.strip() for p in item.split("-")]
                            primary = parts[0] if len(parts) > 0 else ""
                            secondary = parts[1] if len(parts) > 1 else ""
                            tertiary = parts[2] if len(parts) > 2 else ""
                            
                            changed_items_details.append(
                                f"<li><strong>新增服务事项</strong>：{clean_key} - {app_name} - "
                                f"{primary} - {secondary} - {tertiary}</li>"
                            )
                    
                    # 移除服务事项
                    for item in last_items - current_items:
                        if item.strip():
                            parts = [p.strip() for p in item.split("-")]
                            primary = parts[0] if len(parts) > 0 else ""
                            secondary = parts[1] if len(parts) > 1 else ""
                            tertiary = parts[2] if len(parts) > 2 else ""
                            
                            changed_items_details.append(
                                f"<li><strong>移除服务事项</strong>：{clean_key} - {app_name} - "
                                f"{primary} - {secondary} - {tertiary}</li>"
                            )
    else:
        # 完全新增的业务领域下的所有应用
        for app in python_obj[key]:
            added_apps_details.append(f"<li><strong>新增应用</strong>：{clean_key} - {app['name']}</li>")

# 新增：预计算总功能数
def calculate_total_functions(python_obj):
    output_content=""
    serial_number = 0

    for key in python_obj:
        clean_key = key.split('[')[0] if '[' in key else key
        
        # 添加业务领域状态
        domain_status = ""
        if key in added_domains:
            domain_status = "<span style='color:green'>新增业务领域</span>"
        elif key in removed_domains:
            domain_status = "<span style='color:red'>已移除业务领域</span>"

            
        for value in python_obj[key]:
            result = value.get("result", "")
            print(result)
            service_count = ""
            
            if result:
                try:
                    # 获取服务事项数量
                    if "服务事项数量" in result:
                        lines = result.split("\n")
                        service_count = lines[0].split("服务事项数量")[1].strip()
                    
                    # 获取状态
                    status = ""
                    if key in last_python_obj:
                        last_values = [v['name'] for v in last_python_obj[key]]
                        if value['name'] not in last_values:
                            status = "<span style='color:green'>新增应用</span>"
                        else:
                            # 检查服务事项变化
                            last_value = next((v for v in last_python_obj[key] if v['name'] == value['name']), None)
                            if last_value and last_value.get('result'):
                                last_lines = last_value['result'].split("\n")
                                if len(lines) > 1 and len(last_lines) > 1:
                                    # 比较三级目录变化
                                    current_items = set(lines[1:])
                                    last_items = set(last_lines[1:])
                                    added_items = current_items - last_items
                                    removed_items = last_items - current_items
                                    
                                    if added_items or removed_items:
                                        status = ""
                                        if added_items:
                                            added_names = [item.split('-')[0].strip() for item in added_items if '-' in item]
                                            status += f"<span style='color:green'>新增:{len(added_items)}项({', '.join(added_names)})</span> "
                                        if removed_items:
                                            removed_names = [item.split('-')[0].strip() for item in removed_items if '-' in item]
                                            status += f"<span style='color:red'>减少:{len(removed_items)}项({', '.join(removed_names)})</span>"
                    final_status = f"{domain_status} {status}".strip()

                    # 处理三级目录数据
                    if len(lines) > 1:
                        for line in lines[1:]:
                            if line.strip():
                                parts = [p.strip() for p in line.split("-")]
                                primary = parts[0] if len(parts) > 0 else ""
                                secondary = parts[1] if len(parts) > 1 else ""
                                tertiary = parts[2] if len(parts) > 2 else ""
                                
                                output_content += f"<tr>"
                                output_content += f"<td>{serial_number}</td>"
                                output_content += f"<td>{clean_key}</td>"
                                output_content += f"<td>{value['name']}</td>"
                                output_content += f"<td>{service_count}</td>"
                                output_content += f"<td>{primary}</td>"
                                output_content += f"<td>{secondary}</td>"
                                output_content += f"<td>{tertiary}</td>"
                                output_content += f"<td>{final_status}</td>"
                                output_content += "</tr>"
                                serial_number += 1
                        continue
                        
                except Exception as e:
                    print(f"解析出错: {e}")
                    service_count = ""

            # 如果没有三级目录数据
            output_content += f"<tr>"
            output_content += f"<td>{serial_number}</td>"
            output_content += f"<td>{clean_key}</td>"
            output_content += f"<td>{value['name']}</td>"
            output_content += f"<td>{service_count}</td>"
            output_content += f"<td></td>"
            output_content += f"<td></td>"
            output_content += f"<td></td>"
            output_content += f"<td></td>"
            output_content += "</tr>"
            serial_number += 1


    return serial_number
total_functions = calculate_total_functions(python_obj)

output_content += f"""
<h3>变化统计：</h3>
<ul>
    <li>总功能数：{total_functions}个</li>
    <li>新增业务领域：{added_domain_count}个 {', '.join(added_domains) if added_domains else ''}</li>
    <li>减少业务领域：{removed_domain_count}个 {', '.join(removed_domains) if removed_domains else ''}</li>
    <li>新增应用：{added_count}个</li>
    <li>减少应用：{removed_count}个</li>
    <li>变更服务事项：{changed_count}个</li>
</ul>

<h3>详细变化：</h3>
<div style="max-height: 300px; overflow-y: auto; border: 1px solid #ccc; padding: 10px; margin-bottom: 20px;">
"""

# 添加详细变化列表
if added_apps_details:
    output_content += "<h4>新增业务领域和应用：</h4><ul>"
    output_content += "".join(added_apps_details)
    output_content += "</ul>"

if removed_apps_details:
    output_content += "<h4>移除业务领域和应用：</h4><ul>"
    output_content += "".join(removed_apps_details)
    output_content += "</ul>"

if changed_items_details:
    output_content += "<h4>服务事项变化：</h4><ul>"
    output_content += "".join(changed_items_details)
    output_content += "</ul>"

output_content += "</div>"

output_content += "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>"
output_content += "<tr><th>序号</th><th>业务领域</th><th>业务系统</th><th>服务事项数量</th><th>一级内容</th><th>二级内容</th><th>三级内容</th><th>状态</th></tr>"

serial_number = 1

for key in python_obj:
    first_row = True
    clean_key = key.split('[')[0] if '[' in key else key
    
    # 添加业务领域状态
    domain_status = ""
    if key in added_domains:
        domain_status = "<span style='color:green'>新增业务领域</span>"
    elif key in removed_domains:
        domain_status = "<span style='color:red'>已移除业务领域</span>"

        
    for value in python_obj[key]:
        result = value.get("result", "")
        print(result)
        service_count = ""
        
        if result:
            try:
                # 获取服务事项数量
                if "服务事项数量" in result:
                    lines = result.split("\n")
                    service_count = lines[0].split("服务事项数量")[1].strip()
                
                # 获取状态
                status = ""
                if key in last_python_obj:
                    last_values = [v['name'] for v in last_python_obj[key]]
                    if value['name'] not in last_values:
                        status = "<span style='color:green'>新增应用</span>"
                    else:
                        # 检查服务事项变化
                        last_value = next((v for v in last_python_obj[key] if v['name'] == value['name']), None)
                        if last_value and last_value.get('result'):
                            last_lines = last_value['result'].split("\n")
                            if len(lines) > 1 and len(last_lines) > 1:
                                # 比较三级目录变化
                                current_items = set(lines[1:])
                                last_items = set(last_lines[1:])
                                added_items = current_items - last_items
                                removed_items = last_items - current_items
                                
                                if added_items or removed_items:
                                    status = ""
                                    if added_items:
                                        added_names = [item.split('-')[0].strip() for item in added_items if '-' in item]
                                        status += f"<span style='color:green'>新增:{len(added_items)}项({', '.join(added_names)})</span> "
                                    if removed_items:
                                        removed_names = [item.split('-')[0].strip() for item in removed_items if '-' in item]
                                        status += f"<span style='color:red'>减少:{len(removed_items)}项({', '.join(removed_names)})</span>"
                final_status = f"{domain_status} {status}".strip()
                # 现在要修改final_status 我希望输出 备注里面出现的新增或者减少的具体内容


                # 处理三级目录数据
                if len(lines) > 1:
                    for line in lines[1:]:
                        if line.strip():
                            parts = [p.strip() for p in line.split("-")]
                            primary = parts[0] if len(parts) > 0 else ""
                            secondary = parts[1] if len(parts) > 1 else ""
                            tertiary = parts[2] if len(parts) > 2 else ""
                            
                            output_content += f"<tr>"
                            output_content += f"<td>{serial_number}</td>"
                            output_content += f"<td>{clean_key}</td>"
                            output_content += f"<td>{value['name']}</td>"
                            output_content += f"<td>{service_count}</td>"
                            output_content += f"<td>{primary}</td>"
                            output_content += f"<td>{secondary}</td>"
                            output_content += f"<td>{tertiary}</td>"
                            output_content += f"<td>{final_status}</td>"
                            output_content += "</tr>"
                            serial_number += 1
                    continue
                    
            except Exception as e:
                print(f"解析出错: {e}")
                service_count = ""

        # 如果没有三级目录数据
        output_content += f"<tr>"
        output_content += f"<td>{serial_number}</td>"
        output_content += f"<td>{clean_key}</td>"
        output_content += f"<td>{value['name']}</td>"
        output_content += f"<td>{service_count}</td>"
        output_content += f"<td></td>"
        output_content += f"<td></td>"
        output_content += f"<td></td>"
        output_content += f"<td></td>"
        output_content += "</tr>"
        serial_number += 1

output_content += "</table>"
# ... existing code ...


# 保存当前数据
with open(last_data_file, 'w', encoding='utf-8') as f:
    json.dump(python_obj, f, ensure_ascii=False, indent=2)




# 邮件配置
sender_email = "<EMAIL>"
sender_password = "27jRSsHfiMFx6iT7"
# receiver_email = ['<EMAIL>']  # 主收件人
receiver_email = ['<EMAIL>']  # 主收件人

cc = ['<EMAIL>', '<EMAIL>']  # 抄送人

# 在邮件发送前添加变化检查
has_changes = (added_domain_count > 0 or removed_domain_count > 0 or 
               added_count > 0 or removed_count > 0 or changed_count > 0)

if has_changes:
    # 创建邮件对象
    message = MIMEMultipart()
    message["From"] = sender_email
    message["To"] = ', '.join(receiver_email)
    message["Subject"] = "标准版单一窗口  应用个数"
    message["Cc"] = ', '.join(cc)
    
    # 添加邮件正文
    message.attach(MIMEText(output_content, "html"))
    
    # 连接到 SMTP 服务器并发送邮件
    try:
        server = smtplib.SMTP("smtp.exmail.qq.com")
        server.starttls()
        server.login(sender_email, sender_password)
        text = message.as_string()
        server.sendmail(sender_email, receiver_email + cc, text)
        server.quit()
        print("邮件发送成功！")
    except Exception as e:
        print(f"邮件发送失败：{e}")
else:
    print("没有检测到变化，不发送邮件")


# 创建邮件对象
message = MIMEMultipart()
message["From"] = sender_email
message["To"] = ', '.join(receiver_email)
message["Subject"] = "标准版单一窗口  应用个数"
message["Cc"] = ', '.join(cc)

# 添加邮件正文
message.attach(MIMEText(output_content, "html"))

# 连接到 SMTP 服务器并发送邮件

server = smtplib.SMTP("smtp.exmail.qq.com")
server.starttls()
server.login(sender_email, sender_password)
text = message.as_string()
server.sendmail(sender_email, receiver_email + cc, text)
server.quit()
print("邮件发送成功！")