import pandas as pd
import os
import re
import cx_Oracle
import numpy as np

def convert_to_float_or_none(value):
    """清理数据，将'-'或空值转为None，其余转为浮点数"""
    if pd.isna(value) or str(value).strip() == '-':
        return None
    try:
        # 处理可能存在的千分位逗号
        value_str = str(value).replace(',', '')
        return float(value_str)
    except (ValueError, TypeError):
        return None

def parse_table_5(file_path):
    """
    一个更健壮的、专门为(5)号表格的复杂结构编写的解析器。
    它能够处理“双层表头”、“隔行数据”以及“表格缩进”。
    """
    try:
        # 强制读取第一个sheet，确保df_raw是一个DataFrame，而不是包含多个sheet的字典
        df_raw = pd.read_excel(file_path, header=None, sheet_name=0)
        
        # 1. 动态查找单位
        unit = "不明"
        for i in range(min(5, len(df_raw))):
            row_text = ' '.join(str(s) for s in df_raw.iloc[i].tolist() if pd.notna(s))
            unit_match = re.search(r'单位[：:]\s*([^\s]+)', row_text)
            if unit_match:
                unit = unit_match.group(1).strip()
                break
        
        # 1. 定位表头和数据区域
        header_start_row = -1
        trade_mode_col_idx = -1
        for i, row in df_raw.iterrows():
            # 使用 .any() 来进行可靠的判断，避免歧义
            if row.astype(str).str.contains('贸易方式').any():
                header_start_row = i
                # 找到行之后，再遍历一次该行以确定精确的列索引
                for j, cell_content in enumerate(row):
                    if '贸易方式' in str(cell_content):
                        trade_mode_col_idx = j
                        break
                break
        
        if header_start_row == -1:
            print(f"    [!] 错误: 未能在文件中找到'贸易方式'表头。")
            return None, None

        # 2. 基于侦测到的位置，精确提取和构建列名(放弃ffill，改用更精确的逻辑)
        header1_raw = df_raw.iloc[header_start_row].tolist()
        header2 = df_raw.iloc[header_start_row + 1].tolist()

        columns = ['贸易方式']
        current_header1 = ''
        # 从“贸易方式”列之后，取6列
        for i in range(trade_mode_col_idx + 1, trade_mode_col_idx + 7):
            # 如果当前一级表头单元格有值，则更新它
            if pd.notna(header1_raw[i]) and str(header1_raw[i]).strip():
                current_header1 = str(header1_raw[i]).strip()
            
            # 使用更新后的一级表头
            col1_raw = current_header1
            if '进出口' in col1_raw: col1_cleaned = 'IE'
            elif '出口' in col1_raw: col1_cleaned = 'EXP'
            elif '进口' in col1_raw: col1_cleaned = 'IMP'
            else: col1_cleaned = ''
            
            col2 = str(header2[i]).strip()
            col2_cleaned = "YTD" if "1至" in col2 else "MONTH"
            columns.append(f"{col1_cleaned}_{col2_cleaned}")

        # 诊断日志可以保留，也可以移除
        # print(f"        [DIAG] Generated columns: {columns}")

        # 3. 动态定位数据起始行 (寻找"总值")
        data_start_row = -1
        for i in range(header_start_row + 2, len(df_raw)):
            cell_value = df_raw.iloc[i, trade_mode_col_idx]
            if '总值' in str(cell_value):
                data_start_row = i
                break
        
        if data_start_row == -1:
            print(f"    [!] 错误: 在文件 '{os.path.basename(file_path)}' 的'贸易方式'表头后未找到'总值'数据行。")
            return None, None

        # 4. 从已加载的DataFrame中精确切片数据区域，不再重新读取文件
        end_col = trade_mode_col_idx + 7
        df_sliced = df_raw.iloc[data_start_row:, trade_mode_col_idx:end_col].copy()
        # print(f"        [DIAG] Sliced data shape: {df_sliced.shape}")
        df_data = df_sliced
        df_data.columns = columns
        df_data.reset_index(drop=True, inplace=True)

        # 5. 合并隔行数据
        processed_data = []
        for i in range(0, len(df_data), 2):
            row_amount = df_data.iloc[i]
            if i + 1 >= len(df_data): continue
            row_yoy = df_data.iloc[i+1]
            try:
                # 核心逻辑修正：优先从金额行(amount)获取贸易方式，因为'总值'等总是在第一行
                trade_mode = row_amount.iloc[0] # 使用.iloc[0]按位置提取
                
                # 如果金额行是空的（可能是某些子类的缩进格式导致），则尝试从同比行(yoy)获取
                if pd.isna(trade_mode):
                    trade_mode = row_yoy.iloc[0] # 使用.iloc[0]按位置提取
                
                # 如果两种方式都获取不到，或者碰到了表格末尾的注释行，则跳过
                if pd.isna(trade_mode) or '注：' in str(trade_mode):
                    continue

                # 按位置提取数据，不再依赖列名，避免因列名重复导致的问题
                processed_data.append({
                    'TRADE_MODE': trade_mode,
                    'MONTH_IE_AMOUNT': convert_to_float_or_none(row_amount.iloc[1]),
                    'MONTH_IE_YOY': convert_to_float_or_none(row_yoy.iloc[1]),
                    'YTD_IE_AMOUNT': convert_to_float_or_none(row_amount.iloc[2]),
                    'YTD_IE_YOY': convert_to_float_or_none(row_yoy.iloc[2]),
                    'MONTH_EXP_AMOUNT': convert_to_float_or_none(row_amount.iloc[3]),
                    'MONTH_EXP_YOY': convert_to_float_or_none(row_yoy.iloc[3]),
                    'YTD_EXP_AMOUNT': convert_to_float_or_none(row_amount.iloc[4]),
                    'YTD_EXP_YOY': convert_to_float_or_none(row_yoy.iloc[4]),
                    'MONTH_IMP_AMOUNT': convert_to_float_or_none(row_amount.iloc[5]),
                    'MONTH_IMP_YOY': convert_to_float_or_none(row_yoy.iloc[5]),
                    'YTD_IMP_AMOUNT': convert_to_float_or_none(row_amount.iloc[6]),
                    'YTD_IMP_YOY': convert_to_float_or_none(row_yoy.iloc[6]),
                })
            except Exception as loop_e:
                print(f"    [!!!] 在文件 '{os.path.basename(file_path)}' 的第 {i} 数据行附近发生内部循环错误: {loop_e}")
                print(f"        金额行内容: {row_amount.to_dict()}")
                print(f"        同比行内容: {row_yoy.to_dict()}")
                # 重新引发异常以停止处理此文件，避免后续错误
                raise
        return pd.DataFrame(processed_data), unit
    except Exception as e:
        print(f"    [!] 解析文件 '{os.path.basename(file_path)}' 时出错: {e}")
        return None, None

def check_if_data_exists(filename, cursor):
    """根据文件名检查数据是否已存在 (以'总值'为标记)"""
    year_month_match = re.search(r'(\d{4})年(\d{1,2})月', filename)
    if not year_month_match: return False
    year, month = year_month_match.groups()
    current_month = f"{year}{month.zfill(2)}01"
    currency_type = "人民币" if "人民币" in filename else "美元"
    
    query = "SELECT COUNT(*) FROM temp_cus_mon_5 WHERE CURRENT_MONTH = TO_DATE(:1, 'YYYYMMDD') AND CURRENCY_TYPE = :2 AND TRADE_MODE = '总值'"
    cursor.execute(query, (current_month, currency_type))
    return cursor.fetchone()[0] > 0

def batch_process_directory(directory_path, cursor):
    """遍历目录，对新文件执行增量入库"""
    print(f"\n--- 开始扫描目录: {directory_path} ---")
    if not os.path.isdir(directory_path):
        print(f"    [!] 错误: 目录不存在")
        return

    for filename in sorted(os.listdir(directory_path)):
        if not filename.endswith(".xlsx"): continue
        
        if check_if_data_exists(filename, cursor):
            # print(f"    数据已存在, 跳过: {filename}")
            continue

        file_path = os.path.join(directory_path, filename)
        print(f"    [*] 发现新文件, 正在处理: {filename}")
        
        df, unit_from_file = parse_table_5(file_path)
        
        if df is not None and not df.empty:
            year_month_match = re.search(r'(\d{4})年(\d{1,2})月', filename)
            year, month = year_month_match.groups()
            df['CURRENT_MONTH'] = f"{year}{month.zfill(2)}01"
            currency_type = "人民币" if "人民币" in filename else "美元"
            
            # 优先使用动态解析的单位，如果失败则使用默认值
            if unit_from_file and unit_from_file != "不明":
                df['UNIT'] = unit_from_file
            else:
                df['UNIT'] = "万元" if currency_type == "人民币" else "千美元"
            
            # 使用更强大的numpy方法将NaN替换为None，以解决"invalid number"问题
            df = df.replace({np.nan: None})
            
            data_to_insert = [tuple(row) for row in df.to_records(index=False)]
            
            # 在插入前打印第一行数据，用于最终诊断
            if data_to_insert:
                print(f"        [DIAG] 准备插入的第一行数据: {data_to_insert[0]}")

            insert_query = """
            INSERT INTO temp_cus_mon_5 (
                TRADE_MODE, MONTH_IE_AMOUNT, MONTH_IE_YOY, YTD_IE_AMOUNT, YTD_IE_YOY,
                MONTH_EXP_AMOUNT, MONTH_EXP_YOY, YTD_EXP_AMOUNT, YTD_EXP_YOY,
                MONTH_IMP_AMOUNT, MONTH_IMP_YOY, YTD_IMP_AMOUNT, YTD_IMP_YOY,
                CURRENT_MONTH, CURRENCY_TYPE, UNIT
            ) VALUES (
                :1, :2, :3, :4, :5, :6, :7, :8, :9, :10, :11, :12, :13, 
                TO_DATE(:14, 'YYYYMMDD'), :15, :16
            )
            """
            try:
                cursor.executemany(insert_query, data_to_insert)
                print(f"        -> 成功插入 {cursor.rowcount} 条记录.")
            except cx_Oracle.Error as e:
                print(f"        [!] 数据库插入错误: {e}")
    print(f"--- 目录扫描完成 ---")

if __name__ == "__main__":
    base_dir = os.getcwd()
    rmb_directory = os.path.join(base_dir, r"进出口商品统计表_人民币值\(5)进出口商品贸易方式总值表_人民币值")
    usd_directory = os.path.join(base_dir, r"进出口商品统计表_美元值\(5)进出口商品贸易方式总值表_美元值")
    
    conn = None
    try:
        print("正在连接到Oracle数据库...")
        conn = cx_Oracle.connect("manifest_dcb/manifest_dcb@192.168.1.151/TEST")
        cursor = conn.cursor()
        print("数据库连接成功。")

        batch_process_directory(rmb_directory, cursor)
        batch_process_directory(usd_directory, cursor)
        
        conn.commit()
        print("\n数据提交成功。")
        
    except cx_Oracle.Error as error:
        print(f"\n[!!!] 数据库操作期间发生严重错误: {error}")
    except Exception as e:
        print(f"\n[!!!] 批量处理期间发生未知错误: {e}")
    finally:
        if 'cursor' in locals() and cursor: cursor.close()
        if conn:
            conn.close()
            print("数据库连接已关闭。") 