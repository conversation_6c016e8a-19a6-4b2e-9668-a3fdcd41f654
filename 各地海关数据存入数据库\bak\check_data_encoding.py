import pandas as pd

# 尝试不同的编码方式读取CSV文件
encodings = ['utf-8-sig', 'utf-8', 'gbk', 'gb2312']

for encoding in encodings:
    try:
        print(f"尝试编码: {encoding}")
        df = pd.read_csv('T_STATISTICAL_CUS_TOTAL_BEIJING_updated.csv', encoding=encoding)
        print("读取成功!")
        print("前5行数据:")
        print(df.head())
        
        # 检查是否有企业性质数据
        enterprise_data = df[df['STAT_NAME'].str.contains('企业性质', na=False)]
        print(f"\n企业性质数据条数: {len(enterprise_data)}")
        
        # 检查是否有国别地区数据
        country_data = df[df['STAT_NAME'].str.contains('国别地区', na=False)]
        print(f"国别地区数据条数: {len(country_data)}")
        
        # 检查所有不同的STAT_NAME值
        print("\n所有不同的STAT_NAME值:")
        print(df['STAT_NAME'].unique())
        break
    except Exception as e:
        print(f"使用 {encoding} 编码失败: {e}")
        continue