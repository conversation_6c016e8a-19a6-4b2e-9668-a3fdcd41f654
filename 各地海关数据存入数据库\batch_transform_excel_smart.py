import pandas as pd
import numpy as np
import datetime
import os
import re
from pathlib import Path

def detect_unit_from_data(df, amount_columns):
    """
    根据数据数值范围智能推断单位
    """
    # 收集所有金额字段的数值
    all_values = []
    for col in amount_columns:
        if col in df.columns:
            values = pd.to_numeric(df[col], errors='coerce').dropna()
            all_values.extend(values.tolist())
    
    if not all_values:
        return 'unknown'
    
    # 计算统计信息
    median_value = np.median(all_values)
    max_value = np.max(all_values)
    min_value = np.min(all_values)
    
    print(f"  数值分析: 中位数={median_value:.2f}, 最大值={max_value:.2f}, 最小值={min_value:.2f}")
    
    # 根据数值范围推断单位
    if median_value > 100000000:  # 大于1亿，推断为元
        return 'yuan'
    elif median_value > 10000:   # 大于1万，推断为万元
        return 'wan_yuan'
    elif median_value > 100:      # 大于100，推断为亿元
        return 'yi_yuan'
    else:
        return 'unknown'

def detect_unit_from_markers(df):
    """
    从工作表中检测单位标记
    """
    # 检查列名中的单位标记
    for col in df.columns:
        col_str = str(col).lower()
        if '单位' in col_str or 'unit' in col_str:
            if '亿元' in col_str:
                return 'yi_yuan'
            elif '万元' in col_str:
                return 'wan_yuan'
            elif '元' in col_str:
                return 'yuan'
    
    # 检查数据行中的单位标记
    for idx, row in df.iterrows():
        for val in row:
            if pd.notna(val) and isinstance(val, str):
                val_lower = val.lower()
                if '单位' in val_lower or 'unit' in val_lower:
                    if '亿元' in val_lower:
                        return 'yi_yuan'
                    elif '万元' in val_lower:
                        return 'wan_yuan'
                    elif '元' in val_lower:
                        return 'yuan'
    
    return 'unknown'

def get_conversion_factor(unit):
    """
    获取单位转换系数（转换为亿元）
    """
    conversion_map = {
        'yuan': 100000000,    # 元 -> 亿元
        'wan_yuan': 10000,    # 万元 -> 亿元
        'yi_yuan': 1,         # 亿元 -> 亿元
        'unknown': 10000      # 默认按万元处理
    }
    return conversion_map.get(unit, 10000)

def clean_excel_data(df, sheet_name):
    """
    清洗Excel数据，处理特殊字符、空值、数据类型等问题
    """
    # 删除全为空值的列
    df = df.dropna(axis=1, how='all')
    
    # 清理列名：去除特殊字符和空格
    df.columns = df.columns.astype(str).str.strip().str.replace('\xa0', ' ').str.replace(r'Unnamed: \d+', '', regex=True)
    
    # 查找项目列
    project_col = None
    for col in df.columns:
        if '项目' in col or col.strip() == '':
            project_col = col
            break
    
    if project_col is None:
        # 如果没有找到项目列，使用第一列
        project_col = df.columns[0]
    
    # 过滤数据
    clean_df = df[df[project_col].notna()]
    clean_df = clean_df[~clean_df[project_col].astype(str).str.contains('单位|合计|总计|nan', case=False, na=False)]
    clean_df = clean_df[clean_df[project_col].astype(str).str.strip() != '']
    
    # 清理数据中的特殊字符
    for col in clean_df.columns:
        if clean_df[col].dtype == 'object':
            clean_df[col] = clean_df[col].astype(str).str.replace('*', '').str.strip()
    
    return clean_df, project_col

def extract_date_from_filename(filename):
    """
    从文件名中提取日期信息
    """
    # 匹配年份和月份
    patterns = [
        r'(\d{4})年(\d{1,2})月',
        r'(\d{4})年1-(\d{1,2})月',
    ]
    
    for pattern in patterns:
        match = re.search(pattern, filename)
        if match:
            year = match.group(1)
            month = match.group(2)
            
            # 如果是1-X月的格式，使用X作为月份
            if '1-' in filename:
                return f"{year}/{month.zfill(2)}/1"
            else:
                return f"{year}/{month.zfill(2)}/1"
    
    # 默认返回当前日期
    return datetime.datetime.now().strftime('%Y/%m/1')

def transform_single_excel(input_excel_path, output_csv_path):
    """
    转换单个Excel文件为CSV
    """
    try:
        xls = pd.ExcelFile(input_excel_path, engine='openpyxl')
    except FileNotFoundError:
        print(f"错误：找不到文件 {input_excel_path}")
        return None
    except Exception as e:
        print(f"读取Excel文件时出错 {input_excel_path}: {e}")
        return None

    output_dfs = []
    
    # 目标CSV文件的列名（完全匹配Oracle表结构）
    output_columns = [
        "STAT_DATE", "STAT_TYPE", "STAT_NAME", "STAT_CODE", "STAT_CONTENT_RAW",
        "STAT_CONTENT_CLEANSE", "ACC_A_CNY_AMOUNT", "ACC_A_CNY_YOY", "ACC_A_USD_AMOUNT",
        "ACC_A_USD_YOY", "MON_A_CNY_AMOUNT", "MON_A_CNY_YOY", "MON_A_USD_AMOUNT",
        "MON_A_USD_YOY", "ACC_E_CNY_AMOUNT", "ACC_E_CNY_YOY", "ACC_E_USD_AMOUNT",
        "ACC_E_USD_YOY", "MON_E_CNY_AMOUNT", "MON_E_CNY_YOY", "MON_E_USD_AMOUNT",
        "MON_E_USD_YOY", "ACC_I_CNY_AMOUNT", "ACC_I_CNY_YOY", "ACC_I_USD_AMOUNT",
        "ACC_I_USD_YOY", "MON_I_CNY_AMOUNT", "MON_I_CNY_YOY", "MON_I_USD_AMOUNT",
        "MON_I_USD_YOY", "ACC_E_AMOUNT", "ACC_E_AMOUNT_UNIT", "ACC_E_AMOUNT_YOY",
        "MON_E_AMOUNT", "MON_E_AMOUNT_UNIT", "MON_E_AMOUNT_YOY", "ACC_I_AMOUNT",
        "ACC_I_AMOUNT_UNIT", "ACC_I_AMOUNT_YOY", "MON_I_AMOUNT", "MON_I_AMOUNT_UNIT",
        "MON_I_AMOUNT_YOY", "RANK_MARKERS", "DATA_SOURCE", "EMPHASIS_OR_EMERGING_MARK",
        "CREATE_TIME"
    ]

    # 工作表名称到 STAT_TYPE 的映射
    sheet_to_stat_type = {
        '贸易方式': ('01', '贸易方式'),
        '企业性质': ('02', '企业性质'),
        '主要国别（地区）': ('03', '国别地区'),
        '主出商品': ('04', '主出商品'),
        '主进商品': ('05', '主进商品'),
        '十一地市': ('03', '国别地区'),
        '洲贸组织': ('03', '国别地区'),  # 添加可能的变体
    }

    # 从文件名提取日期
    filename = os.path.basename(input_excel_path)
    stat_date = extract_date_from_filename(filename)
    
    print(f"处理文件: {filename}")
    print(f"提取的日期: {stat_date}")

    for sheet_name in xls.sheet_names:
        # 跳过不需要的工作表
        if sheet_name not in sheet_to_stat_type:
            continue
            
        stat_type, stat_name = sheet_to_stat_type.get(sheet_name, ('99', '未知'))
        
        try:
            df = xls.parse(sheet_name)
            print(f"  正在处理工作表: {sheet_name}")
            
            # 数据清洗
            clean_df, project_col = clean_excel_data(df, sheet_name)
            
            print(f"  清洗后数据行数: {len(clean_df)} (原始: {len(df)})")
            
            if len(clean_df) == 0:
                print(f"  警告：工作表 {sheet_name} 没有有效数据")
                continue
            
            # 智能单位检测
            print(f"  正在检测单位...")
            
            # 1. 从标记检测单位
            marker_unit = detect_unit_from_markers(df)
            print(f"  标记检测单位: {marker_unit}")
            
            # 2. 从数据数值范围推断单位
            amount_columns = ['当期进出口', '当期进口', '当期出口', '当期']
            data_unit = detect_unit_from_data(clean_df, amount_columns)
            print(f"  数据推断单位: {data_unit}")
            
            # 3. 确定最终使用的单位
            if marker_unit != 'unknown':
                final_unit = marker_unit
                print(f"  使用标记检测的单位: {final_unit}")
            else:
                final_unit = data_unit
                print(f"  使用数据推断的单位: {final_unit}")
            
            # 4. 获取转换系数
            conversion_factor = get_conversion_factor(final_unit)
            print(f"  转换系数: {conversion_factor} (转换为亿元)")
            
            # 创建一个临时的DataFrame来存储当前工作表的数据
            temp_df = pd.DataFrame(columns=output_columns)
            
            # 基础字段
            temp_df['STAT_CONTENT_RAW'] = clean_df[project_col]
            # 数据清洗：去除多余空格
            temp_df['STAT_CONTENT_CLEANSE'] = clean_df[project_col].astype(str).str.strip()
            temp_df['STAT_TYPE'] = stat_type
            temp_df['STAT_NAME'] = stat_name
            temp_df['STAT_CODE'] = ''
            
            # 处理不同工作表的数据结构
            if sheet_name in ['贸易方式', '企业性质', '主要国别（地区）', '十一地市', '洲贸组织']:
                # 这些表有标准的进出口结构
                if '当期进出口' in clean_df.columns:
                    temp_df['MON_A_CNY_AMOUNT'] = pd.to_numeric(clean_df['当期进出口'], errors='coerce') / conversion_factor
                if '当期进口' in clean_df.columns:
                    temp_df['MON_I_CNY_AMOUNT'] = pd.to_numeric(clean_df['当期进口'], errors='coerce') / conversion_factor
                if '当期出口' in clean_df.columns:
                    temp_df['MON_E_CNY_AMOUNT'] = pd.to_numeric(clean_df['当期出口'], errors='coerce') / conversion_factor
                
                # 同比数据
                if '进出口同比' in clean_df.columns:
                    temp_df['MON_A_CNY_YOY'] = pd.to_numeric(clean_df['进出口同比'], errors='coerce')
                if '进口同比' in clean_df.columns:
                    temp_df['MON_I_CNY_YOY'] = pd.to_numeric(clean_df['进口同比'], errors='coerce')
                if '出口同比' in clean_df.columns:
                    temp_df['MON_E_CNY_YOY'] = pd.to_numeric(clean_df['出口同比'], errors='coerce')
                    
            elif sheet_name in ['主出商品', '主进商品']:
                # 商品表有不同结构
                if '当期' in clean_df.columns:
                    if sheet_name == '主出商品':
                        temp_df['MON_E_CNY_AMOUNT'] = pd.to_numeric(clean_df['当期'], errors='coerce') / conversion_factor
                        temp_df['MON_E_CNY_YOY'] = pd.to_numeric(clean_df['同比'], errors='coerce')
                    else:  # 主进商品
                        temp_df['MON_I_CNY_AMOUNT'] = pd.to_numeric(clean_df['当期'], errors='coerce') / conversion_factor
                        temp_df['MON_I_CNY_YOY'] = pd.to_numeric(clean_df['同比'], errors='coerce')
            
            # 设置累计数据为空（浙江数据只有当月数据）
            acc_columns = [
                'ACC_A_CNY_AMOUNT', 'ACC_A_CNY_YOY', 'ACC_A_USD_AMOUNT', 'ACC_A_USD_YOY',
                'ACC_E_CNY_AMOUNT', 'ACC_E_CNY_YOY', 'ACC_E_USD_AMOUNT', 'ACC_E_USD_YOY',
                'ACC_I_CNY_AMOUNT', 'ACC_I_CNY_YOY', 'ACC_I_USD_AMOUNT', 'ACC_I_USD_YOY',
                'ACC_E_AMOUNT', 'ACC_E_AMOUNT_UNIT', 'ACC_E_AMOUNT_YOY',
                'ACC_I_AMOUNT', 'ACC_I_AMOUNT_UNIT', 'ACC_I_AMOUNT_YOY'
            ]
            for col in acc_columns:
                temp_df[col] = np.nan
            
            # 设置美元金额为空（浙江数据只有人民币金额）
            usd_columns = [
                'ACC_A_USD_AMOUNT', 'ACC_A_USD_YOY', 'MON_A_USD_AMOUNT', 'MON_A_USD_YOY',
                'ACC_E_USD_AMOUNT', 'ACC_E_USD_YOY', 'MON_E_USD_AMOUNT', 'MON_E_USD_YOY',
                'ACC_I_USD_AMOUNT', 'ACC_I_USD_YOY', 'MON_I_USD_AMOUNT', 'MON_I_USD_YOY'
            ]
            for col in usd_columns:
                temp_df[col] = np.nan
                
            # 设置数量单位为空
            amount_unit_columns = [
                'MON_E_AMOUNT_UNIT', 'MON_I_AMOUNT_UNIT', 'ACC_E_AMOUNT_UNIT', 'ACC_I_AMOUNT_UNIT'
            ]
            for col in amount_unit_columns:
                temp_df[col] = ''
            
            # 设置当月数量为空
            temp_df['MON_E_AMOUNT'] = np.nan
            temp_df['MON_I_AMOUNT'] = np.nan
            temp_df['MON_E_AMOUNT_YOY'] = np.nan
            temp_df['MON_I_AMOUNT_YOY'] = np.nan
            
            output_dfs.append(temp_df)
            
        except Exception as e:
            print(f"  处理工作表 {sheet_name} 时出错: {e}")
            continue

    if not output_dfs:
        print("没有成功处理任何数据")
        return None

    # 合并所有处理过的数据
    final_df = pd.concat(output_dfs, ignore_index=True)
    
    # 填充公共字段
    final_df['STAT_DATE'] = stat_date
    final_df['DATA_SOURCE'] = '07'  # 浙江（修正编码）
    final_df['EMPHASIS_OR_EMERGING_MARK'] = ''
    final_df['RANK_MARKERS'] = ''
    final_df['CREATE_TIME'] = datetime.datetime.now().strftime('%Y/%m/%d %H:%M:%S')
    
    # 确保所有列都存在
    for col in output_columns:
        if col not in final_df.columns:
            final_df[col] = np.nan if 'AMOUNT' in col or 'YOY' in col else ''
    
    # 按照目标顺序排列列
    final_df = final_df[output_columns]
    
    # 数据清理：将空字符串替换为NaN，然后填充适当的默认值
    for col in final_df.columns:
        if 'AMOUNT' in col or 'YOY' in col:
            final_df[col] = pd.to_numeric(final_df[col], errors='coerce')
        elif 'UNIT' in col or 'CODE' in col or 'MARKERS' in col or 'MARK' in col:
            final_df[col] = final_df[col].fillna('').astype(str)
    
    # 将处理好的数据保存为CSV文件
    final_df.to_csv(output_csv_path, index=False, encoding='utf-8-sig', float_format='%.4f')
    print(f"数据已成功转换并保存到 {output_csv_path}")
    print(f"总记录数: {len(final_df)}")
    print(f"字段数: {len(final_df.columns)}")
    
    return final_df

def batch_transform_excel_files(input_dir, output_dir):
    """
    批量转换Excel文件
    """
    # 创建输出目录
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    # 获取所有Excel文件
    excel_files = []
    for file in os.listdir(input_dir):
        if file.endswith('.xlsx') and not file.startswith('~'):
            excel_files.append(file)
    
    if not excel_files:
        print(f"在目录 {input_dir} 中没有找到Excel文件")
        return
    
    print(f"找到 {len(excel_files)} 个Excel文件")
    
    # 统计信息
    total_files = 0
    successful_files = 0
    total_records = 0
    
    for excel_file in sorted(excel_files):
        total_files += 1
        
        input_path = os.path.join(input_dir, excel_file)
        output_filename = f"T_STATISTICAL_CUS_TOTAL_{os.path.splitext(excel_file)[0]}.csv"
        output_path = os.path.join(output_dir, output_filename)
        
        print(f"\n{'='*60}")
        print(f"处理文件 {total_files}/{len(excel_files)}: {excel_file}")
        print(f"{'='*60}")
        
        try:
            result_df = transform_single_excel(input_path, output_path)
            if result_df is not None:
                successful_files += 1
                total_records += len(result_df)
                print(f"成功处理: {excel_file} ({len(result_df)} 条记录)")
            else:
                print(f"处理失败: {excel_file}")
        except Exception as e:
            print(f"处理出错 {excel_file}: {e}")
    
    print(f"\n{'='*60}")
    print("批量处理完成！")
    print(f"{'='*60}")
    print(f"总文件数: {total_files}")
    print(f"成功处理: {successful_files}")
    print(f"失败文件: {total_files - successful_files}")
    print(f"总记录数: {total_records}")
    print(f"输出目录: {output_dir}")

if __name__ == '__main__':
    # 配置路径
    input_directory = "浙江录入的"
    output_directory = "批量转换结果_智能单位检测"
    
    print("=== 浙江海关数据批量转换（智能单位检测） ===")
    print(f"输入目录: {input_directory}")
    print(f"输出目录: {output_directory}")
    print(f"开始时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    batch_transform_excel_files(input_directory, output_directory)
    
    print(f"\n结束时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")