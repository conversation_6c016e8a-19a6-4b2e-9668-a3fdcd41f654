import pandas as pd

# 读取表结构文件
db_df = pd.read_csv('T_STATISTICAL_CUS_TOTAL_CS结构和注释.csv', encoding='gbk')

sql_statements = []

# 表头
sql_statements.append('-- 海关统计数据表建表语句')
sql_statements.append('-- 删除现有表（如果存在）')
sql_statements.append('DROP TABLE T_STATISTICAL_CUS_TOTAL_CS CASCADE CONSTRAINTS;')
sql_statements.append('')
sql_statements.append('-- 创建新表')
sql_statements.append('CREATE TABLE T_STATISTICAL_CUS_TOTAL_CS (')

# 生成字段定义
for i, row in db_df.iterrows():
    col_name = row.iloc[2]  # 列名
    col_comment = row.iloc[3]  # 列注释
    
    # 根据字段类型确定数据类型
    if 'AMOUNT' in col_name or 'YOY' in col_name:
        data_type = 'NUMBER(18,4)'
    elif col_name == 'DATA_SOURCE':
        data_type = 'VARCHAR2(10)'
    elif col_name in ['STAT_CODE', 'STAT_TYPE']:
        data_type = 'VARCHAR2(20)'
    elif col_name in ['CREATE_TIME', 'STAT_DATE']:
        data_type = 'VARCHAR2(50)'
    elif 'UNIT' in col_name:
        data_type = 'VARCHAR2(20)'
    elif col_name in ['EMPHASIS_OR_EMERGING_MARK', 'RANK_MARKERS']:
        data_type = 'VARCHAR2(10)'
    else:
        data_type = 'VARCHAR2(500)'
    
    # 最后一个字段不加逗号
    comma = ',' if i < len(db_df) - 1 else ''
    
    sql_statements.append(f'    {col_name} {data_type}{comma}  -- {col_comment}')

sql_statements.append(');')
sql_statements.append('')

# 添加表注释
table_comment = db_df.iloc[0, 1]  # 表注释
sql_statements.append(f"COMMENT ON TABLE T_STATISTICAL_CUS_TOTAL_CS IS '{table_comment}';")
sql_statements.append('')

# 添加列注释
sql_statements.append('-- 添加列注释')
for i, row in db_df.iterrows():
    col_name = row.iloc[2]
    col_comment = row.iloc[3]
    sql_statements.append(f"COMMENT ON COLUMN T_STATISTICAL_CUS_TOTAL_CS.{col_name} IS '{col_comment}';")

# 输出到文件和控制台
with open('create_table.sql', 'w', encoding='utf-8') as f:
    for line in sql_statements:
        f.write(line + '\n')
        print(line)

print(f'\nSQL语句已保存到 create_table.sql 文件')