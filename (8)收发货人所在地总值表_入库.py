import pandas as pd
import os
import re
import cx_Oracle
import numpy as np

def convert_to_float_or_none(value):
    """清理数据，将'-'或空值转为None，其余转为浮点数"""
    if pd.isna(value) or str(value).strip() == '-':
        return None
    try:
        value_str = str(value).replace(',', '')
        return float(value_str)
    except (ValueError, TypeError):
        return None

def parse_table_8(file_path):
    """
    专门为(8)号表格“进出口商品收发货人所在地总值表”编写的解析器。
    """
    try:
        # 这个表结构比较特殊，标题和数据的位置相对固定，我们直接读取整个表
        df_raw = pd.read_excel(file_path, header=None)
        
        # 1. 动态查找单位
        unit = "不明"
        for i in range(min(5, len(df_raw))):
            row_text = ' '.join(str(s) for s in df_raw.iloc[i].tolist() if pd.notna(s))
            unit_match = re.search(r'单位[：:]\s*([^\s]+)', row_text)
            if unit_match:
                unit = unit_match.group(1).strip()
                break

        # 1. 提取年月。标题通常在第二行第二列 (B2)
        title_text = df_raw.iloc[1, 1] if len(df_raw) > 1 and len(df_raw.columns) > 1 else ""
        year, month = "", ""
        if isinstance(title_text, str):
            year_month_match = re.search(r'(\d{4})年(\d{1,2})月', title_text)
            if year_month_match:
                year, month = year_month_match.groups()
        
        if not (year and month):
             print(f"    [!] 错误: 无法从文件 '{os.path.basename(file_path)}' 的标题中提取年月。")
             return None, None

        # 2. 查找数据起始行 "总值"
        data_start_row = -1
        # 数据通常在B列，即索引1
        for i, row in df_raw.iterrows():
             # 确保该行在索引1处有值
            if len(row) > 1 and isinstance(row.iloc[1], str) and row.iloc[1].strip() == "总值":
                data_start_row = i
                break
        
        if data_start_row == -1:
            print(f"    [!] 错误: 在文件 '{os.path.basename(file_path)}' 中未找到'总值'数据行。")
            return None, None

        # 3. 确定数据结束行 (到"注："之前)
        data_end_row = len(df_raw)
        for i in range(data_start_row, len(df_raw)):
            if len(df_raw.iloc[i]) > 1 and isinstance(df_raw.iloc[i, 1], str) and df_raw.iloc[i, 1].strip().startswith("注"):
                data_end_row = i
                break
        
        # 4. 切片数据区域。数据在B列到K列 (索引1到10)
        df_sliced = df_raw.iloc[data_start_row:data_end_row, 1:11].copy()
        
        # 5. 重命名列并处理数据
        df_sliced.columns = [
            'LOCATION', 'MONTH_IE_AMOUNT', 'YTD_IE_AMOUNT',
            'MONTH_EXP_AMOUNT', 'YTD_EXP_AMOUNT', 'MONTH_IMP_AMOUNT', 'YTD_IMP_AMOUNT',
            'YTD_IE_YOY', 'YTD_EXP_YOY', 'YTD_IMP_YOY'
        ]

        processed_data = []
        for _, row in df_sliced.iterrows():
            if pd.isna(row['LOCATION']) or str(row['LOCATION']).strip() == '':
                continue
            
            processed_data.append({
                'LOCATION': str(row['LOCATION']).strip(),
                'MONTH_IE_AMOUNT': convert_to_float_or_none(row['MONTH_IE_AMOUNT']),
                'YTD_IE_AMOUNT': convert_to_float_or_none(row['YTD_IE_AMOUNT']),
                'YTD_IE_YOY': convert_to_float_or_none(row['YTD_IE_YOY']),
                'MONTH_EXP_AMOUNT': convert_to_float_or_none(row['MONTH_EXP_AMOUNT']),
                'YTD_EXP_AMOUNT': convert_to_float_or_none(row['YTD_EXP_AMOUNT']),
                'YTD_EXP_YOY': convert_to_float_or_none(row['YTD_EXP_YOY']),
                'MONTH_IMP_AMOUNT': convert_to_float_or_none(row['MONTH_IMP_AMOUNT']),
                'YTD_IMP_AMOUNT': convert_to_float_or_none(row['YTD_IMP_AMOUNT']),
                'YTD_IMP_YOY': convert_to_float_or_none(row['YTD_IMP_YOY']),
            })
            
        return pd.DataFrame(processed_data), f"{year}{month.zfill(2)}01", unit

    except Exception as e:
        print(f"    [!] 解析文件 '{os.path.basename(file_path)}' 时出错: {e}")
        return None, None, None

def check_if_data_exists_8(current_month, currency_type, cursor):
    """根据年月和货币类型检查数据是否已存在"""
    query = "SELECT COUNT(*) FROM temp_cus_mon_8 WHERE CURRENT_MONTH = TO_DATE(:1, 'YYYYMMDD') AND CURRENCY_TYPE = :2 AND LOCATION = '总值'"
    cursor.execute(query, (current_month, currency_type))
    return cursor.fetchone()[0] > 0

def batch_process_directory_8(directory_path, cursor):
    """遍历目录，对2021年及以后版本的新文件执行增量入库"""
    print(f"\n--- 开始扫描目录: {directory_path} ---")
    if not os.path.isdir(directory_path):
        print(f"    [!] 错误: 目录 '{directory_path}' 不存在")
        return

    for filename in sorted(os.listdir(directory_path)):
        # 表8的文件可能是.xls或.xlsx
        if not (filename.lower().endswith(".xlsx") or filename.lower().endswith(".xls")):
            continue
        
        # 从目录路径判断年份（如果文件名不含年份）
        dir_year_match = re.search(r'(\d{4})', directory_path)
        file_year_match = re.search(r'(\d{4})', filename)
        
        year = None
        if file_year_match:
            year = int(file_year_match.group(1))
        elif dir_year_match:
            year = int(dir_year_match.group(1))

        if year and year < 2021:
            continue
        
        file_path = os.path.join(directory_path, filename)
        
        df, current_month_str, unit_from_file = parse_table_8(file_path)

        if df is None or df.empty:
            continue
        
        currency_type = "人民币" if "人民币" in directory_path else "美元"

        if check_if_data_exists_8(current_month_str, currency_type, cursor):
            continue
        
        print(f"    [*] 发现新数据, 正在处理: {filename}")

        df['CURRENT_MONTH'] = current_month_str
        df['CURRENCY_TYPE'] = currency_type
        
        # 优先使用动态解析的单位，如果失败则使用默认值
        if unit_from_file and unit_from_file != "不明":
            df['UNIT'] = unit_from_file
        else:
            df['UNIT'] = "万元" if currency_type == "人民币" else "千美元"
        
        column_order = [
            'LOCATION', 'CURRENT_MONTH', 'CURRENCY_TYPE', 'UNIT',
            'MONTH_IE_AMOUNT', 'YTD_IE_AMOUNT', 'YTD_IE_YOY',
            'MONTH_EXP_AMOUNT', 'YTD_EXP_AMOUNT', 'YTD_EXP_YOY',
            'MONTH_IMP_AMOUNT', 'YTD_IMP_AMOUNT', 'YTD_IMP_YOY'
        ]
        df = df[column_order]
        df = df.replace({np.nan: None})
        data_to_insert = [tuple(row) for row in df.to_records(index=False)]

        insert_query = """
        INSERT INTO temp_cus_mon_8 (
            LOCATION, CURRENT_MONTH, CURRENCY_TYPE, UNIT,
            MONTH_IE_AMOUNT, YTD_IE_AMOUNT, YTD_IE_YOY,
            MONTH_EXP_AMOUNT, YTD_EXP_AMOUNT, YTD_EXP_YOY,
            MONTH_IMP_AMOUNT, YTD_IMP_AMOUNT, YTD_IMP_YOY
        ) VALUES (
            :1, TO_DATE(:2, 'YYYYMMDD'), :3, :4, :5, :6, :7, :8, :9, :10, :11, :12, :13
        )
        """
        try:
            cursor.executemany(insert_query, data_to_insert)
            print(f"        -> 成功插入 {cursor.rowcount} 条记录.")
        except cx_Oracle.Error as e:
            print(f"        [!] 数据库插入错误: {e}")
    print(f"--- 目录扫描完成 ---")

if __name__ == "__main__":
    base_dir = os.getcwd()
    rmb_base_dir = os.path.join(base_dir, "进出口商品统计表_人民币值")
    usd_base_dir = os.path.join(base_dir, "进出口商品统计表_美元值")

    # 由于表8的目录结构可能每年都变，我们需要遍历查找
    for year in range(2021, 2030): # 假设处理到2029年
        rmb_year_dir = os.path.join(rmb_base_dir, f"{year}年进出口商品收发货人所在地总值表")
        usd_year_dir = os.path.join(usd_base_dir, f"{year}年进出口商品收发货人所在地总值表")
        # 您提供的路径是: 年进出口商品收发货人所在地总值表\2025年进出口商品收发货人所在地总值表
        # 这暗示了目录结构可能更复杂，我们简化处理，直接查找年份目录
        rmb_dir_to_scan = os.path.join(rmb_base_dir, "(8)进出口商品收发货人所在地总值表_人民币值")
        usd_dir_to_scan = os.path.join(usd_base_dir, "(8)进出口商品收发货人所在地总值表_美元值")

    conn = None
    try:
        print("正在连接到Oracle数据库...")
        conn = cx_Oracle.connect("manifest_dcb/manifest_dcb@192.168.1.151/TEST")
        cursor = conn.cursor()
        print("数据库连接成功。")

        # 扫描固定的总目录
        batch_process_directory_8(rmb_dir_to_scan, cursor)
        batch_process_directory_8(usd_dir_to_scan, cursor)
        
        conn.commit()
        print("\n数据提交成功。")
        
    except cx_Oracle.Error as error:
        print(f"\n[!!!] 数据库操作期间发生严重错误: {error}")
    except Exception as e:
        print(f"\n[!!!] 批量处理期间发生未知错误: {e}")
    finally:
        if 'cursor' in locals() and cursor: cursor.close()
        if conn:
            conn.close()
            print("数据库连接已关闭。") 