# 文件名: (13_14)统一-主要商品量值入库.py
# 这是一个完整、可运行的、合并了(13)和(14)号表处理逻辑的最终版本。

import pandas as pd
import os
import re
import cx_Oracle
import numpy as np
from tqdm import tqdm
from pathlib import Path
import sys

def get_db_connection():
    """建立并返回数据库连接"""
    try:
        connection = cx_Oracle.connect("manifest_dcb/manifest_dcb@*************/TEST")
        return connection
    except Exception as e:
        print(f"数据库连接失败: {e}")
        sys.exit(1)

def convert_to_float_or_none(value):
    """清理数据"""
    if pd.isna(value) or str(value).strip() == '-':
        return None
    try:
        return float(str(value).replace(',', ''))
    except (ValueError, TypeError):
        return None

def parse_major_product_data(file_path):
    """
    【融合】一个通用的解析器，能够处理(13)和(14)号表相似的Excel结构。
    """
    try:
        df_raw = pd.read_excel(file_path, header=None)
        
        title_text = df_raw.iloc[1, 1] if len(df_raw) > 1 and len(df_raw.columns) > 1 else ""
        year, month = "", ""
        if isinstance(title_text, str):
            year_month_match = re.search(r'(\d{4})年(\d{1,2})月', title_text)
            if year_month_match:
                year, month = year_month_match.groups()
        
        if not (year and month): return None, None

        header_start_row = -1
        for i, row in df_raw.iterrows():
            if row.astype(str).str.contains('商品名称').any():
                header_start_row = i
                break
        
        if header_start_row == -1: return None, None
            
        data_start_row = header_start_row + 2
        
        # 定义所有可能的列名
        all_column_names = [
            'COMMODITY_NAME', 'UNIT', 
            'MONTH_QUANTITY', 'MONTH_AMOUNT', 'YTD_QUANTITY', 'YTD_AMOUNT',
            'MONTH_QUANTITY_YOY', 'MONTH_AMOUNT_YOY', 'YTD_QUANTITY_YOY', 'YTD_AMOUNT_YOY',
            'YOY_MONTH_EXPORT_WEIGHT', 'YOY_YTD_EXPORT_WEIGHT', # 出口特定列
            'YOY_MONTH_IMPORT_WEIGHT', 'YOY_YTD_IMPORT_WEIGHT'  # 进口特定列
        ]
        
        # 根据实际是出口还是进口文件，选择正确的最后两列名称
        is_export = '出口' in title_text
        if is_export:
            current_cols = all_column_names[:10] + all_column_names[10:12]
        else: # is_import
            current_cols = all_column_names[:10] + all_column_names[12:14]

        df_sliced = df_raw.iloc[data_start_row:, 1:13].copy()
        
        num_actual_cols = df_sliced.shape[1]
        
        if num_actual_cols == 10:
            df_sliced.columns = current_cols[:10]
        elif num_actual_cols == 12:
            df_sliced.columns = current_cols
        else:
            return None, None
        
        # 标准化df，确保所有可能的列都存在
        for col_name in all_column_names:
            if col_name not in df_sliced.columns:
                df_sliced[col_name] = np.nan

        df_sliced.dropna(subset=['COMMODITY_NAME'], inplace=True)
        df_sliced = df_sliced[~df_sliced['COMMODITY_NAME'].astype(str).str.strip().str.startswith('注：')]
        if df_sliced.empty: return None, None

        numeric_cols = all_column_names[2:]
        for col in numeric_cols:
            df_sliced[col] = df_sliced[col].apply(convert_to_float_or_none)

        df_sliced['COMMODITY_NAME'] = df_sliced['COMMODITY_NAME'].astype(str).str.strip()
        df_sliced['UNIT'] = df_sliced['UNIT'].astype(str).str.strip().replace('nan', '-')

        return df_sliced, f"{year}{month.zfill(2)}01"

    except Exception as e:
        print(f"    [!] 解析文件 '{os.path.basename(file_path)}' 时出错: {e}")
        return None, None

def upsert_to_13_14(connection, df, trade_direction):
    """
    【新】使用MERGE语句将数据插入或更新到统一表 CUS_TRADE_MAJOR_PRODUCT_MON
    """
    if df is None or df.empty: return 0

    cursor = connection.cursor()
    
    df['TRADE_DIRECTION'] = trade_direction
    df = df.replace({np.nan: None})
    df.columns = [col.upper() for col in df.columns]
    data_to_merge = df.to_dict('records')

    merge_sql = """
    MERGE INTO CUS_TRADE_MAJOR_PRODUCT_MON dest
    USING (
        SELECT :CURRENT_MONTH AS current_month, :CURRENCY_TYPE AS currency_type, :COMMODITY_NAME AS commodity_name, :TRADE_DIRECTION AS trade_direction FROM dual
    ) src ON (
        dest.current_month = TO_DATE(src.current_month, 'YYYYMMDD') AND
        dest.currency_type = src.currency_type AND
        dest.commodity_name = src.commodity_name AND
        dest.trade_direction = src.trade_direction
    )
    WHEN MATCHED THEN
        UPDATE SET
            dest.unit = :UNIT,
            dest.month_quantity = :MONTH_QUANTITY, dest.month_amount = :MONTH_AMOUNT,
            dest.ytd_quantity = :YTD_QUANTITY, dest.ytd_amount = :YTD_AMOUNT,
            dest.month_quantity_yoy = :MONTH_QUANTITY_YOY, dest.month_amount_yoy = :MONTH_AMOUNT_YOY,
            dest.ytd_quantity_yoy = :YTD_QUANTITY_YOY, dest.ytd_amount_yoy = :YTD_AMOUNT_YOY,
            dest.yoy_month_export_weight = :YOY_MONTH_EXPORT_WEIGHT, dest.yoy_ytd_export_weight = :YOY_YTD_EXPORT_WEIGHT,
            dest.yoy_month_import_weight = :YOY_MONTH_IMPORT_WEIGHT, dest.yoy_ytd_import_weight = :YOY_YTD_IMPORT_WEIGHT
    WHEN NOT MATCHED THEN
        INSERT (
            COMMODITY_NAME, CURRENT_MONTH, CURRENCY_TYPE, TRADE_DIRECTION,
            UNIT, MONTH_QUANTITY, MONTH_AMOUNT, YTD_QUANTITY, YTD_AMOUNT,
            MONTH_QUANTITY_YOY, MONTH_AMOUNT_YOY, YTD_QUANTITY_YOY, YTD_AMOUNT_YOY,
            YOY_MONTH_EXPORT_WEIGHT, YOY_YTD_EXPORT_WEIGHT,
            YOY_MONTH_IMPORT_WEIGHT, YOY_YTD_IMPORT_WEIGHT
        ) VALUES (
            :COMMODITY_NAME, TO_DATE(:CURRENT_MONTH, 'YYYYMMDD'), :CURRENCY_TYPE, :TRADE_DIRECTION,
            :UNIT, :MONTH_QUANTITY, :MONTH_AMOUNT, :YTD_QUANTITY, :YTD_AMOUNT,
            :MONTH_QUANTITY_YOY, :MONTH_AMOUNT_YOY, :YTD_QUANTITY_YOY, :YTD_AMOUNT_YOY,
            :YOY_MONTH_EXPORT_WEIGHT, :YOY_YTD_EXPORT_WEIGHT,
            :YOY_MONTH_IMPORT_WEIGHT, :YOY_YTD_IMPORT_WEIGHT
        )
    """
    try:
        cursor.executemany(merge_sql, data_to_merge)
        connection.commit()
        print(f"        -> 成功合并 {cursor.rowcount} 条 [{trade_direction}] 记录.")
        return cursor.rowcount
    except Exception as e:
        print(f"        [!] 执行批量合并时发生严重错误: {e}")
        connection.rollback()
        return 0
    finally:
        cursor.close()

def process_directory(directory_path, trade_direction, connection):
    if not os.path.isdir(directory_path):
        print(f"    [!] 目录不存在: {directory_path}")
        return
    
    files = [f for f in sorted(os.listdir(directory_path)) if f.lower().endswith(('.xls', '.xlsx')) and not f.startswith('~')]
    print(f"\n--- 开始扫描目录 ({trade_direction}): {directory_path} ---")
    
    for filename in tqdm(files, desc=f"处理 {os.path.basename(directory_path)}"):
        file_path = os.path.join(directory_path, filename)
        df, current_month_str = parse_major_product_data(file_path)
        
        if df is not None and not df.empty:
            df['current_month'] = current_month_str
            df['currency_type'] = "人民币" if "人民币" in filename else "美元"
            upsert_to_13_14(connection, df, trade_direction)

def main():
    base_dir = os.getcwd()
    # (13) 出口目录
    exp_rmb_dir_13 = os.path.join(base_dir, r"进出口商品统计表_人民币值\(13)出口主要商品量值表_人民币值")
    exp_usd_dir_13 = os.path.join(base_dir, r"进出口商品统计表_美元值\(13)出口主要商品量值表_美元值")
    # (14) 进口目录
    imp_rmb_dir_14 = os.path.join(base_dir, r"进出口商品统计表_人民币值\(14)进口主要商品量值表_人民币值")
    imp_usd_dir_14 = os.path.join(base_dir, r"进出口商品统计表_美元值\(14)进口主要商品量值表_美元值")

    connection = None
    try:
        connection = get_db_connection()
        print("数据库连接成功.")
        
        process_directory(exp_rmb_dir_13, '出口', connection)
        process_directory(exp_usd_dir_13, '出口', connection)
        process_directory(imp_rmb_dir_14, '进口', connection)
        process_directory(imp_usd_dir_14, '进口', connection)

        print("\n--- (13)和(14)所有目录处理完毕 ---")
    except Exception as e:
        print(f"\n处理过程中发生未知错误: {e}")
    finally:
        if connection:
            connection.close()
            print("数据库连接已关闭。")

if __name__ == "__main__":
    main() 