from DrissionPage import ChromiumPage
import time
import os
import re
import shutil

def download_file_with_browser(page, file_url, save_path, temp_download_dir):
    """使用浏览器下载文件"""
    try:
        print(f"使用浏览器访问文件URL: {file_url}")

        # 清空临时下载目录
        for temp_file in os.listdir(temp_download_dir):
            temp_file_path = os.path.join(temp_download_dir, temp_file)
            if os.path.isfile(temp_file_path):
                os.remove(temp_file_path)

        # 直接访问文件URL，这会触发下载
        page.get(file_url)

        # 等待下载完成
        downloaded = False
        start_time = time.time()
        timeout = 60  # 设置超时时间为60秒

        while not downloaded and time.time() - start_time < timeout:
            time.sleep(2)  # 每2秒检查一次

            # 检查临时目录中是否有新文件
            temp_files = os.listdir(temp_download_dir)
            if temp_files:
                for temp_file in temp_files:
                    if temp_file.endswith(('.xls', '.xlsx')) and not temp_file.endswith('.crdownload'):
                        # 找到下载的文件
                        temp_file_path = os.path.join(temp_download_dir, temp_file)
                        print(f"文件已下载到临时位置: {temp_file_path}")

                        # 移动并重命名文件
                        shutil.copy2(temp_file_path, save_path)
                        print(f"文件已复制到最终位置: {save_path}")
                        downloaded = True
                        return True
                    elif temp_file.endswith('.crdownload'):
                        print(f"文件正在下载中: {temp_file}")

        if not downloaded:
            print("下载超时")
            return False

    except Exception as e:
        print(f"下载文件时出错: {e}")
        return False

def download_from_collected_links():
    """基于已收集的链接下载文件"""

    print("=== 基于已收集链接下载北京海关数据 ===")

    # 读取已收集的链接
    links_file = "北京海关数据链接_最终版.txt"
    if not os.path.exists(links_file):
        print(f"链接文件不存在: {links_file}")
        return

    # 创建下载目录
    base_dir = "北京海关统计数据_最终版"
    if not os.path.exists(base_dir):
        os.makedirs(base_dir)
        print(f"创建下载目录: {base_dir}")

    # 创建临时下载目录
    temp_download_dir = "temp_download_beijing_2024_2025"
    if not os.path.exists(temp_download_dir):
        os.makedirs(temp_download_dir)
        print(f"创建临时下载目录: {temp_download_dir}")

    # 设置ChromiumPage
    page = ChromiumPage(timeout=30)
    page.set.download_path(os.path.abspath(temp_download_dir))
    print(f"设置下载目录: {temp_download_dir}")

    try:
        # 读取链接文件
        all_links = []
        with open(links_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            for line in lines[1:]:  # 跳过标题行
                line = line.strip()
                if line:
                    parts = line.split('\t')
                    if len(parts) >= 5:
                        all_links.append({
                            'page': parts[0],
                            'year': parts[1],
                            'table_key': parts[2],
                            'text': parts[3],
                            'url': parts[4]
                        })

        print(f"从文件中读取到 {len(all_links)} 个链接")

        # 按年份统计
        year_2024_count = len([link for link in all_links if link['year'] == '2024'])
        year_2025_count = len([link for link in all_links if link['year'] == '2025'])

        print(f"2024年数据: {year_2024_count} 个")
        print(f"2025年数据: {year_2025_count} 个")

        # 开始下载
        download_count = 0
        error_count = 0

        for i, link_info in enumerate(all_links):
            try:
                print(f"\n处理第 {i+1}/{len(all_links)} 个链接...")
                print(f"页码: {link_info['page']}")
                print(f"年份: {link_info['year']}")
                print(f"表格: {link_info['table_key']}")
                print(f"URL: {link_info['url']}")

                # 访问详情页
                page.get(link_info['url'])
                time.sleep(3)

                # 查找页面中的.xls或.xlsx文件链接
                xls_links = page.eles('tag:a')
                file_url = None

                for xls_link in xls_links:
                    href = xls_link.attr('href')
                    if href and (href.endswith('.xls') or href.endswith('.xlsx')):
                        # 构建完整的文件URL
                        if href.startswith('/'):
                            file_url = "http://beijing.customs.gov.cn" + href
                        elif not href.startswith('http'):
                            current_url = page.url
                            base_path = '/'.join(current_url.split('/')[:-1]) + '/'
                            file_url = base_path + href
                        else:
                            file_url = href
                        break

                if not file_url:
                    # 尝试从meta标签中获取文件URL
                    meta_elements = page.eles('tag:meta')
                    for meta in meta_elements:
                        content = meta.attr('content')
                        if content and (content.endswith('.xls') or content.endswith('.xlsx')):
                            file_url = content
                            break

                if file_url:
                    print(f"找到文件URL: {file_url}")

                    # 生成本地文件名
                    clean_text = re.sub(r'[\\/*?:"<>|]', '_', link_info['text'])
                    file_name = f"{link_info['year']}_{link_info['table_key']}_{clean_text}.xls"
                    file_path = os.path.join(base_dir, file_name)

                    # 检查文件是否已存在
                    if os.path.exists(file_path):
                        print(f"文件已存在，跳过: {file_name}")
                        continue

                    # 下载文件
                    print(f"开始下载: {file_name}")
                    if download_file_with_browser(page, file_url, file_path, temp_download_dir):
                        print(f"下载成功: {file_name}")
                        download_count += 1
                    else:
                        print(f"下载失败: {file_name}")
                        error_count += 1
                else:
                    print("未找到文件下载链接")
                    error_count += 1

            except Exception as e:
                print(f"处理链接时出错: {e}")
                error_count += 1

            # 防止请求过快
            time.sleep(2)

        print(f"\n=== 下载完成 ===")
        print(f"成功下载: {download_count} 个文件")
        print(f"失败: {error_count} 个文件")

    except Exception as e:
        print(f"主程序执行出错: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # 清理临时目录
        try:
            if os.path.exists(temp_download_dir):
                shutil.rmtree(temp_download_dir)
                print(f"临时目录已清理: {temp_download_dir}")
        except Exception as e:
            print(f"清理临时目录出错: {e}")

        # 关闭浏览器
        try:
            page.quit()
            print("浏览器已关闭")
        except:
            pass

if __name__ == "__main__":
    download_from_collected_links()