"""
检查Oracle表T_STATISTICAL_CUS_TOTAL_CS的实际结构
"""

try:
    import cx_Oracle
    ORACLE_AVAILABLE = True
except ImportError:
    ORACLE_AVAILABLE = False
    print("警告：未安装cx_Oracle模块")

def check_table_structure(db_config):
    """
    检查Oracle表的实际结构
    """
    if not ORACLE_AVAILABLE:
        print("❌ 无法连接数据库：cx_Oracle模块未安装")
        return
    
    try:
        conn = cx_Oracle.connect(**db_config)
        cursor = conn.cursor()
        
        # 查询表结构
        cursor.execute("""
            SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, NULLABLE, COLUMN_ID
            FROM USER_TAB_COLUMNS 
            WHERE TABLE_NAME = 'T_STATISTICAL_CUS_TOTAL_CS'
            ORDER BY COLUMN_ID
        """)
        
        columns = cursor.fetchall()
        
        print("=" * 80)
        print("Oracle表 T_STATISTICAL_CUS_TOTAL_CS 的实际结构：")
        print("=" * 80)
        print(f"{'序号':<4} {'字段名':<25} {'数据类型':<15} {'长度':<8} {'可空':<8}")
        print("-" * 80)
        
        for col in columns:
            column_name, data_type, data_length, nullable, column_id = col
            print(f"{column_id:<4} {column_name:<25} {data_type:<15} {data_length if data_length else '':<8} {nullable:<8}")
        
        print(f"\n总字段数: {len(columns)}")
        
        # 生成正确的INSERT语句
        print("\n" + "=" * 80)
        print("生成正确的INSERT语句：")
        print("=" * 80)
        
        field_names = [col[0] for col in columns]
        placeholders = [f":{i+1}" for i in range(len(columns))]
        
        insert_sql = f"""INSERT INTO T_STATISTICAL_CUS_TOTAL_CS (
    {', '.join(field_names)}
) VALUES (
    {', '.join(placeholders)}
)"""
        
        print(insert_sql)
        
        # 保存字段列表到文件
        with open('oracle_table_fields.txt', 'w', encoding='utf-8') as f:
            for i, field in enumerate(field_names):
                f.write(f"{i+1:2d}. {field}\n")
        
        print(f"\n字段列表已保存到: oracle_table_fields.txt")
        
        cursor.close()
        conn.close()
        
        return field_names
        
    except Exception as e:
        print(f"❌ 检查表结构失败: {e}")
        return None

def main():
    # 数据库连接配置
    db_config = {
        'user': 'manifest_dcb',
        'password': 'manifest_dcb',
        'dsn': '192.168.1.151/TEST'
    }
    
    print("检查Oracle表结构工具")
    check_table_structure(db_config)

if __name__ == '__main__':
    main()