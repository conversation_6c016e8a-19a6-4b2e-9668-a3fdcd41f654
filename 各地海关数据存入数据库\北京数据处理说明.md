# 北京海关数据处理说明

## 概述
本文档说明如何使用Python脚本处理北京海关数据并导入到Oracle数据库中。

## 文件说明
1. `beijing_data_transform_updated.py` - 将北京Excel数据转换为CSV格式
2. `import_beijing_to_oracle.py` - 将CSV数据导入Oracle数据库
3. `北京单月的/` - 包含原始Excel数据文件的文件夹

## 处理步骤

### 1. 数据转换
运行以下命令将北京Excel数据转换为CSV格式：
```bash
python beijing_data_transform_updated.py
```

此脚本会：
- 读取`北京单月的/`文件夹中的所有.xls文件
- 解析不同类型的Excel表格（总值表、国别地区表、贸易方式表、企业性质表、主要商品表等）
- 将数据转换为与Oracle表`T_STATISTICAL_CUS_TOTAL_CS`结构匹配的CSV格式
- 输出文件：`T_STATISTICAL_CUS_TOTAL_BEIJING.csv`

### 2. 数据导入
运行以下命令将CSV数据导入Oracle数据库：
```bash
python import_beijing_to_oracle.py
```

此脚本会：
- 连接到Oracle数据库（配置在脚本中）
- 清空目标表`T_STATISTICAL_CUS_TOTAL_CS`
- 批量插入CSV数据到数据库
- 验证导入结果

## 数据库表结构
目标表：`T_STATISTICAL_CUS_TOTAL_CS`
字段包括：
- STAT_DATE: 统计月份
- STAT_TYPE: 统计类型（01贸易方式, 02企业性质, 03国别地区, 04主出商品, 05主进商品）
- STAT_NAME: 统计类型名称
- STAT_CODE: 统计代码
- STAT_CONTENT_RAW: 统计内容（原始）
- STAT_CONTENT_CLEANSE: 统计内容（清洗后）
- 各类金额字段（ACC_累计, MON_当月, A_总值, E_出口, I_进口）
- 各类同比字段（_YOY）
- 各类数量字段（_AMOUNT）
- DATA_SOURCE: 数据来源（03表示北京）

## 注意事项
1. 确保已安装必要的Python库：
   ```bash
   pip install pandas openpyxl xlrd cx_Oracle
   ```

2. 确保Oracle数据库连接信息正确配置在`import_beijing_to_oracle.py`中

3. 脚本会清空目标表，请确保这不会影响其他数据

4. 数据处理过程中会自动进行单位转换（万元转亿元）

## 故障排除
1. 如果遇到编码问题，请确保文件路径正确
2. 如果数据库连接失败，请检查网络连接和数据库配置
3. 如果数据导入失败，请检查CSV文件格式是否正确