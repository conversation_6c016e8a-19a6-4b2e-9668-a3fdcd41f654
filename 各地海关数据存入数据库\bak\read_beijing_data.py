import pandas as pd
import os

# 设置正确的目录
data_dir = r'C:\Users\<USER>\Desktop\海关数据\小超海关统计\全国进出口\各地海关数据存入数据库\北京单月的'

# 列出目录中的所有文件
files = os.listdir(data_dir)
print("目录中的文件:")
for file in files:
    print(file)

# 尝试读取第一个文件
first_file = "(1)北京地区进出口商品总值表B：月度表（2025年1-5月）.xls"
file_path = os.path.join(data_dir, first_file)

try:
    # 尝试不同的读取方式
    df = pd.read_excel(file_path, engine='xlrd')
    print(f"\n成功读取文件: {first_file}")
    print(f"数据形状: {df.shape}")
    print("\n前10行数据:")
    print(df.head(10))
except Exception as e:
    print(f"使用xlrd读取失败: {e}")
    
    try:
        # 尝试openpyxl
        df = pd.read_excel(file_path, engine='openpyxl')
        print(f"\n成功读取文件: {first_file}")
        print(f"数据形状: {df.shape}")
        print("\n前10行数据:")
        print(df.head(10))
    except Exception as e2:
        print(f"使用openpyxl读取也失败: {e2}")
        
        # 尝试指定编码
        try:
            df = pd.read_excel(file_path, engine='openpyxl', encoding='utf-8')
            print(f"\n使用编码读取成功: {first_file}")
            print(f"数据形状: {df.shape}")
            print("\n前10行数据:")
            print(df.head(10))
        except Exception as e3:
            print(f"指定编码读取也失败: {e3}")