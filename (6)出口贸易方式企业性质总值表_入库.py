import pandas as pd
import os
import re
import cx_Oracle
import numpy as np

def convert_to_float_or_none(value):
    """清理数据，将'-'或空值转为None，其余转为浮点数"""
    if pd.isna(value) or str(value).strip() == '-':
        return None
    try:
        value_str = str(value).replace(',', '')
        return float(value_str)
    except (ValueError, TypeError):
        return None

def parse_table_6(file_path):
    """
    专门为(6)号表格“出口商品贸易方式企业性质总值表”编写的解析器 (适配2021年及以后版本)。
    它处理5列数据格式、合并的列标题和隔行数据，并提取备注。
    """
    try:
        df_raw = pd.read_excel(file_path, header=None, sheet_name=0)
        
        # 1. 动态查找单位
        unit = "不明"
        for i in range(min(5, len(df_raw))):
            row_text = ' '.join(str(s) for s in df_raw.iloc[i].tolist() if pd.notna(s))
            unit_match = re.search(r'单位[：:]\s*([^\s]+)', row_text)
            if unit_match:
                unit = unit_match.group(1).strip()
                break

        # 1. 定位表头
        header_start_row = -1
        trade_mode_col_idx = -1
        for i, row in df_raw.iterrows():
            # "企业性质" 和 "贸易方式" 在一个单元格里，用 "贸易方式" 作为特征
            if row.astype(str).str.contains('贸易方式').any():
                header_start_row = i
                for j, cell_content in enumerate(row):
                    if '贸易方式' in str(cell_content):
                        trade_mode_col_idx = j
                        break
                break
        
        if header_start_row == -1:
            print(f"    [!] 错误: 未能在文件中找到'贸易方式'表头。")
            return None, None
        
        # 2. 动态定位数据起始行 (寻找"总值"或"总计")
        data_start_row = -1
        for i in range(header_start_row + 2, len(df_raw)):
            cell_value = str(df_raw.iloc[i, trade_mode_col_idx])
            # 兼容“总值”和“总 计”等不同写法
            if '总' in cell_value and ('值' in cell_value or '计' in cell_value):
                data_start_row = i
                break
        
        if data_start_row == -1:
            print(f"    [!] 错误: 在文件 '{os.path.basename(file_path)}' 的'贸易方式'表头后未找到'总值'或'总计'数据行。")
            return None, None
        
        data_end_row = len(df_raw)
        remarks = ''
        for i in range(data_start_row, len(df_raw)):
            cell_value = str(df_raw.iloc[i, trade_mode_col_idx])
            if '注：' in cell_value:
                data_end_row = i
                remarks = cell_value.strip()
                break

        # 4. 精确切片数据区域，适配5列数据格式：1(贸易方式) + 5 = 6 列
        end_col = trade_mode_col_idx + 6
        df_sliced = df_raw.iloc[data_start_row:data_end_row, trade_mode_col_idx:end_col].copy()
        df_sliced.reset_index(drop=True, inplace=True)

        # 5. 合并隔行数据
        processed_data = []
        # 每两行（金额行和同比行）合并为一条记录
        for i in range(0, len(df_sliced), 2):
            row_amount = df_sliced.iloc[i]
            if i + 1 >= len(df_sliced): continue
            row_yoy = df_sliced.iloc[i+1]
            
            trade_mode = row_amount.iloc[0]
            if pd.isna(trade_mode):
                continue
            
            # 按位置提取数据，适配5列数据格式
            processed_data.append({
                'TRADE_MODE': trade_mode,
                'TOTAL_AMOUNT': convert_to_float_or_none(row_amount.iloc[1]),
                'TOTAL_YOY': convert_to_float_or_none(row_yoy.iloc[1]),
                'STATE_OWNED_AMOUNT': convert_to_float_or_none(row_amount.iloc[2]),
                'STATE_OWNED_YOY': convert_to_float_or_none(row_yoy.iloc[2]),
                'FOREIGN_INVESTED_AMOUNT': convert_to_float_or_none(row_amount.iloc[3]),
                'FOREIGN_INVESTED_YOY': convert_to_float_or_none(row_yoy.iloc[3]),
                'PRIVATE_AMOUNT': convert_to_float_or_none(row_amount.iloc[4]),
                'PRIVATE_YOY': convert_to_float_or_none(row_yoy.iloc[4]),
                'OTHER_AMOUNT': convert_to_float_or_none(row_amount.iloc[5]),
                'OTHER_YOY': convert_to_float_or_none(row_yoy.iloc[5]),
            })
            
        df_result = pd.DataFrame(processed_data)
        if not df_result.empty:
            df_result['REMARKS'] = remarks
        return df_result, unit
    except Exception as e:
        print(f"    [!] 解析文件 '{os.path.basename(file_path)}' 时出错: {e}")
        return None, None

def check_if_data_exists_6(filename, cursor):
    """根据文件名和贸易方式'总值'检查数据是否已存在"""
    year_month_match = re.search(r'(\d{4})年1至(\d{1,2})月', filename)
    if not year_month_match: return False
    year, month = year_month_match.groups()
    current_month = f"{year}{month.zfill(2)}01"
    currency_type = "人民币" if "人民币" in filename else "美元"
    
    query = "SELECT COUNT(*) FROM temp_cus_mon_6 WHERE CURRENT_MONTH = TO_DATE(:1, 'YYYYMMDD') AND CURRENCY_TYPE = :2 AND TRADE_MODE = '总值'"
    cursor.execute(query, (current_month, currency_type))
    return cursor.fetchone()[0] > 0

def batch_process_directory_6(directory_path, cursor):
    """遍历目录，对2021年及以后版本的新文件执行增量入库"""
    print(f"\n--- 开始扫描目录: {directory_path} ---")
    if not os.path.isdir(directory_path):
        print(f"    [!] 错误: 目录不存在")
        return

    for filename in sorted(os.listdir(directory_path)):
        if not filename.endswith(".xlsx"): continue
        
        # 新增：只处理2021年及以后年份的文件
        year_match = re.search(r'(\d{4})年', filename)
        if year_match:
            year = int(year_match.group(1))
            if year < 2021:
                continue
        else:
            continue # 文件名不规范，跳过

        if check_if_data_exists_6(filename, cursor):
            # print(f"    数据已存在, 跳过: {filename}")
            continue

        file_path = os.path.join(directory_path, filename)
        print(f"    [*] 发现新文件, 正在处理: {filename}")
        
        df, unit_from_file = parse_table_6(file_path)
        
        if df is not None and not df.empty:
            year_month_match = re.search(r'(\d{4})年1至(\d{1,2})月', filename)
            if not year_month_match:
                print(f"    [!] 无法从文件名中解析年月: {filename}")
                continue
            year, month = year_month_match.groups()
            df['CURRENT_MONTH'] = f"{year}{month.zfill(2)}01"
            currency_type = "人民币" if "人民币" in filename else "美元"
            
            # 优先使用动态解析的单位，如果失败则使用默认值
            if unit_from_file and unit_from_file != "不明":
                df['UNIT'] = unit_from_file
            else:
                df['UNIT'] = "万元" if currency_type == "人民币" else "千美元"
            
            # 确保REMARKS列存在，即使没有解析到备注
            if 'REMARKS' not in df.columns:
                df['REMARKS'] = ''

            # 按数据库插入顺序排列列
            column_order = [
                'TRADE_MODE', 'CURRENT_MONTH', 'CURRENCY_TYPE', 'UNIT', 'REMARKS',
                'TOTAL_AMOUNT', 'TOTAL_YOY',
                'STATE_OWNED_AMOUNT', 'STATE_OWNED_YOY',
                'FOREIGN_INVESTED_AMOUNT', 'FOREIGN_INVESTED_YOY',
                'PRIVATE_AMOUNT', 'PRIVATE_YOY',
                'OTHER_AMOUNT', 'OTHER_YOY'
            ]
            df = df[column_order]

            df = df.replace({np.nan: None})
            
            data_to_insert = [tuple(row) for row in df.to_records(index=False)]

            insert_query = """
            INSERT INTO temp_cus_mon_6 (
                TRADE_MODE, CURRENT_MONTH, CURRENCY_TYPE, UNIT, REMARKS,
                TOTAL_AMOUNT, TOTAL_YOY,
                STATE_OWNED_AMOUNT, STATE_OWNED_YOY,
                FOREIGN_INVESTED_AMOUNT, FOREIGN_INVESTED_YOY,
                PRIVATE_AMOUNT, PRIVATE_YOY,
                OTHER_AMOUNT, OTHER_YOY
            ) VALUES (
                :1, TO_DATE(:2, 'YYYYMMDD'), :3, :4, :5, 
                :6, :7, :8, :9, :10, :11, :12, :13, :14, :15
            )
            """
            try:
                cursor.executemany(insert_query, data_to_insert)
                print(f"        -> 成功插入 {cursor.rowcount} 条记录.")
            except cx_Oracle.Error as e:
                print(f"        [!] 数据库插入错误: {e}")
    print(f"--- 目录扫描完成 ---")

if __name__ == "__main__":
    base_dir = os.getcwd()
    # 定义(6)出口 和 (7)进口 的目录
    exp_rmb_dir_6 = os.path.join(base_dir, r"进出口商品统计表_人民币值\(6)出口商品贸易方式企业性质总值表_人民币值")
    exp_usd_dir_6 = os.path.join(base_dir, r"进出口商品统计表_美元值\(6)出口商品贸易方式企业性质总值表_美元值") # 假设存在
    # imp_rmb_dir_7 = os.path.join(base_dir, r"进出口商品统计表_人民币值\(7)进口商品贸易方式企业性质总值表_人民币值") # 假设存在
    # imp_usd_dir_7 = os.path.join(base_dir, r"进出口商品统计表_美元值\(7)进口商品贸易方式企业性质总值表_美元值") # 假设存在
    
    conn = None
    try:
        print("正在连接到Oracle数据库...")
        conn = cx_Oracle.connect("manifest_dcb/manifest_dcb@192.168.1.151/TEST")
        cursor = conn.cursor()
        print("数据库连接成功。")

        # 处理(6)出口表
        batch_process_directory_6(exp_rmb_dir_6, cursor)
        batch_process_directory_6(exp_usd_dir_6, cursor)
        
        # 后续可以添加对(7)进口表的处理
        # batch_process_directory_7(imp_rmb_dir_7, cursor)
        # batch_process_directory_7(imp_usd_dir_7, cursor)
        
        conn.commit()
        print("\n数据提交成功。")
        
    except cx_Oracle.Error as error:
        print(f"\n[!!!] 数据库操作期间发生严重错误: {error}")
    except Exception as e:
        print(f"\n[!!!] 批量处理期间发生未知错误: {e}")
    finally:
        if 'cursor' in locals() and cursor: cursor.close()
        if conn:
            conn.close()
            print("数据库连接已关闭。") 