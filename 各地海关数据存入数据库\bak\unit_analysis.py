"""
浙江省进出口数据Excel文件单位分析脚本
用于分析Excel文件中不同工作表的单位情况
"""

import pandas as pd
import numpy as np
import sys
import io
from pathlib import Path
import re

# 设置输出编码
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

def analyze_units_in_excel(file_path, file_name):
    """分析Excel文件中的单位情况"""
    print(f"\n{'='*80}")
    print(f"分析文件: {file_name}")
    print(f"{'='*80}")
    
    try:
        # 读取Excel文件
        excel_file = pd.ExcelFile(file_path)
        print(f"工作表数量: {len(excel_file.sheet_names)}")
        print(f"工作表名称: {excel_file.sheet_names}")
        
        file_analysis = {}
        
        # 分析每个工作表
        for sheet_name in excel_file.sheet_names:
            print(f"\n{'-'*60}")
            print(f"工作表: {sheet_name}")
            print(f"{'-'*60}")
            
            # 读取工作表
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            
            sheet_analysis = {
                '工作表名称': sheet_name,
                '行数': len(df),
                '列数': len(df.columns),
                '列名': list(df.columns),
                '单位信息': {},
                '金额相关字段': [],
                '数量相关字段': [],
                '单位标记位置': [],
                '单位标记格式': [],
                '单位一致性': True,
                '发现的问题': []
            }
            
            print(f"行数: {sheet_analysis['行数']}")
            print(f"列数: {sheet_analysis['列数']}")
            print(f"列名: {sheet_analysis['列名']}")
            
            # 1. 检查列名中的单位信息
            print("\n1. 检查列名中的单位信息:")
            for col in df.columns:
                col_str = str(col)
                # 查找单位相关的关键词
                units_in_col = []
                if any(unit in col_str for unit in ['万元', '亿元', '元', '美元', '万美元', '亿美元']):
                    units_in_col = [unit for unit in ['万元', '亿元', '元', '美元', '万亿美元', '亿美元'] if unit in col_str]
                    sheet_analysis['单位标记位置'].append(f"列名'{col}'")
                    sheet_analysis['单位标记格式'].append(f"列名中直接包含: {units_in_col}")
                    sheet_analysis['单位信息'][col] = units_in_col
                    
                    # 判断是否为金额相关字段
                    if any(keyword in col_str for keyword in ['进出口', '进口', '出口', '额', '值']):
                        sheet_analysis['金额相关字段'].append(col)
                    else:
                        sheet_analysis['数量相关字段'].append(col)
                        
                print(f"  {col}: {units_in_col if units_in_col else '未发现单位'}")
            
            # 2. 检查前几行数据中的单位信息
            print("\n2. 检查数据中的单位信息:")
            for i in range(min(10, len(df))):  # 检查前10行
                for col in df.columns:
                    cell_value = df.iloc[i, df.columns.get_loc(col)]
                    if pd.notna(cell_value):
                        cell_str = str(cell_value)
                        # 检查是否包含单位信息
                        if any(unit in cell_str for unit in ['万元', '亿元', '元', '美元', '万亿美元', '亿美元']):
                            units_in_cell = [unit for unit in ['万元', '亿元', '元', '美元', '万亿美元', '亿美元'] if unit in cell_str]
                            print(f"  第{i+1}行'{col}': {cell_str}")
                            sheet_analysis['单位标记位置'].append(f"第{i+1}行'{col}'单元格")
                            sheet_analysis['单位标记格式'].append(f"单元格中包含: {units_in_cell}")
                            
                            # 添加到单位信息
                            if col not in sheet_analysis['单位信息']:
                                sheet_analysis['单位信息'][col] = []
                            sheet_analysis['单位信息'][col].extend(units_in_cell)
            
            # 3. 检查数据格式和数值范围
            print("\n3. 检查数据格式和数值范围:")
            numeric_cols = []
            for col in df.columns:
                # 尝试转换为数值
                temp_series = pd.to_numeric(df[col].astype(str).str.replace('*', '').str.strip(), errors='coerce')
                if temp_series.notna().sum() > 0:
                    numeric_cols.append(col)
                    min_val = temp_series.min()
                    max_val = temp_series.max()
                    mean_val = temp_series.mean()
                    print(f"  {col}: 最小值={min_val:.2f}, 最大值={max_val:.2f}, 平均值={mean_val:.2f}")
                    
                    # 根据数值范围推断单位
                    if max_val > 1000000:  # 超过100万，可能是元
                        inferred_unit = "可能为元"
                    elif max_val > 10000:  # 超过1万，可能是万元
                        inferred_unit = "可能为万元"
                    elif max_val > 100:  # 超过100，可能是亿元
                        inferred_unit = "可能为亿元"
                    else:
                        inferred_unit = "单位不明确"
                    
                    print(f"    推断单位: {inferred_unit}")
                    
                    # 检查是否有明显的单位不一致
                    if col in sheet_analysis['单位信息'] and sheet_analysis['单位信息'][col]:
                        explicit_units = sheet_analysis['单位信息'][col]
                        if len(set(explicit_units)) > 1:
                            sheet_analysis['单位一致性'] = False
                            sheet_analysis['发现的问题'].append(f"列'{col}'存在多个单位: {explicit_units}")
            
            # 4. 检查工作表标题或备注中的单位信息
            print("\n4. 检查工作表标题和备注:")
            # 检查第一行是否为标题行
            if len(df) > 0:
                first_row = df.iloc[0]
                for col, value in first_row.items():
                    if pd.notna(value):
                        value_str = str(value)
                        if any(unit in value_str for unit in ['单位', '万元', '亿元', '元', '美元']):
                            print(f"  第一行'{col}': {value_str}")
                            sheet_analysis['单位标记位置'].append(f"第一行'{col}'单元格")
                            sheet_analysis['单位标记格式'].append(f"标题行中包含: {value_str}")
            
            # 5. 分析金额和数量字段
            print("\n5. 金额和数量字段分析:")
            amount_fields = []
            quantity_fields = []
            
            for col in df.columns:
                col_str = str(col)
                # 金额相关字段
                if any(keyword in col_str for keyword in ['进出口', '进口', '出口', '额', '值', '金额']):
                    amount_fields.append(col)
                # 数量相关字段
                elif any(keyword in col_str for keyword in ['数量', '重量', '吨', '个', '件']):
                    quantity_fields.append(col)
            
            sheet_analysis['金额相关字段'] = amount_fields
            sheet_analysis['数量相关字段'] = quantity_fields
            
            print(f"  金额相关字段: {amount_fields}")
            print(f"  数量相关字段: {quantity_fields}")
            
            # 6. 检查单位一致性
            print("\n6. 单位一致性检查:")
            all_units = []
            for units in sheet_analysis['单位信息'].values():
                all_units.extend(units)
            
            if all_units:
                unique_units = list(set(all_units))
                print(f"  发现的单位: {unique_units}")
                if len(unique_units) > 1:
                    sheet_analysis['单位一致性'] = False
                    sheet_analysis['发现的问题'].append(f"工作表中存在多个不同单位: {unique_units}")
                    print(f"  ⚠️  发现多个不同单位，可能存在不一致")
                else:
                    print(f"  ✓ 单位一致: {unique_units[0]}")
            else:
                print(f"  ⚠️  未发现明确的单位标记")
                sheet_analysis['发现的问题'].append("未发现明确的单位标记")
            
            # 7. 显示数据样本
            print("\n7. 数据样本:")
            print(df.head(3).to_string())
            
            file_analysis[sheet_name] = sheet_analysis
        
        return file_analysis
        
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None

def generate_unit_analysis_report(file_analyses):
    """生成单位分析报告"""
    print(f"\n{'='*80}")
    print("单位分析报告")
    print(f"{'='*80}")
    
    report = {
        '文件总数': len(file_analyses),
        '工作表总数': sum(len(analysis.keys()) for analysis in file_analyses.values()),
        '单位使用情况': {},
        '发现的问题': [],
        '建议': []
    }
    
    # 统计单位使用情况
    all_units = []
    all_problems = []
    
    for file_name, file_analysis in file_analyses.items():
        print(f"\n{'-'*60}")
        print(f"文件: {file_name}")
        print(f"{'-'*60}")
        
        for sheet_name, sheet_analysis in file_analysis.items():
            print(f"\n工作表: {sheet_name}")
            
            # 收集所有单位
            for units in sheet_analysis['单位信息'].values():
                all_units.extend(units)
            
            # 收集问题
            all_problems.extend(sheet_analysis['发现的问题'])
            
            # 显示该工作表的单位情况
            if sheet_analysis['单位信息']:
                print(f"  发现的单位信息:")
                for col, units in sheet_analysis['单位信息'].items():
                    print(f"    {col}: {units}")
            else:
                print(f"  未发现明确的单位信息")
            
            # 显示金额和数量字段
            if sheet_analysis['金额相关字段']:
                print(f"  金额相关字段: {sheet_analysis['金额相关字段']}")
            if sheet_analysis['数量相关字段']:
                print(f"  数量相关字段: {sheet_analysis['数量相关字段']}")
            
            # 显示单位标记位置
            if sheet_analysis['单位标记位置']:
                print(f"  单位标记位置: {sheet_analysis['单位标记位置']}")
            
            # 显示问题
            if sheet_analysis['发现的问题']:
                print(f"  发现的问题:")
                for problem in sheet_analysis['发现的问题']:
                    print(f"    ⚠️  {problem}")
    
    # 统计单位使用频率
    if all_units:
        unit_counts = {}
        for unit in all_units:
            unit_counts[unit] = unit_counts.get(unit, 0) + 1
        
        report['单位使用情况'] = unit_counts
        print(f"\n{'-'*60}")
        print("单位使用统计:")
        print(f"{'-'*60}")
        for unit, count in sorted(unit_counts.items(), key=lambda x: x[1], reverse=True):
            print(f"  {unit}: {count}次")
    
    # 统计问题
    if all_problems:
        problem_counts = {}
        for problem in all_problems:
            problem_counts[problem] = problem_counts.get(problem, 0) + 1
        
        report['发现的问题'] = problem_counts
        print(f"\n{'-'*60}")
        print("问题统计:")
        print(f"{'-'*60}")
        for problem, count in sorted(problem_counts.items(), key=lambda x: x[1], reverse=True):
            print(f"  {problem}: {count}次")
    
    # 生成建议
    print(f"\n{'-'*60}")
    print("建议:")
    print(f"{'-'*60}")
    
    if not all_units:
        report['建议'].append("建议在所有金额和数量字段中明确标注单位")
        print("  1. 建议在所有金额和数量字段中明确标注单位")
    
    if len(set(all_units)) > 1:
        report['建议'].append("建议统一单位标准，避免混用不同单位")
        print("  2. 建议统一单位标准，避免混用不同单位")
    
    if any('未发现明确的单位标记' in str(problem) for problem in all_problems):
        report['建议'].append("建议在列名或标题行中添加单位信息")
        print("  3. 建议在列名或标题行中添加单位信息")
    
    report['建议'].append("建议建立数据质量检查机制，确保单位一致性")
    print("  4. 建议建立数据质量检查机制，确保单位一致性")
    
    return report

def main():
    """主函数"""
    print("浙江省进出口数据Excel文件单位分析")
    print("=" * 80)
    
    # 指定的文件路径
    files_to_analyze = [
        (
            r"C:\Users\<USER>\Desktop\海关数据\小超海关统计\全国进出口\各地海关数据存入数据库\录入的\2024年1月浙江省进出口情况.xlsx",
            "2024年1月浙江省进出口情况.xlsx"
        ),
        (
            r"C:\Users\<USER>\Desktop\海关数据\小超海关统计\全国进出口\各地海关数据存入数据库\录入的\2024年1-10月浙江省进出口情况.xlsx",
            "2024年1-10月浙江省进出口情况.xlsx"
        ),
        (
            r"C:\Users\<USER>\Desktop\海关数据\小超海关统计\全国进出口\各地海关数据存入数据库\录入的\2025年1月浙江省进出口情况.xlsx",
            "2025年1月浙江省进出口情况.xlsx"
        )
    ]
    
    file_analyses = {}
    
    # 分析每个文件
    for file_path, file_name in files_to_analyze:
        file_analysis = analyze_units_in_excel(file_path, file_name)
        if file_analysis:
            file_analyses[file_name] = file_analysis
    
    # 生成综合报告
    if file_analyses:
        report = generate_unit_analysis_report(file_analyses)
        
        print(f"\n{'='*80}")
        print("分析完成")
        print(f"{'='*80}")
        print(f"分析了 {report['文件总数']} 个文件，共 {report['工作表总数']} 个工作表")
        print(f"发现 {len(report['发现的问题'])} 类问题")
        print(f"提出 {len(report['建议'])} 条建议")
    else:
        print("分析失败，请检查文件路径和格式")

if __name__ == "__main__":
    main()