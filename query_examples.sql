-- =====================================================
-- 海关统计数据常用查询示例
-- 为数据人员提供常用的查询模板
-- =====================================================

-- 1. 基础查询示例
-- =====================================================

-- 1.1 查询最新月份的总值统计
SELECT * FROM V_CUS_TOTAL_STATISTICS 
WHERE STAT_DATE = (SELECT MAX(STAT_DATE) FROM V_CUS_TOTAL_STATISTICS)
ORDER BY CURRENCY_TYPE;

-- 1.2 查询指定时间段的进出口总额
SELECT 
    STAT_DATE,
    CURRENCY_TYPE,
    当月进出口金额,
    累计进出口金额,
    当月进出口同比,
    累计进出口同比
FROM V_CUS_TOTAL_STATISTICS
WHERE STAT_DATE BETWEEN DATE '2024-01-01' AND DATE '2024-12-31'
ORDER BY STAT_DATE, CURRENCY_TYPE;

-- 1.3 查询主要贸易伙伴（按累计进出口金额排序）
SELECT 
    国家地区,
    CURRENCY_TYPE,
    累计进出口金额,
    累计出口金额,
    累计进口金额,
    累计进出口同比
FROM V_CUS_COUNTRY_STATISTICS
WHERE STAT_DATE = (SELECT MAX(STAT_DATE) FROM V_CUS_COUNTRY_STATISTICS)
  AND CURRENCY_TYPE = '人民币'
  AND 累计进出口金额 IS NOT NULL
ORDER BY 累计进出口金额 DESC
FETCH FIRST 20 ROWS ONLY;

-- 2. 趋势分析查询
-- =====================================================

-- 2.1 月度进出口趋势分析
SELECT 
    年月,
    CURRENCY_TYPE,
    当月进出口总额,
    当月出口总额,
    当月进口总额,
    平均进出口同比,
    LAG(当月进出口总额) OVER (PARTITION BY CURRENCY_TYPE ORDER BY 年月) as 上月进出口总额,
    ROUND((当月进出口总额 - LAG(当月进出口总额) OVER (PARTITION BY CURRENCY_TYPE ORDER BY 年月)) / 
          LAG(当月进出口总额) OVER (PARTITION BY CURRENCY_TYPE ORDER BY 年月) * 100, 2) as 环比增长率
FROM V_CUS_MONTHLY_TREND
WHERE STAT_TYPE = '01'  -- 总值统计
  AND 年份 >= '2023'
ORDER BY 年月 DESC, CURRENCY_TYPE;

-- 2.2 年度对比分析
SELECT 
    年份,
    CURRENCY_TYPE,
    SUM(当月进出口总额) as 年度进出口总额,
    SUM(当月出口总额) as 年度出口总额,
    SUM(当月进口总额) as 年度进口总额,
    ROUND(AVG(平均进出口同比), 2) as 平均同比增长率
FROM V_CUS_MONTHLY_TREND
WHERE STAT_TYPE = '01'
GROUP BY 年份, CURRENCY_TYPE
ORDER BY 年份 DESC, CURRENCY_TYPE;

-- 3. 排名分析查询
-- =====================================================

-- 3.1 主要贸易方式排名
SELECT 
    ROWNUM as 排名,
    贸易方式,
    累计进出口金额,
    累计进出口同比,
    ROUND(累计进出口金额 / SUM(累计进出口金额) OVER() * 100, 2) as 占比百分比
FROM (
    SELECT 
        贸易方式,
        累计进出口金额,
        累计进出口同比
    FROM V_CUS_TRADE_MODE_STATISTICS
    WHERE STAT_DATE = (SELECT MAX(STAT_DATE) FROM V_CUS_TRADE_MODE_STATISTICS)
      AND CURRENCY_TYPE = '人民币'
      AND 累计进出口金额 IS NOT NULL
    ORDER BY 累计进出口金额 DESC
)
WHERE ROWNUM <= 15;

-- 3.2 主要出口商品排名
SELECT 
    ROWNUM as 排名,
    商品名称,
    累计金额,
    累计数量,
    数量单位,
    累计金额同比,
    累计数量同比
FROM (
    SELECT 
        商品名称,
        累计金额,
        累计数量,
        数量单位,
        累计金额同比,
        累计数量同比
    FROM V_CUS_MAJOR_COMMODITY_STATISTICS
    WHERE STAT_DATE = (SELECT MAX(STAT_DATE) FROM V_CUS_MAJOR_COMMODITY_STATISTICS)
      AND 贸易方向 = '出口'
      AND CURRENCY_TYPE = '人民币'
      AND 累计金额 IS NOT NULL
    ORDER BY 累计金额 DESC
)
WHERE ROWNUM <= 20;

-- 4. 交叉分析查询
-- =====================================================

-- 4.1 主要贸易伙伴的主要商品分析
SELECT 
    国家,
    商品描述,
    贸易方向,
    累计金额,
    RANK() OVER (PARTITION BY 国家, 贸易方向 ORDER BY 累计金额 DESC) as 商品排名
FROM V_CUS_COUNTRY_COMMODITY_CROSS
WHERE STAT_DATE = (SELECT MAX(STAT_DATE) FROM V_CUS_COUNTRY_COMMODITY_CROSS)
  AND CURRENCY_TYPE = '人民币'
  AND 国家 IN ('美国', '欧盟', '东盟', '日本', '韩国')
  AND 累计金额 IS NOT NULL
ORDER BY 国家, 贸易方向, 商品排名;

-- 4.2 贸易差额分析
SELECT 
    STAT_DATE,
    CURRENCY_TYPE,
    当月出口金额,
    当月进口金额,
    当月贸易差额,
    累计出口金额,
    累计进口金额,
    累计贸易差额,
    CASE 
        WHEN 当月贸易差额 > 0 THEN '贸易顺差'
        WHEN 当月贸易差额 < 0 THEN '贸易逆差'
        ELSE '贸易平衡'
    END as 贸易状况
FROM V_CUS_TOTAL_STATISTICS
WHERE STAT_DATE >= ADD_MONTHS(SYSDATE, -12)
ORDER BY STAT_DATE DESC, CURRENCY_TYPE;

-- 5. 数据质量检查查询
-- =====================================================

-- 5.1 检查数据完整性
SELECT 
    STAT_TYPE,
    STAT_TYPE_NAME,
    COUNT(*) as 总记录数,
    COUNT(STAT_DATE) as 有日期记录数,
    COUNT(CURRENCY_TYPE) as 有货币类型记录数,
    COUNT(MONTH_IE_AMOUNT) as 有当月进出口金额记录数,
    COUNT(YTD_IE_AMOUNT) as 有累计进出口金额记录数,
    ROUND(COUNT(MONTH_IE_AMOUNT) / COUNT(*) * 100, 2) as 当月金额完整度百分比
FROM CUS_TRADE_UNIFIED_STATISTICS
GROUP BY STAT_TYPE, STAT_TYPE_NAME
ORDER BY STAT_TYPE;

-- 5.2 检查异常数据
SELECT 
    STAT_TYPE,
    STAT_TYPE_NAME,
    STAT_DATE,
    CURRENCY_TYPE,
    DIMENSION_1,
    MONTH_IE_AMOUNT,
    YTD_IE_AMOUNT,
    MONTH_IE_YOY,
    '金额异常' as 异常类型
FROM CUS_TRADE_UNIFIED_STATISTICS
WHERE (MONTH_IE_AMOUNT < 0 OR YTD_IE_AMOUNT < 0)
   OR (ABS(MONTH_IE_YOY) > 1000)  -- 同比增长率异常
ORDER BY STAT_DATE DESC;

-- 6. 业务分析查询
-- =====================================================

-- 6.1 季度分析
SELECT 
    TO_CHAR(STAT_DATE, 'YYYY') as 年份,
    CASE 
        WHEN TO_CHAR(STAT_DATE, 'MM') IN ('01','02','03') THEN 'Q1'
        WHEN TO_CHAR(STAT_DATE, 'MM') IN ('04','05','06') THEN 'Q2'
        WHEN TO_CHAR(STAT_DATE, 'MM') IN ('07','08','09') THEN 'Q3'
        WHEN TO_CHAR(STAT_DATE, 'MM') IN ('10','11','12') THEN 'Q4'
    END as 季度,
    CURRENCY_TYPE,
    SUM(MONTH_IE_AMOUNT) as 季度进出口总额,
    SUM(MONTH_EXP_AMOUNT) as 季度出口总额,
    SUM(MONTH_IMP_AMOUNT) as 季度进口总额,
    AVG(MONTH_IE_YOY) as 平均同比增长率
FROM CUS_TRADE_UNIFIED_STATISTICS
WHERE STAT_TYPE = '01'
  AND STAT_DATE >= DATE '2023-01-01'
GROUP BY TO_CHAR(STAT_DATE, 'YYYY'), 
         CASE 
             WHEN TO_CHAR(STAT_DATE, 'MM') IN ('01','02','03') THEN 'Q1'
             WHEN TO_CHAR(STAT_DATE, 'MM') IN ('04','05','06') THEN 'Q2'
             WHEN TO_CHAR(STAT_DATE, 'MM') IN ('07','08','09') THEN 'Q3'
             WHEN TO_CHAR(STAT_DATE, 'MM') IN ('10','11','12') THEN 'Q4'
         END,
         CURRENCY_TYPE
ORDER BY 年份 DESC, 季度 DESC, CURRENCY_TYPE;

-- 6.2 同比增长率分析
SELECT 
    STAT_TYPE_NAME,
    DIMENSION_1,
    CURRENCY_TYPE,
    累计进出口金额,
    累计进出口同比,
    CASE 
        WHEN 累计进出口同比 > 20 THEN '高速增长'
        WHEN 累计进出口同比 > 10 THEN '快速增长'
        WHEN 累计进出口同比 > 0 THEN '正增长'
        WHEN 累计进出口同比 > -10 THEN '轻微下降'
        ELSE '大幅下降'
    END as 增长状况
FROM (
    SELECT 
        STAT_TYPE_NAME,
        DIMENSION_1,
        CURRENCY_TYPE,
        YTD_IE_AMOUNT as 累计进出口金额,
        YTD_IE_YOY as 累计进出口同比
    FROM CUS_TRADE_UNIFIED_STATISTICS
    WHERE STAT_DATE = (SELECT MAX(STAT_DATE) FROM CUS_TRADE_UNIFIED_STATISTICS)
      AND STAT_TYPE IN ('02', '05')  -- 国别地区和贸易方式
      AND YTD_IE_AMOUNT IS NOT NULL
      AND YTD_IE_YOY IS NOT NULL
)
ORDER BY 累计进出口同比 DESC;

-- 显示查询示例说明
SELECT '查询示例创建完成！包含以下类型的查询：' as 说明 FROM DUAL
UNION ALL SELECT '1. 基础查询 - 总值、国别、时间段查询' FROM DUAL
UNION ALL SELECT '2. 趋势分析 - 月度趋势、年度对比' FROM DUAL
UNION ALL SELECT '3. 排名分析 - 贸易方式、商品排名' FROM DUAL
UNION ALL SELECT '4. 交叉分析 - 国家商品、贸易差额' FROM DUAL
UNION ALL SELECT '5. 数据质量 - 完整性、异常检查' FROM DUAL
UNION ALL SELECT '6. 业务分析 - 季度分析、增长分析' FROM DUAL;
