#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
海关统计数据迁移脚本
将现有18张分表数据迁移到统一大表 CUS_TRADE_UNIFIED_STATISTICS
"""

import cx_Oracle
import pandas as pd
import logging
from datetime import datetime
import sys
import os

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'migration_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class CustomsDataMigrator:
    def __init__(self, connection_string):
        """初始化迁移器"""
        self.connection_string = connection_string
        self.conn = None
        self.batch_size = 1000
        self.total_migrated = 0
        
    def connect(self):
        """建立数据库连接"""
        try:
            self.conn = cx_Oracle.connect(self.connection_string)
            logger.info("数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            return False
    
    def close(self):
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()
            logger.info("数据库连接已关闭")
    
    def migrate_table_01(self):
        """迁移表(1) - 进出口商品总值表"""
        logger.info("开始迁移表(1) - 进出口商品总值表")
        
        source_sql = """
        SELECT 
            CURRENT_MONTH as STAT_DATE,
            CURRENCY_TYPE,
            UNIT,
            VAL_MONTH_IE as MONTH_IE_AMOUNT,
            VAL_MONTH_EXP as MONTH_EXP_AMOUNT,
            VAL_MONTH_IMP as MONTH_IMP_AMOUNT,
            VAL_MONTH_TB as MONTH_TB_AMOUNT,
            VAL_YTD_IE as YTD_IE_AMOUNT,
            VAL_YTD_EXP as YTD_EXP_AMOUNT,
            VAL_YTD_IMP as YTD_IMP_AMOUNT,
            VAL_YTD_TB as YTD_TB_AMOUNT,
            YOY_MONTH_IE as MONTH_IE_YOY,
            YOY_MONTH_EXP as MONTH_EXP_YOY,
            YOY_MONTH_IMP as MONTH_IMP_YOY,
            YOY_MONTH_TB as MONTH_TB_YOY,
            YOY_YTD_IE as YTD_IE_YOY,
            YOY_YTD_EXP as YTD_EXP_YOY,
            YOY_YTD_IMP as YTD_IMP_YOY,
            YOY_YTD_TB as YTD_TB_YOY,
            MOM_MONTH_IE as MONTH_IE_MOM,
            MOM_MONTH_EXP as MONTH_EXP_MOM,
            MOM_MONTH_IMP as MONTH_IMP_MOM,
            MOM_MONTH_TB as MONTH_TB_MOM
        FROM temp_cus_mon_1
        """
        
        return self._migrate_data(source_sql, '01', '总值统计', 'temp_cus_mon_1')
    
    def migrate_table_02(self):
        """迁移表(2) - 国别地区总值表"""
        logger.info("开始迁移表(2) - 国别地区总值表")
        
        source_sql = """
        SELECT 
            CURRENT_MONTH as STAT_DATE,
            CURRENCY_TYPE,
            UNIT,
            LOCATION as DIMENSION_1,
            MONTH_IMPORT_EXPORT as MONTH_IE_AMOUNT,
            MONTH_EXPORT as MONTH_EXP_AMOUNT,
            MONTH_IMPORT as MONTH_IMP_AMOUNT,
            YTD_IMPORT_EXPORT as YTD_IE_AMOUNT,
            YTD_EXPORT as YTD_EXP_AMOUNT,
            YTD_IMPORT as YTD_IMP_AMOUNT,
            YOY_IMPORT_EXPORT as YTD_IE_YOY,
            YOY_EXPORT as YTD_EXP_YOY,
            YOY_IMPORT as YTD_IMP_YOY
        FROM temp_cus_mon_2
        """
        
        return self._migrate_data(source_sql, '02', '国别地区', 'temp_cus_mon_2')
    
    def migrate_table_05(self):
        """迁移表(5) - 贸易方式总值表"""
        logger.info("开始迁移表(5) - 贸易方式总值表")
        
        source_sql = """
        SELECT 
            TO_DATE(CURRENT_MONTH, 'YYYYMMDD') as STAT_DATE,
            CURRENCY_TYPE,
            UNIT,
            TRADE_MODE as DIMENSION_1,
            MONTH_IE_AMOUNT,
            MONTH_EXP_AMOUNT,
            MONTH_IMP_AMOUNT,
            YTD_IE_AMOUNT,
            YTD_EXP_AMOUNT,
            YTD_IMP_AMOUNT,
            MONTH_IE_YOY,
            MONTH_EXP_YOY,
            MONTH_IMP_YOY,
            YTD_IE_YOY,
            YTD_EXP_YOY,
            YTD_IMP_YOY
        FROM temp_cus_mon_5
        """
        
        return self._migrate_data(source_sql, '05', '贸易方式', 'temp_cus_mon_5')
    
    def migrate_table_13_14(self):
        """迁移表(13)(14) - 主要商品量值表"""
        logger.info("开始迁移表(13)(14) - 主要商品量值表")
        
        source_sql = """
        SELECT 
            CURRENT_MONTH as STAT_DATE,
            CURRENCY_TYPE,
            UNIT,
            COMMODITY_NAME as DIMENSION_1,
            TRADE_DIRECTION,
            MONTH_AMOUNT as MONTH_IE_AMOUNT,
            YTD_AMOUNT as YTD_IE_AMOUNT,
            MONTH_QUANTITY,
            YTD_QUANTITY,
            MONTH_AMOUNT_YOY as MONTH_IE_YOY,
            YTD_AMOUNT_YOY as YTD_IE_YOY,
            MONTH_QUANTITY_YOY,
            YTD_QUANTITY_YOY,
            YOY_MONTH_EXPORT_WEIGHT as MONTH_WEIGHT_YOY,
            YOY_YTD_EXPORT_WEIGHT as YTD_WEIGHT_YOY,
            YOY_MONTH_IMPORT_WEIGHT as MONTH_WEIGHT_YOY,
            YOY_YTD_IMPORT_WEIGHT as YTD_WEIGHT_YOY
        FROM CUS_TRADE_MAJOR_PRODUCT_MON
        """
        
        return self._migrate_data(source_sql, '12', '主要商品量值', 'CUS_TRADE_MAJOR_PRODUCT_MON')
    
    def migrate_table_15_16(self):
        """迁移表(15)(16) - 国家×商品表"""
        logger.info("开始迁移表(15)(16) - 国家×商品表")
        
        source_sql = """
        SELECT 
            CURRENT_MONTH as STAT_DATE,
            CURRENCY_TYPE,
            UNIT,
            COUNTRY as DIMENSION_1,
            HS_CODE_DESC as DIMENSION_2,
            TRADE_DIRECTION,
            VALUE_MONTH as MONTH_IE_AMOUNT,
            VALUE_YTD as YTD_IE_AMOUNT
        FROM CUS_TRADE_COUNTRY_PRODUCT_MON
        """
        
        return self._migrate_data(source_sql, '13', '国家×商品', 'CUS_TRADE_COUNTRY_PRODUCT_MON')

    def migrate_other_tables(self):
        """迁移其他表 - 通用方法"""
        logger.info("开始迁移其他表")

        # 定义其他表的迁移配置
        other_tables = [
            {
                'table': 'temp_cus_mon_3',
                'stat_type': '03',
                'stat_type_name': '商品构成',
                'sql': """
                SELECT
                    CURRENT_MONTH as STAT_DATE,
                    CURRENCY_TYPE,
                    UNIT,
                    COMPOSITION as DIMENSION_1,
                    MONTH_EXPORT as MONTH_EXP_AMOUNT,
                    MONTH_IMPORT as MONTH_IMP_AMOUNT,
                    YTD_EXPORT as YTD_EXP_AMOUNT,
                    YTD_IMPORT as YTD_IMP_AMOUNT,
                    YOY_EXPORT as YTD_EXP_YOY,
                    YOY_IMPORT as YTD_IMP_YOY
                FROM temp_cus_mon_3
                """
            },
            {
                'table': 'temp_cus_mon_4',
                'stat_type': '04',
                'stat_type_name': '商品类章',
                'sql': """
                SELECT
                    CURRENT_MONTH as STAT_DATE,
                    CURRENCY_TYPE,
                    UNIT,
                    CATEGORY_CHAPTER as DIMENSION_1,
                    MONTH_EXPORT as MONTH_EXP_AMOUNT,
                    MONTH_IMPORT as MONTH_IMP_AMOUNT,
                    YTD_EXPORT as YTD_EXP_AMOUNT,
                    YTD_IMPORT as YTD_IMP_AMOUNT,
                    YOY_EXPORT as YTD_EXP_YOY,
                    YOY_IMPORT as YTD_IMP_YOY
                FROM temp_cus_mon_4
                """
            }
        ]

        total_migrated = 0
        for table_config in other_tables:
            try:
                count = self._migrate_data(
                    table_config['sql'],
                    table_config['stat_type'],
                    table_config['stat_type_name'],
                    table_config['table']
                )
                total_migrated += count
            except Exception as e:
                logger.error(f"迁移表 {table_config['table']} 失败: {e}")
                continue

        return total_migrated

    def _migrate_data(self, source_sql, stat_type, stat_type_name, source_table):
        """通用数据迁移方法"""
        try:
            cursor = self.conn.cursor()
            
            # 查询源数据
            cursor.execute(source_sql)
            columns = [desc[0] for desc in cursor.description]
            
            migrated_count = 0
            batch_data = []
            
            while True:
                rows = cursor.fetchmany(self.batch_size)
                if not rows:
                    break
                
                for row in rows:
                    # 构建插入数据
                    data_dict = dict(zip(columns, row))
                    
                    # 添加统一字段
                    data_dict['STAT_TYPE'] = stat_type
                    data_dict['STAT_TYPE_NAME'] = stat_type_name
                    data_dict['SOURCE_TABLE'] = source_table
                    data_dict['DATA_SOURCE'] = 'MIGRATION'
                    data_dict['CREATE_TIME'] = datetime.now()
                    data_dict['UPDATE_TIME'] = datetime.now()
                    
                    batch_data.append(data_dict)
                
                # 批量插入
                if batch_data:
                    self._batch_insert(batch_data)
                    migrated_count += len(batch_data)
                    batch_data = []
                    
                    if migrated_count % 10000 == 0:
                        logger.info(f"已迁移 {migrated_count} 条记录")
            
            # 处理剩余数据
            if batch_data:
                self._batch_insert(batch_data)
                migrated_count += len(batch_data)
            
            cursor.close()
            self.total_migrated += migrated_count
            logger.info(f"表 {source_table} 迁移完成，共 {migrated_count} 条记录")
            return migrated_count
            
        except Exception as e:
            logger.error(f"迁移表 {source_table} 时发生错误: {e}")
            return 0
    
    def _batch_insert(self, batch_data):
        """批量插入数据到统一表"""
        if not batch_data:
            return
        
        # 构建插入SQL
        insert_sql = """
        INSERT INTO CUS_TRADE_UNIFIED_STATISTICS (
            STAT_DATE, STAT_TYPE, STAT_TYPE_NAME, CURRENCY_TYPE, UNIT,
            DIMENSION_1, DIMENSION_2, DIMENSION_3, TRADE_DIRECTION,
            MONTH_IE_AMOUNT, MONTH_EXP_AMOUNT, MONTH_IMP_AMOUNT, MONTH_TB_AMOUNT,
            YTD_IE_AMOUNT, YTD_EXP_AMOUNT, YTD_IMP_AMOUNT, YTD_TB_AMOUNT,
            MONTH_IE_YOY, MONTH_EXP_YOY, MONTH_IMP_YOY, MONTH_TB_YOY,
            YTD_IE_YOY, YTD_EXP_YOY, YTD_IMP_YOY, YTD_TB_YOY,
            MONTH_IE_MOM, MONTH_EXP_MOM, MONTH_IMP_MOM, MONTH_TB_MOM,
            MONTH_QUANTITY, YTD_QUANTITY, MONTH_QUANTITY_YOY, YTD_QUANTITY_YOY,
            MONTH_WEIGHT_YOY, YTD_WEIGHT_YOY,
            SOURCE_TABLE, DATA_SOURCE, CREATE_TIME, UPDATE_TIME
        ) VALUES (
            :STAT_DATE, :STAT_TYPE, :STAT_TYPE_NAME, :CURRENCY_TYPE, :UNIT,
            :DIMENSION_1, :DIMENSION_2, :DIMENSION_3, :TRADE_DIRECTION,
            :MONTH_IE_AMOUNT, :MONTH_EXP_AMOUNT, :MONTH_IMP_AMOUNT, :MONTH_TB_AMOUNT,
            :YTD_IE_AMOUNT, :YTD_EXP_AMOUNT, :YTD_IMP_AMOUNT, :YTD_TB_AMOUNT,
            :MONTH_IE_YOY, :MONTH_EXP_YOY, :MONTH_IMP_YOY, :MONTH_TB_YOY,
            :YTD_IE_YOY, :YTD_EXP_YOY, :YTD_IMP_YOY, :YTD_TB_YOY,
            :MONTH_IE_MOM, :MONTH_EXP_MOM, :MONTH_IMP_MOM, :MONTH_TB_MOM,
            :MONTH_QUANTITY, :YTD_QUANTITY, :MONTH_QUANTITY_YOY, :YTD_QUANTITY_YOY,
            :MONTH_WEIGHT_YOY, :YTD_WEIGHT_YOY,
            :SOURCE_TABLE, :DATA_SOURCE, :CREATE_TIME, :UPDATE_TIME
        )
        """
        
        cursor = self.conn.cursor()
        try:
            cursor.executemany(insert_sql, batch_data)
            self.conn.commit()
        except Exception as e:
            logger.error(f"批量插入数据时发生错误: {e}")
            self.conn.rollback()
            raise
        finally:
            cursor.close()
    
    def run_migration(self):
        """执行完整迁移"""
        logger.info("开始海关统计数据迁移")
        
        if not self.connect():
            return False
        
        try:
            # 清空目标表
            cursor = self.conn.cursor()
            cursor.execute("TRUNCATE TABLE CUS_TRADE_UNIFIED_STATISTICS")
            self.conn.commit()
            cursor.close()
            logger.info("目标表已清空")
            
            # 执行各表迁移
            migration_tasks = [
                self.migrate_table_01,
                self.migrate_table_02,
                self.migrate_table_05,
                self.migrate_table_13_14,
                self.migrate_table_15_16,
                self.migrate_other_tables,
            ]
            
            for task in migration_tasks:
                try:
                    task()
                except Exception as e:
                    logger.error(f"迁移任务失败: {e}")
                    continue
            
            logger.info(f"迁移完成，总计迁移 {self.total_migrated} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"迁移过程中发生错误: {e}")
            return False
        finally:
            self.close()

def main():
    """主函数"""
    # 数据库连接字符串
    connection_string = "manifest_dcb/manifest_dcb@192.168.1.151/TEST"
    
    # 创建迁移器并执行迁移
    migrator = CustomsDataMigrator(connection_string)
    success = migrator.run_migration()
    
    if success:
        logger.info("数据迁移成功完成")
        sys.exit(0)
    else:
        logger.error("数据迁移失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
