# 海关统计数据统一大表性能优化建议

## 1. 索引策略

### 1.1 主要索引
已在DDL中创建的核心索引：
- `IDX_CUS_UNIFIED_DATE_TYPE`: (STAT_DATE, STAT_TYPE) - 支持按日期和类型查询
- `IDX_CUS_UNIFIED_CURRENCY`: (CURRENCY_TYPE, STAT_DATE) - 支持按货币类型查询
- `IDX_CUS_UNIFIED_DIM1`: (DIMENSION_1, STAT_DATE) - 支持按主维度查询
- `IDX_CUS_UNIFIED_MAIN`: (STAT_TYPE, STAT_DATE, CURRENCY_TYPE, DIMENSION_1) - 复合查询索引

### 1.2 建议增加的索引
```sql
-- 按金额范围查询的索引
CREATE INDEX IDX_CUS_UNIFIED_AMOUNT ON CUS_TRADE_UNIFIED_STATISTICS(YTD_IE_AMOUNT, STAT_DATE);

-- 按同比增长率查询的索引  
CREATE INDEX IDX_CUS_UNIFIED_YOY ON CUS_TRADE_UNIFIED_STATISTICS(YTD_IE_YOY, STAT_TYPE);

-- 支持贸易方向查询的索引
CREATE INDEX IDX_CUS_UNIFIED_TRADE_DIR_DIM ON CUS_TRADE_UNIFIED_STATISTICS(TRADE_DIRECTION, DIMENSION_1, STAT_DATE);

-- 函数索引：支持按年月查询
CREATE INDEX IDX_CUS_UNIFIED_YEAR_MONTH ON CUS_TRADE_UNIFIED_STATISTICS(
    TO_CHAR(STAT_DATE, 'YYYY-MM'), STAT_TYPE
);
```

### 1.3 索引维护策略
- 定期重建索引：`ALTER INDEX index_name REBUILD;`
- 监控索引使用情况：查询 `V$SQL_PLAN` 视图
- 删除未使用的索引以减少维护开销

## 2. 分区策略

### 2.1 推荐分区方案
```sql
-- 按月分区的表结构（适用于大数据量场景）
CREATE TABLE CUS_TRADE_UNIFIED_STATISTICS_PART (
    -- 字段定义同原表
    ID NUMBER(20) GENERATED BY DEFAULT AS IDENTITY,
    STAT_DATE DATE NOT NULL,
    -- ... 其他字段
    CONSTRAINT PK_CUS_UNIFIED_PART PRIMARY KEY (ID, STAT_DATE)
)
PARTITION BY RANGE (STAT_DATE) (
    PARTITION P_2023_01 VALUES LESS THAN (DATE '2023-02-01'),
    PARTITION P_2023_02 VALUES LESS THAN (DATE '2023-03-01'),
    -- ... 继续按月分区
    PARTITION P_2024_12 VALUES LESS THAN (DATE '2025-01-01'),
    PARTITION P_2025_01 VALUES LESS THAN (DATE '2025-02-01'),
    PARTITION P_FUTURE VALUES LESS THAN (MAXVALUE)
);
```

### 2.2 分区维护
```sql
-- 自动添加新分区
ALTER TABLE CUS_TRADE_UNIFIED_STATISTICS_PART 
ADD PARTITION P_2025_02 VALUES LESS THAN (DATE '2025-03-01');

-- 删除历史分区（保留3年数据）
ALTER TABLE CUS_TRADE_UNIFIED_STATISTICS_PART 
DROP PARTITION P_2022_01;
```

### 2.3 子分区策略（可选）
```sql
-- 按日期范围分区，按统计类型子分区
PARTITION BY RANGE (STAT_DATE)
SUBPARTITION BY LIST (STAT_TYPE) (
    PARTITION P_2024 VALUES LESS THAN (DATE '2025-01-01') (
        SUBPARTITION P_2024_TOTAL VALUES ('01'),
        SUBPARTITION P_2024_COUNTRY VALUES ('02'),
        SUBPARTITION P_2024_TRADE_MODE VALUES ('05'),
        SUBPARTITION P_2024_OTHER VALUES (DEFAULT)
    )
);
```

## 3. 查询优化

### 3.1 SQL优化原则
1. **使用绑定变量**：避免硬编码，提高SQL重用率
2. **合理使用索引提示**：在必要时使用 `/*+ INDEX */` 提示
3. **避免全表扫描**：确保WHERE条件能使用索引
4. **优化JOIN操作**：使用合适的连接方式

### 3.2 常见查询优化示例
```sql
-- 优化前：可能导致全表扫描
SELECT * FROM CUS_TRADE_UNIFIED_STATISTICS 
WHERE TO_CHAR(STAT_DATE, 'YYYY') = '2024';

-- 优化后：使用日期范围查询
SELECT * FROM CUS_TRADE_UNIFIED_STATISTICS 
WHERE STAT_DATE >= DATE '2024-01-01' 
  AND STAT_DATE < DATE '2025-01-01';

-- 使用索引提示（必要时）
SELECT /*+ INDEX(CUS_TRADE_UNIFIED_STATISTICS IDX_CUS_UNIFIED_MAIN) */
       * FROM CUS_TRADE_UNIFIED_STATISTICS 
WHERE STAT_TYPE = '02' AND STAT_DATE >= DATE '2024-01-01';
```

### 3.3 分页查询优化
```sql
-- 使用 ROWNUM 进行分页（Oracle 11g及以下）
SELECT * FROM (
    SELECT ROWNUM rn, t.* FROM (
        SELECT * FROM CUS_TRADE_UNIFIED_STATISTICS 
        WHERE STAT_TYPE = '02' 
        ORDER BY STAT_DATE DESC
    ) t WHERE ROWNUM <= 100
) WHERE rn > 50;

-- 使用 OFFSET/FETCH 进行分页（Oracle 12c及以上）
SELECT * FROM CUS_TRADE_UNIFIED_STATISTICS 
WHERE STAT_TYPE = '02' 
ORDER BY STAT_DATE DESC
OFFSET 50 ROWS FETCH NEXT 50 ROWS ONLY;
```

## 4. 统计信息管理

### 4.1 收集统计信息
```sql
-- 收集表统计信息
EXEC DBMS_STATS.GATHER_TABLE_STATS('SCHEMA_NAME', 'CUS_TRADE_UNIFIED_STATISTICS');

-- 收集索引统计信息
EXEC DBMS_STATS.GATHER_INDEX_STATS('SCHEMA_NAME', 'IDX_CUS_UNIFIED_MAIN');

-- 自动收集统计信息（推荐）
EXEC DBMS_STATS.GATHER_TABLE_STATS(
    ownname => 'SCHEMA_NAME',
    tabname => 'CUS_TRADE_UNIFIED_STATISTICS',
    estimate_percent => DBMS_STATS.AUTO_SAMPLE_SIZE,
    method_opt => 'FOR ALL COLUMNS SIZE AUTO',
    cascade => TRUE
);
```

### 4.2 定期维护计划
```sql
-- 创建定期统计信息收集作业
BEGIN
    DBMS_SCHEDULER.CREATE_JOB(
        job_name => 'GATHER_CUS_STATS_JOB',
        job_type => 'PLSQL_BLOCK',
        job_action => 'BEGIN DBMS_STATS.GATHER_TABLE_STATS(''SCHEMA_NAME'', ''CUS_TRADE_UNIFIED_STATISTICS''); END;',
        start_date => SYSTIMESTAMP,
        repeat_interval => 'FREQ=WEEKLY; BYDAY=SUN; BYHOUR=2',
        enabled => TRUE
    );
END;
/
```

## 5. 内存优化

### 5.1 SGA参数调优
```sql
-- 查看当前SGA配置
SELECT name, value FROM v$parameter 
WHERE name IN ('sga_target', 'pga_aggregate_target', 'shared_pool_size');

-- 建议配置（根据实际情况调整）
ALTER SYSTEM SET sga_target = 4G SCOPE=SPFILE;
ALTER SYSTEM SET pga_aggregate_target = 2G SCOPE=SPFILE;
```

### 5.2 结果集缓存
```sql
-- 启用结果集缓存（适用于重复查询）
ALTER SYSTEM SET result_cache_mode = FORCE;
ALTER SYSTEM SET result_cache_max_size = 1G;

-- 在查询中使用结果集缓存
SELECT /*+ RESULT_CACHE */ 
       STAT_TYPE, COUNT(*) 
FROM CUS_TRADE_UNIFIED_STATISTICS 
GROUP BY STAT_TYPE;
```

## 6. 存储优化

### 6.1 表空间管理
```sql
-- 创建专用表空间
CREATE TABLESPACE CUS_DATA
DATAFILE '/path/to/cus_data01.dbf' SIZE 10G
AUTOEXTEND ON NEXT 1G MAXSIZE 50G
EXTENT MANAGEMENT LOCAL
SEGMENT SPACE MANAGEMENT AUTO;

-- 将表移动到专用表空间
ALTER TABLE CUS_TRADE_UNIFIED_STATISTICS MOVE TABLESPACE CUS_DATA;
```

### 6.2 压缩策略
```sql
-- 启用表压缩（减少存储空间）
ALTER TABLE CUS_TRADE_UNIFIED_STATISTICS COMPRESS FOR OLTP;

-- 对于历史数据，可以使用更高级的压缩
ALTER TABLE CUS_TRADE_UNIFIED_STATISTICS COMPRESS FOR QUERY HIGH;
```

## 7. 监控和维护

### 7.1 性能监控查询
```sql
-- 监控表的访问情况
SELECT table_name, num_rows, blocks, avg_row_len, last_analyzed
FROM user_tables 
WHERE table_name = 'CUS_TRADE_UNIFIED_STATISTICS';

-- 监控索引使用情况
SELECT index_name, num_rows, distinct_keys, clustering_factor, last_analyzed
FROM user_indexes 
WHERE table_name = 'CUS_TRADE_UNIFIED_STATISTICS';

-- 查看执行计划
EXPLAIN PLAN FOR 
SELECT * FROM CUS_TRADE_UNIFIED_STATISTICS 
WHERE STAT_TYPE = '02' AND STAT_DATE >= DATE '2024-01-01';

SELECT * FROM TABLE(DBMS_XPLAN.DISPLAY);
```

### 7.2 定期维护任务
1. **每周**：收集统计信息
2. **每月**：检查索引碎片，必要时重建
3. **每季度**：分析查询性能，优化慢查询
4. **每年**：评估分区策略，清理历史数据

## 8. 容量规划

### 8.1 存储容量估算
- 假设每条记录约 500 字节
- 每月新增数据约 100万条
- 年增长量：500字节 × 100万 × 12 = 6GB
- 建议预留 3-5 年容量：30-50GB

### 8.2 性能基准
- 单表查询响应时间：< 1秒
- 复杂分析查询：< 10秒  
- 批量数据加载：> 10万条/分钟
- 并发查询支持：> 50个并发用户

## 9. 备份和恢复策略

### 9.1 备份策略
```sql
-- 逻辑备份（使用expdp）
expdp username/password@database \
  tables=CUS_TRADE_UNIFIED_STATISTICS \
  directory=BACKUP_DIR \
  dumpfile=cus_data_%U.dmp \
  parallel=4 \
  compression=all

-- 物理备份（RMAN）
RMAN> BACKUP TABLESPACE CUS_DATA;
```

### 9.2 恢复测试
定期进行恢复测试，确保备份的有效性和恢复流程的正确性。
