# 海关进出口统计数据统一大表设计方案

## 1. 现状分析

### 当前问题
- 数据分散在18张不同的表中，每个维度一张表
- 查询复杂，需要关联多张表
- 数据维护困难，结构不统一
- 分析效率低，不利于数据人员使用

### 现有表结构特点
1. **表(1)**: 总值表 - 无分类维度，只有时间维度
2. **表(2)**: 国别地区表 - 按地区分类
3. **表(3)**: 商品构成表 - 按商品构成分类  
4. **表(4)**: 商品类章表 - 按商品类章分类
5. **表(5)**: 贸易方式表 - 按贸易方式分类
6. **表(6-7)**: 贸易方式×企业性质表 - 双维度交叉
7. **表(8)**: 收发货人所在地表 - 按地区分类
8. **表(9)**: 境内目的地货源地表 - 按地区分类
9. **表(10)**: 关别总值表 - 按海关分类
10. **表(11)**: 特定地区表 - 按特定地区分类
11. **表(12)**: 外商投资企业表 - 按企业类型分类
12. **表(13-14)**: 主要商品量值表 - 按商品+贸易方向分类
13. **表(15-16)**: 国家×商品表 - 按国家+商品分类
14. **表(17-18)**: 商品×贸易方式表 - 按商品+贸易方式分类

## 2. 统一大表设计

### 2.1 设计原则
1. **维度统一**: 所有分类维度作为字段存储
2. **指标标准化**: 统一指标命名和数据类型
3. **扩展性**: 预留字段支持未来扩展
4. **查询友好**: 优化索引和分区策略

### 2.2 表结构设计

#### 核心维度字段
- `STAT_DATE` - 统计日期 (DATE)
- `STAT_TYPE` - 统计类型编码 (VARCHAR2(10))
- `STAT_TYPE_NAME` - 统计类型名称 (VARCHAR2(100))
- `CURRENCY_TYPE` - 货币类型 (VARCHAR2(10))
- `UNIT` - 计量单位 (VARCHAR2(50))

#### 分类维度字段
- `DIMENSION_1` - 主要分类维度 (VARCHAR2(500))
- `DIMENSION_2` - 次要分类维度 (VARCHAR2(500)) 
- `DIMENSION_3` - 第三分类维度 (VARCHAR2(500))
- `TRADE_DIRECTION` - 贸易方向 (VARCHAR2(10))

#### 核心指标字段
- `MONTH_IE_AMOUNT` - 当月进出口金额
- `MONTH_EXP_AMOUNT` - 当月出口金额  
- `MONTH_IMP_AMOUNT` - 当月进口金额
- `YTD_IE_AMOUNT` - 累计进出口金额
- `YTD_EXP_AMOUNT` - 累计出口金额
- `YTD_IMP_AMOUNT` - 累计进口金额

#### 同比指标字段
- `MONTH_IE_YOY` - 当月进出口同比(%)
- `MONTH_EXP_YOY` - 当月出口同比(%)
- `MONTH_IMP_YOY` - 当月进口同比(%)
- `YTD_IE_YOY` - 累计进出口同比(%)
- `YTD_EXP_YOY` - 累计出口同比(%)
- `YTD_IMP_YOY` - 累计进口同比(%)

#### 数量相关字段
- `MONTH_QUANTITY` - 当月数量
- `YTD_QUANTITY` - 累计数量
- `MONTH_QUANTITY_YOY` - 当月数量同比(%)
- `YTD_QUANTITY_YOY` - 累计数量同比(%)

#### 元数据字段
- `DATA_SOURCE` - 数据来源
- `CREATE_TIME` - 创建时间
- `UPDATE_TIME` - 更新时间

### 2.3 统计类型编码规则

| 编码 | 统计类型 | DIMENSION_1 | DIMENSION_2 | DIMENSION_3 |
|------|----------|-------------|-------------|-------------|
| 01 | 总值统计 | NULL | NULL | NULL |
| 02 | 国别地区 | 国家/地区名称 | NULL | NULL |
| 03 | 商品构成 | 商品构成类别 | NULL | NULL |
| 04 | 商品类章 | 商品类章名称 | NULL | NULL |
| 05 | 贸易方式 | 贸易方式名称 | NULL | NULL |
| 06 | 贸易方式×企业性质 | 贸易方式 | 企业性质 | NULL |
| 07 | 收发货人所在地 | 地区名称 | NULL | NULL |
| 08 | 境内目的地货源地 | 地区名称 | NULL | NULL |
| 09 | 关别统计 | 海关名称 | NULL | NULL |
| 10 | 特定地区 | 地区名称 | NULL | NULL |
| 11 | 外商投资企业 | 企业类型 | NULL | NULL |
| 12 | 主要商品量值 | 商品名称 | NULL | NULL |
| 13 | 国家×商品 | 国家名称 | 商品描述 | NULL |
| 14 | 商品×贸易方式 | 商品名称 | 贸易方式 | NULL |

## 3. 优势分析

### 3.1 查询便利性
- 单表查询，无需复杂关联
- 统一的字段命名和数据格式
- 支持多维度灵活分析

### 3.2 维护便利性  
- 统一的数据入库流程
- 一致的数据质量控制
- 简化的备份和恢复

### 3.3 扩展性
- 预留维度字段支持新的分类
- 统一的元数据管理
- 便于添加新的指标字段

## 4. 实施建议

### 4.1 分阶段实施
1. 创建统一大表结构
2. 开发数据迁移脚本
3. 验证数据完整性
4. 创建查询视图
5. 性能优化调整

### 4.2 风险控制
- 保留原有分表作为备份
- 充分测试数据迁移脚本
- 建立数据质量检查机制
- 制定回滚方案
