# 海关进出口统计数据统一大表设计方案

## 1. 现状分析

### 当前问题
- 数据分散在18张不同的表中，每个维度一张表
- 查询复杂，需要关联多张表
- 数据维护困难，结构不统一
- 分析效率低，不利于数据人员使用

### 现有表结构特点
1. **表(1)**: 总值表 - 无分类维度，只有时间维度
2. **表(2)**: 国别地区表 - 按地区分类
3. **表(3)**: 商品构成表 - 按商品构成分类  
4. **表(4)**: 商品类章表 - 按商品类章分类
5. **表(5)**: 贸易方式表 - 按贸易方式分类
6. **表(6-7)**: 贸易方式×企业性质表 - 双维度交叉
7. **表(8)**: 收发货人所在地表 - 按地区分类
8. **表(9)**: 境内目的地货源地表 - 按地区分类
9. **表(10)**: 关别总值表 - 按海关分类
10. **表(11)**: 特定地区表 - 按特定地区分类
11. **表(12)**: 外商投资企业表 - 按企业类型分类
12. **表(13-14)**: 主要商品量值表 - 按商品+贸易方向分类
13. **表(15-16)**: 国家×商品表 - 按国家+商品分类
14. **表(17-18)**: 商品×贸易方式表 - 按商品+贸易方式分类

## 2. 统一大表设计（基于真实入库脚本分析）

### 2.1 设计原则
1. **完全兼容现有结构**: 基于18个真实入库脚本的字段分析
2. **指标标准化**: 统一指标命名，保持数据完整性
3. **维度灵活性**: 支持单维度和多维度交叉分析
4. **查询友好**: 优化索引和分区策略

### 2.2 真实字段映射分析

#### A. 表(1) - temp_cus_mon_1 字段
- 金额字段：VAL_MONTH_IE, VAL_MONTH_EXP, VAL_MONTH_IMP, VAL_MONTH_TB
- 累计字段：VAL_YTD_IE, VAL_YTD_EXP, VAL_YTD_IMP, VAL_YTD_TB
- 同比字段：YOY_MONTH_IE, YOY_MONTH_EXP, YOY_MONTH_IMP, YOY_MONTH_TB
- 环比字段：MOM_MONTH_IE, MOM_MONTH_EXP, MOM_MONTH_IMP, MOM_MONTH_TB

#### B. 表(2-12) - temp_cus_mon_X 字段模式
- 维度字段：LOCATION, COMPOSITION, CATEGORY_CHAPTER, TRADE_MODE等
- 金额字段：MONTH_IMPORT_EXPORT, MONTH_EXPORT, MONTH_IMPORT
- 累计字段：YTD_IMPORT_EXPORT, YTD_EXPORT, YTD_IMPORT
- 同比字段：YOY_IMPORT_EXPORT, YOY_EXPORT, YOY_IMPORT

#### C. 表(13-14) - CUS_TRADE_MAJOR_PRODUCT_MON 字段
- 维度字段：COMMODITY_NAME, TRADE_DIRECTION
- 数量字段：MONTH_QUANTITY, YTD_QUANTITY
- 金额字段：MONTH_AMOUNT, YTD_AMOUNT
- 同比字段：MONTH_QUANTITY_YOY, MONTH_AMOUNT_YOY, YTD_QUANTITY_YOY, YTD_AMOUNT_YOY
- 重量字段：YOY_MONTH_EXPORT_WEIGHT, YOY_YTD_EXPORT_WEIGHT等

#### D. 表(15-18) - 交叉分析表字段
- 维度字段：COUNTRY, HS_CODE_DESC, COMMODITY_NAME, TRADE_MODE
- 金额字段：VALUE_MONTH, VALUE_YTD
- 方向字段：TRADE_DIRECTION

### 2.3 统一大表字段设计

#### 核心维度字段
- `CURRENT_MONTH` - 统计月份 (DATE) - 与现有表保持一致
- `STAT_TYPE` - 统计类型编码 (VARCHAR2(10))
- `STAT_TYPE_NAME` - 统计类型名称 (VARCHAR2(100))
- `CURRENCY_TYPE` - 货币类型 (VARCHAR2(10)) - 人民币/美元
- `UNIT` - 计量单位 (VARCHAR2(50))

#### 分类维度字段（灵活支持各种分类）
- `DIMENSION_1` - 主要分类维度 (VARCHAR2(500)) - 国家、商品、贸易方式等
- `DIMENSION_2` - 次要分类维度 (VARCHAR2(500)) - 用于交叉分析
- `DIMENSION_3` - 第三分类维度 (VARCHAR2(500)) - 预留扩展
- `TRADE_DIRECTION` - 贸易方向 (VARCHAR2(10)) - 进口/出口/进出口

#### 金额指标字段（兼容所有表的金额字段）
- `VAL_MONTH_IE` - 当月进出口金额
- `VAL_MONTH_EXP` - 当月出口金额
- `VAL_MONTH_IMP` - 当月进口金额
- `VAL_MONTH_TB` - 当月贸易差额
- `VAL_YTD_IE` - 累计进出口金额
- `VAL_YTD_EXP` - 累计出口金额
- `VAL_YTD_IMP` - 累计进口金额
- `VAL_YTD_TB` - 累计贸易差额

#### 同比指标字段
- `YOY_MONTH_IE` - 当月进出口同比(%)
- `YOY_MONTH_EXP` - 当月出口同比(%)
- `YOY_MONTH_IMP` - 当月进口同比(%)
- `YOY_MONTH_TB` - 当月贸易差额同比(%)
- `YOY_YTD_IE` - 累计进出口同比(%)
- `YOY_YTD_EXP` - 累计出口同比(%)
- `YOY_YTD_IMP` - 累计进口同比(%)
- `YOY_YTD_TB` - 累计贸易差额同比(%)

#### 环比指标字段（表1特有）
- `MOM_MONTH_IE` - 当月进出口环比(%)
- `MOM_MONTH_EXP` - 当月出口环比(%)
- `MOM_MONTH_IMP` - 当月进口环比(%)
- `MOM_MONTH_TB` - 当月贸易差额环比(%)

#### 数量相关字段（表13-14特有）
- `MONTH_QUANTITY` - 当月数量
- `YTD_QUANTITY` - 累计数量
- `MONTH_QUANTITY_YOY` - 当月数量同比(%)
- `YTD_QUANTITY_YOY` - 累计数量同比(%)
- `QUANTITY_UNIT` - 数量单位

#### 重量相关字段（表13-14特有）
- `YOY_MONTH_EXPORT_WEIGHT` - 当月出口重量同比
- `YOY_YTD_EXPORT_WEIGHT` - 累计出口重量同比
- `YOY_MONTH_IMPORT_WEIGHT` - 当月进口重量同比
- `YOY_YTD_IMPORT_WEIGHT` - 累计进口重量同比

#### 元数据字段
- `SOURCE_TABLE` - 源表名称
- `DATA_SOURCE` - 数据来源
- `CREATE_TIME` - 创建时间
- `UPDATE_TIME` - 更新时间

### 2.3 统计类型编码规则

| 编码 | 统计类型 | DIMENSION_1 | DIMENSION_2 | DIMENSION_3 |
|------|----------|-------------|-------------|-------------|
| 01 | 总值统计 | NULL | NULL | NULL |
| 02 | 国别地区 | 国家/地区名称 | NULL | NULL |
| 03 | 商品构成 | 商品构成类别 | NULL | NULL |
| 04 | 商品类章 | 商品类章名称 | NULL | NULL |
| 05 | 贸易方式 | 贸易方式名称 | NULL | NULL |
| 06 | 贸易方式×企业性质 | 贸易方式 | 企业性质 | NULL |
| 07 | 收发货人所在地 | 地区名称 | NULL | NULL |
| 08 | 境内目的地货源地 | 地区名称 | NULL | NULL |
| 09 | 关别统计 | 海关名称 | NULL | NULL |
| 10 | 特定地区 | 地区名称 | NULL | NULL |
| 11 | 外商投资企业 | 企业类型 | NULL | NULL |
| 12 | 主要商品量值 | 商品名称 | NULL | NULL |
| 13 | 国家×商品 | 国家名称 | 商品描述 | NULL |
| 14 | 商品×贸易方式 | 商品名称 | 贸易方式 | NULL |

## 3. 优势分析

### 3.1 查询便利性
- 单表查询，无需复杂关联
- 统一的字段命名和数据格式
- 支持多维度灵活分析

### 3.2 维护便利性  
- 统一的数据入库流程
- 一致的数据质量控制
- 简化的备份和恢复

### 3.3 扩展性
- 预留维度字段支持新的分类
- 统一的元数据管理
- 便于添加新的指标字段

## 4. 实施建议

### 4.1 分阶段实施
1. 创建统一大表结构
2. 开发数据迁移脚本
3. 验证数据完整性
4. 创建查询视图
5. 性能优化调整

### 4.2 风险控制
- 保留原有分表作为备份
- 充分测试数据迁移脚本
- 建立数据质量检查机制
- 制定回滚方案
