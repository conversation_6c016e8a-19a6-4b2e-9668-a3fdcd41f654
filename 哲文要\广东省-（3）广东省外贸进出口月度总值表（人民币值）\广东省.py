# %%
from DrissionPage import Chromium, ChromiumOptions
from datetime import datetime
import pandas as pd
import os
import re
import time
import random
import json

# --- 日志文件处理 ---
LOG_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', 'download_log.json')

def load_log():
    """加载日志文件，如果不存在则创建一个空的。"""
    if not os.path.exists(LOG_FILE):
        return {"national": [], "guangdong": []}
    with open(LOG_FILE, 'r', encoding='utf-8') as f:
        return json.load(f)

def save_log(log_data):
    """保存日志数据到文件。"""
    with open(LOG_FILE, 'w', encoding='utf-8') as f:
        json.dump(log_data, f, indent=4, ensure_ascii=False)

def is_file_logged_guangdong(filename, log_data):
    """检查文件是否已在广东日志中。"""
    return filename in log_data.get("guangdong", [])

def log_file_guangdong(filename, log_data):
    """将文件添加到广东日志中。"""
    if filename not in log_data["guangdong"]:
        log_data["guangdong"].append(filename)

# --------------------

def sanitize_filename(filename):
    """去除文件名中的非法字符"""
    return re.sub(r'[\\/*?:"<>|]', '', filename)

def process_data_page(tab, output_dir):
    """
    处理单个数据页面，使用DrissionPage定位器提取表格数据并生成Excel文件。
    """
    download_log = load_log()

    try:
        page_title_ele = tab.ele('tag:h2', timeout=5)
        page_title = page_title_ele.text if page_title_ele else tab.title
        print(f"\n正在处理页面: {page_title}")

        # 直接定位ID为'easysiteText'的元素，这本身应该就是表格
        table = tab.wait.ele_displayed('#easysiteText', timeout=10)
        if not table:
            print(f"警告：在页面 {page_title} 未找到ID为'easysiteText'的元素。")
            return

        rows = table.eles('tag:tr')
        if len(rows) < 4:
            print(f"警告：在 {page_title} 的表格中未找到足够的数据行。")
            return

        print(f"找到 {len(rows)} 行，准备解析...")

        # 跳过前3个标题行和最后1个备注行
        for row in rows[3:-1]:
            cells = row.children('tag:td')
            # 确保行中有足够的列
            if len(cells) < 6:
                continue

            month_str = cells[0].text.strip()
            total_val = cells[1].text.strip()
            export_val = cells[3].text.strip()
            import_val = cells[5].text.strip()

            # 检查是否为有效数据行（月份非空且有数值）
            if not month_str or '年' not in month_str or not total_val or total_val == '　':
                continue

            # 兼容 "2023年1-2月" 和 "2023年2月" 两种格式
            month_match = re.search(r'(\d{4})年(.*月)', month_str)
            if not month_match:
                print(f"  跳过无法解析的行: {month_str}")
                continue

            year, month_part = month_match.groups()
            
            # 清理月份字段中的空格
            month_part = re.sub(r'\s+', '', month_part)
            file_month_str = f"{year}年{month_part}"
            
            output_df = pd.DataFrame({
                '关键字': ['进出口总值', '出口总值', '进口总值'],
                '数值': [total_val, export_val, import_val]
            })

            file_name = f"（3）{file_month_str}广东省外贸进出口月度总值表（人民币值）.xlsx"
            safe_file_name = sanitize_filename(file_name)

            if is_file_logged_guangdong(safe_file_name, download_log):
                print(f"  文件已在日志中，跳过: {safe_file_name}")
                continue

            output_path = os.path.join(output_dir, safe_file_name)
            
            if os.path.exists(output_path):
                print(f"  文件已存在于本地，记录日志并跳过: {safe_file_name}")
                log_file_guangdong(safe_file_name, download_log)
                save_log(download_log)
                continue

            output_df.to_excel(output_path, header=False, index=False)
            print(f"  已生成文件: {safe_file_name}")
            # 生成成功后记录日志
            log_file_guangdong(safe_file_name, download_log)
            save_log(download_log)

    except Exception as e:
        print(f"处理页面 {tab.url} 时发生错误: {e}")

def scrape_monthly_overview(tab, base_url, output_dir):
    """
    采集所有"广东省外贸进出口月度总值表"数据。
    """
    print(f"\n--- 开始采集'月度总值表'数据, 访问: {base_url} ---")
    tab.get(base_url)
    
    # 等待由JS生成的列表容器加载完成
    container = tab.ele('.conList_ul', timeout=10)
    if not container:
        print("错误：等待列表容器'.conList_ul'加载超时。")
        return

    # 改为使用文本内容进行模糊匹配，而不是title属性，并使用正确的组合语法
    links = container.eles('t:a@@text():广东省外贸进出口月度总值表')

    if not links:
        print("在目录页未找到任何'月度总值表'链接。")
        return

    print(f"找到 {len(links)} 个'月度总值表'链接，准备逐一处理...")
    
    # 收集链接，避免在循环中操作页面导致元素失效
    report_urls = [{'url': link.attr('href'), 'title': link.text} for link in links]

    for report in report_urls:
        print(f"\n正在导航到: {report['title']}")
        tab.get(report['url'])
        time.sleep(random.uniform(1, 2))
        process_data_page(tab, output_dir)

def scrape_trade_by_mode(tab, base_url, output_dir):
    """
    通过翻页查找并采集"进出口商品贸易方式总值表"数据。
    """
    print(f"\n--- 开始采集'贸易方式总值表'数据, 访问: {base_url} ---")
    tab.get(base_url)
    page_num = 1

    while True:
        print(f"正在第 {page_num} 页查找'贸易方式总值表'...")

        # 等待由JS生成的列表容器加载完成
        container = tab.ele('.conList_ul', timeout=10)
        if not container:
            print("错误：等待列表容器'.conList_ul'加载超时。")
            break

        # 改为使用文本内容进行模糊匹配，而不是title属性，并使用正确的组合语法
        link = container.ele('t:a@@text():进出口商品贸易方式总值表（人民币值）', timeout=3)
        
        if link:
            print("成功找到目标链接，进入页面进行处理...")
            tab.get(link.attr('href'))
            time.sleep(random.uniform(1, 2))
            process_data_page(tab, output_dir)
            break

        # 如果本页没找到，尝试翻页
        print("本页未找到目标链接，尝试寻找'下一页'按钮...")
        next_button = tab.ele('t:a@@class:pagingNormal@@class:next', timeout=2)
        if next_button and 'pagingDisabled' not in (next_button.attr('class') or ''):
            print("找到'下一页'，准备翻页...")
            next_button.click()
            time.sleep(random.uniform(2, 3))
            page_num += 1
        else:
            print("未找到'贸易方式总值表'的链接，或已到达最后一页，搜索结束。")
            break

if __name__ == "__main__":
    co = ChromiumOptions().auto_port()
    browser = Chromium(addr_or_opts=co)
    tab = browser.latest_tab
    
    base_project_path = os.path.dirname(os.path.abspath(__file__))
    # 将基础输出目录固定为 '文件落地'
    output_base_dir = os.path.join(base_project_path, '..', '文件落地')

    TARGET_URL = 'http://gdfs.customs.gov.cn/guangdong_sub/zwgk62/sjgb59/index.html'

    # --- 任务1: 采集月度总值表 ---
    monthly_output_dir = os.path.join(output_base_dir, '广东省外贸进出口月度总值表（人民币值）')
    if not os.path.exists(monthly_output_dir):
        os.makedirs(monthly_output_dir)
    scrape_monthly_overview(tab, TARGET_URL, monthly_output_dir)

    print("\n--- 广东省数据采集完成 ---")
    browser.quit()

    print("\n--- 开始执行数据入库流程 ---")
    importer_script_path = os.path.join(base_project_path, '..', '贸易数据入库.py')
    
    if os.path.exists(importer_script_path):
        # 为第一个任务的文件夹执行入库
        folder_name_1 = os.path.basename(monthly_output_dir)
        print(f"\n准备为文件夹 '{folder_name_1}' 执行入库...")
        os.system(f'python "{importer_script_path}" "{folder_name_1}"')

        print("\n--- 所有数据入库流程已结束 ---")
    else:
        print(f"错误：未找到入库脚本 '贸易数据入库.py'。")


