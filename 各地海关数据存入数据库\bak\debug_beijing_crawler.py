from DrissionPage import ChromiumPage
import time
import os
import re

def debug_beijing_crawler():
    """调试北京海关爬虫"""

    print("=== 调试北京海关爬虫 ===")

    page = ChromiumPage(timeout=30)

    try:
        # 访问北京海关统计数据页面
        beijing_url = "http://beijing.customs.gov.cn/beijing_customs/434756/434804/2941702/434773/434774/index.html"
        print(f"正在访问: {beijing_url}")

        page.get(beijing_url)
        time.sleep(5)

        print(f"页面标题: {page.title}")

        # 查找所有链接
        all_links = page.eles('tag:a')
        print(f"找到 {len(all_links)} 个链接")

        # 查找包含2024或2025年的链接
        target_year_links = []
        for i, link in enumerate(all_links):
            link_text = link.text.strip()
            href = link.attr('href')

            if link_text and any(year in link_text for year in ['2024', '2025']):
                target_year_links.append({
                    'index': i,
                    'text': link_text,
                    'href': href
                })
                print(f"找到年份链接 {len(target_year_links)}: {link_text}")

        print(f"\n总共找到 {len(target_year_links)} 个包含年份的链接")

        # 检查我们的筛选逻辑
        print("\n开始筛选目标表格...")
        target_tables = []

        for link in target_year_links:
            text = link['text']

            # 检查是否是我们需要的表格
            if "北京地区" in text:
                if "(5)" in text and "贸易方式" in text and "总值表" in text and "企业性质" not in text:
                    target_tables.append((link, "(5)", "北京地区进出口商品贸易方式总值表"))
                    print(f"匹配 (5): {text}")
                elif "(6)" in text and "出口" in text and "贸易方式" in text and "企业性质" in text:
                    target_tables.append((link, "(6)", "北京地区出口商品贸易方式企业性质总值表"))
                    print(f"匹配 (6): {text}")
                elif "(7)" in text and "进口" in text and "贸易方式" in text and "企业性质" in text:
                    target_tables.append((link, "(7)", "北京地区进口商品贸易方式企业性质总值表"))
                    print(f"匹配 (7): {text}")
                elif "(8)" in text and "出口" in text and "主要商品" in text and "量值表" in text:
                    target_tables.append((link, "(8)", "北京地区出口主要商品量值表"))
                    print(f"匹配 (8): {text}")
                elif "(9)" in text and "进口" in text and "主要商品" in text and "量值表" in text:
                    target_tables.append((link, "(9)", "北京地区进口主要商品量值表"))
                    print(f"匹配 (9): {text}")
                else:
                    print(f"不匹配: {text}")

        print(f"\n筛选后找到 {len(target_tables)} 个目标表格")

        # 保存调试信息
        with open('调试信息_北京海关.txt', 'w', encoding='utf-8') as f:
            f.write("所有包含年份的链接:\n")
            f.write("=" * 50 + "\n")
            for link in target_year_links:
                f.write(f"{link['text']}\n")

            f.write("\n\n筛选后的目标表格:\n")
            f.write("=" * 50 + "\n")
            for link, key, name in target_tables:
                f.write(f"{key}: {link['text']}\n")

        print("调试信息已保存到: 调试信息_北京海关.txt")

        # 如果找到目标表格，测试访问第一个
        if target_tables:
            print(f"\n测试访问第一个目标表格...")
            first_link = target_tables[0][0]

            # 构建完整URL
            href = first_link['href']
            if href.startswith('/'):
                full_url = "http://beijing.customs.gov.cn" + href
            else:
                full_url = href

            print(f"访问: {full_url}")
            page.get(full_url)
            time.sleep(3)

            print(f"页面标题: {page.title}")

            # 查找.xls文件链接
            xls_links = page.eles('tag:a')
            file_found = False

            for xls_link in xls_links:
                href = xls_link.attr('href')
                if href and (href.endswith('.xls') or href.endswith('.xlsx')):
                    print(f"找到文件链接: {href}")
                    file_found = True
                    break

            if not file_found:
                print("未找到.xls文件链接，检查meta标签...")
                meta_elements = page.eles('tag:meta')
                for meta in meta_elements:
                    content = meta.attr('content')
                    if content and (content.endswith('.xls') or content.endswith('.xlsx')):
                        print(f"在meta标签中找到文件: {content}")
                        file_found = True
                        break

            if not file_found:
                print("未找到任何文件链接")

    except Exception as e:
        print(f"调试过程中出错: {e}")
        import traceback
        traceback.print_exc()

    finally:
        page.quit()

if __name__ == "__main__":
    debug_beijing_crawler()