-- 清空海关统计数据表并重新导入
-- 执行前请确认备份重要数据

-- 1. 查看当前数据概况
SELECT 'Before Clear:' as Status, DATA_SOURCE, COUNT(*) as Records 
FROM T_STATISTICAL_CUS_TOTAL_CS 
GROUP BY DATA_SOURCE
UNION ALL
SELECT 'Total:' as Status, 'ALL' as DATA_SOURCE, COUNT(*) as Records
FROM T_STATISTICAL_CUS_TOTAL_CS;

-- 2. 清空整个表
TRUNCATE TABLE T_STATISTICAL_CUS_TOTAL_CS;

-- 3. 确认表已清空
SELECT COUNT(*) as "Records After Clear" FROM T_STATISTICAL_CUS_TOTAL_CS;

-- 4. 提交事务
COMMIT;

PROMPT '表已清空，请依次执行：';
PROMPT '1. 浙江数据导入（DATA_SOURCE = 07）';
PROMPT '2. 北京数据导入（DATA_SOURCE = 08）';