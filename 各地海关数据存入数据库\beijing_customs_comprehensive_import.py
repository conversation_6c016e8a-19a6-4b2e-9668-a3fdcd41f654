"""
北京海关统计数据完整批量处理系统
基于对7种类型文件（1、2、5、6、7、8、9）的结构分析编写
支持Excel批量转换为CSV和Oracle数据库导入
"""

import pandas as pd
import numpy as np
import datetime
import os
import re
from pathlib import Path

def extract_date_from_filename(filename):
    """
    从文件名中提取日期信息
    """
    patterns = [
        r'(\d{4})年(\d{1,2})月',
        r'(\d{4})年1-(\d{1,2})月',
        r'(\d{4})_.*?(\d{4})年1-(\d{1,2})月',
        r'(\d{4})年(\d{1,2})-(\d{1,2})月',
    ]
    
    for pattern in patterns:
        match = re.search(pattern, filename)
        if match:
            groups = match.groups()
            if len(groups) == 2:
                year, month = groups
                return f"{year}/{month.zfill(2)}/1"
            elif len(groups) == 3:
                if '1-' in filename:
                    year, end_month = groups[0], groups[2]
                    return f"{year}/{end_month.zfill(2)}/1"
                else:
                    year, start_month, end_month = groups
                    return f"{year}/{end_month.zfill(2)}/1"
    
    return datetime.datetime.now().strftime('%Y/%m/1')

def determine_file_type(filename):
    """
    根据文件名确定文件类型
    """
    type_mapping = {
        '（1）': 'type_1_monthly',
        '（2）': 'type_2_country',
        '（5）': 'type_5_trade_mode',
        '（6）': 'type_6_export_enterprise',
        '（7）': 'type_7_import_enterprise',
        '（8）': 'type_8_export_commodity',
        '（9）': 'type_9_import_commodity'
    }
    
    for pattern, file_type in type_mapping.items():
        if pattern in filename:
            return file_type
    
    return 'unknown'

def clean_numeric_value(value):
    """
    清理数值，处理--等特殊值
    """
    if pd.isna(value) or value == '' or str(value) == '--' or str(value) == 'nan':
        return None
    try:
        return float(value)
    except:
        return None

def clean_string_value(value):
    """
    清理字符串值
    """
    if pd.isna(value) or value == '' or str(value) == 'nan':
        return ''
    return str(value).strip()

def process_type_1_monthly(df, filename):
    """
    处理类型1：月度总值表
    数据特点：时间序列，每行是一个月份
    """
    print("    处理月度总值表...")
    
    # 找到数据开始行（包含年月日的行）
    data_start_row = 0
    for i, row in df.iterrows():
        if str(row.iloc[0]).startswith('2'):  # 年份开头
            data_start_row = i
            break
    
    if data_start_row == 0:
        print("    未找到有效数据行")
        return None
    
    # 获取表头（数据行的上一行）
    header_row = data_start_row - 1
    headers = df.iloc[header_row].tolist()
    
    # 提取数据
    data_df = df.iloc[data_start_row:].copy()
    data_df.columns = range(len(data_df.columns))  # 重新设置列索引
    
    # 过滤有效数据行
    valid_rows = []
    for _, row in data_df.iterrows():
        if str(row.iloc[0]).startswith('2') and len(str(row.iloc[0])) == 6:
            valid_rows.append(row)
    
    if not valid_rows:
        return None
    
    # 构建结果数据
    results = []
    for row in valid_rows:
        result = {
            'STAT_TYPE': '01',
            'STAT_NAME': '月度总值',
            'STAT_CONTENT_RAW': str(row.iloc[0]),
            'STAT_CONTENT_CLEANSE': str(row.iloc[0]),
            # 当月进出口
            'MON_A_CNY_AMOUNT': clean_numeric_value(row.iloc[1]),  # 人民币(万)
            'MON_A_CNY_YOY': clean_numeric_value(row.iloc[2]),    # 人民币同比
            'MON_A_USD_AMOUNT': clean_numeric_value(row.iloc[3]),  # 美元值(千)
            'MON_A_USD_YOY': clean_numeric_value(row.iloc[4]),    # 美元值同比
            # 当月出口
            'MON_E_CNY_AMOUNT': clean_numeric_value(row.iloc[5]),  # 人民币(万)
            'MON_E_CNY_YOY': clean_numeric_value(row.iloc[6]),    # 人民币同比
            'MON_E_USD_AMOUNT': clean_numeric_value(row.iloc[7]),  # 美元值(千)
            'MON_E_USD_YOY': clean_numeric_value(row.iloc[8]),    # 美元值同比
            # 当月进口
            'MON_I_CNY_AMOUNT': clean_numeric_value(row.iloc[9]),  # 人民币(万)
            'MON_I_CNY_YOY': clean_numeric_value(row.iloc[10]),   # 人民币同比
            'MON_I_USD_AMOUNT': clean_numeric_value(row.iloc[11]), # 美元值(千)
            'MON_I_USD_YOY': clean_numeric_value(row.iloc[12]),   # 美元值同比
        }
        results.append(result)
    
    print(f"    提取到 {len(results)} 条月度数据")
    return results

def process_type_2_country(df, filename):
    """
    处理类型2：国别地区总值表
    """
    print("    处理国别地区总值表...")
    
    # 找到数据开始行（包含"产终国"的行）
    data_start_row = 0
    for i, row in df.iterrows():
        if '产终国' in str(row.iloc[0]):
            data_start_row = i + 1
            break
    
    if data_start_row == 0:
        print("    未找到有效数据行")
        return None
    
    # 提取数据
    data_df = df.iloc[data_start_row:].copy()
    
    results = []
    for _, row in data_df.iterrows():
        country_name = clean_string_value(row.iloc[0])
        if not country_name or country_name in ['', 'nan']:
            continue
        
        result = {
            'STAT_TYPE': '03',
            'STAT_NAME': '国别地区',
            'STAT_CONTENT_RAW': country_name,
            'STAT_CONTENT_CLEANSE': country_name.strip(),
            # 当月进出口
            'MON_A_CNY_AMOUNT': clean_numeric_value(row.iloc[1]),
            'MON_A_CNY_YOY': clean_numeric_value(row.iloc[2]),
            'MON_A_USD_AMOUNT': clean_numeric_value(row.iloc[3]),
            'MON_A_USD_YOY': clean_numeric_value(row.iloc[4]),
            # 累计进出口
            'ACC_A_CNY_AMOUNT': clean_numeric_value(row.iloc[5]),
            'ACC_A_CNY_YOY': clean_numeric_value(row.iloc[6]),
            'ACC_A_USD_AMOUNT': clean_numeric_value(row.iloc[7]),
            'ACC_A_USD_YOY': clean_numeric_value(row.iloc[8]),
            # 当月出口
            'MON_E_CNY_AMOUNT': clean_numeric_value(row.iloc[9]),
            'MON_E_CNY_YOY': clean_numeric_value(row.iloc[10]),
            'MON_E_USD_AMOUNT': clean_numeric_value(row.iloc[11]),
            'MON_E_USD_YOY': clean_numeric_value(row.iloc[12]),
            # 累计出口
            'ACC_E_CNY_AMOUNT': clean_numeric_value(row.iloc[13]),
            'ACC_E_CNY_YOY': clean_numeric_value(row.iloc[14]),
            'ACC_E_USD_AMOUNT': clean_numeric_value(row.iloc[15]),
            'ACC_E_USD_YOY': clean_numeric_value(row.iloc[16]),
            # 当月进口
            'MON_I_CNY_AMOUNT': clean_numeric_value(row.iloc[17]),
            'MON_I_CNY_YOY': clean_numeric_value(row.iloc[18]),
            'MON_I_USD_AMOUNT': clean_numeric_value(row.iloc[19]),
            'MON_I_USD_YOY': clean_numeric_value(row.iloc[20]),
            # 累计进口
            'ACC_I_CNY_AMOUNT': clean_numeric_value(row.iloc[21]),
            'ACC_I_CNY_YOY': clean_numeric_value(row.iloc[22]),
            'ACC_I_USD_AMOUNT': clean_numeric_value(row.iloc[23]),
            'ACC_I_USD_YOY': clean_numeric_value(row.iloc[24]) if len(row) > 24 else None,
        }
        results.append(result)
    
    print(f"    提取到 {len(results)} 条国别地区数据")
    return results

def process_type_5_trade_mode(df, filename):
    """
    处理类型5：贸易方式总值表
    """
    print("    处理贸易方式总值表...")
    
    # 找到数据开始行（包含"中文"的行）
    data_start_row = 0
    for i, row in df.iterrows():
        if '中文' in str(row.iloc[0]):
            data_start_row = i + 1
            break
    
    if data_start_row == 0:
        print("    未找到有效数据行")
        return None
    
    # 提取数据
    data_df = df.iloc[data_start_row:].copy()
    
    results = []
    for _, row in data_df.iterrows():
        trade_mode = clean_string_value(row.iloc[0])
        if not trade_mode or trade_mode in ['', 'nan']:
            continue
        
        result = {
            'STAT_TYPE': '01',
            'STAT_NAME': '贸易方式',
            'STAT_CONTENT_RAW': trade_mode,
            'STAT_CONTENT_CLEANSE': trade_mode.strip(),
            # 当月进出口
            'MON_A_CNY_AMOUNT': clean_numeric_value(row.iloc[1]),
            'MON_A_CNY_YOY': clean_numeric_value(row.iloc[2]),
            'MON_A_USD_AMOUNT': clean_numeric_value(row.iloc[3]),
            'MON_A_USD_YOY': clean_numeric_value(row.iloc[4]),
            # 累计进出口
            'ACC_A_CNY_AMOUNT': clean_numeric_value(row.iloc[5]),
            'ACC_A_CNY_YOY': clean_numeric_value(row.iloc[6]),
            'ACC_A_USD_AMOUNT': clean_numeric_value(row.iloc[7]),
            'ACC_A_USD_YOY': clean_numeric_value(row.iloc[8]),
            # 当月出口
            'MON_E_CNY_AMOUNT': clean_numeric_value(row.iloc[9]),
            'MON_E_CNY_YOY': clean_numeric_value(row.iloc[10]),
            'MON_E_USD_AMOUNT': clean_numeric_value(row.iloc[11]),
            'MON_E_USD_YOY': clean_numeric_value(row.iloc[12]),
            # 累计出口
            'ACC_E_CNY_AMOUNT': clean_numeric_value(row.iloc[13]),
            'ACC_E_CNY_YOY': clean_numeric_value(row.iloc[14]),
            'ACC_E_USD_AMOUNT': clean_numeric_value(row.iloc[15]),
            'ACC_E_USD_YOY': clean_numeric_value(row.iloc[16]),
            # 当月进口
            'MON_I_CNY_AMOUNT': clean_numeric_value(row.iloc[17]),
            'MON_I_CNY_YOY': clean_numeric_value(row.iloc[18]),
            'MON_I_USD_AMOUNT': clean_numeric_value(row.iloc[19]),
            'MON_I_USD_YOY': clean_numeric_value(row.iloc[20]),
            # 累计进口
            'ACC_I_CNY_AMOUNT': clean_numeric_value(row.iloc[21]),
            'ACC_I_CNY_YOY': clean_numeric_value(row.iloc[22]),
            'ACC_I_USD_AMOUNT': clean_numeric_value(row.iloc[23]),
            'ACC_I_USD_YOY': clean_numeric_value(row.iloc[24]) if len(row) > 24 else None,
        }
        results.append(result)
    
    print(f"    提取到 {len(results)} 条贸易方式数据")
    return results

def process_type_6_7_enterprise(df, filename, is_export=True):
    """
    处理类型6和7：企业性质表（出口和进口）
    """
    direction = "出口" if is_export else "进口"
    print(f"    处理企业性质表（{direction}）...")
    
    # 找到数据开始行（包含"企业性质"的行）
    data_start_row = 0
    for i, row in df.iterrows():
        if '企业性质' in str(row.iloc[0]):
            data_start_row = i + 1
            break
    
    if data_start_row == 0:
        print("    未找到有效数据行")
        return None
    
    # 提取数据
    data_df = df.iloc[data_start_row:].copy()
    
    results = []
    current_enterprise = ""
    
    for _, row in data_df.iterrows():
        enterprise_name = clean_string_value(row.iloc[0])
        trade_mode = clean_string_value(row.iloc[1])
        
        # 如果第一列不为空，说明是新的企业性质
        if enterprise_name and not enterprise_name.startswith('#'):
            current_enterprise = enterprise_name.strip()
        
        # 如果第二列是#合计，说明是该企业性质的合计数据
        if trade_mode == '#合计' and current_enterprise:
            result = {
                'STAT_TYPE': '02',
                'STAT_NAME': '企业性质',
                'STAT_CONTENT_RAW': current_enterprise,
                'STAT_CONTENT_CLEANSE': current_enterprise,
            }
            
            # 根据出口/进口设置相应字段
            if is_export:
                result.update({
                    'ACC_E_CNY_AMOUNT': clean_numeric_value(row.iloc[2]),
                    'ACC_E_CNY_YOY': clean_numeric_value(row.iloc[3]),
                    'ACC_E_USD_AMOUNT': clean_numeric_value(row.iloc[4]),
                    'ACC_E_USD_YOY': clean_numeric_value(row.iloc[5]) if len(row) > 5 else None,
                })
            else:
                result.update({
                    'ACC_I_CNY_AMOUNT': clean_numeric_value(row.iloc[2]),
                    'ACC_I_CNY_YOY': clean_numeric_value(row.iloc[3]),
                    'ACC_I_USD_AMOUNT': clean_numeric_value(row.iloc[4]),
                    'ACC_I_USD_YOY': clean_numeric_value(row.iloc[5]) if len(row) > 5 else None,
                })
            
            results.append(result)
    
    print(f"    提取到 {len(results)} 条企业性质数据（{direction}）")
    return results

def process_type_8_9_commodity(df, filename, is_export=True):
    """
    处理类型8和9：商品量值表（出口和进口）
    """
    direction = "出口" if is_export else "进口"
    print(f"    处理主要商品量值表（{direction}）...")
    
    # 找到数据开始行（包含"商品编码8位"的行）
    data_start_row = 0
    for i, row in df.iterrows():
        if '商品编码8位' in str(row.iloc[0]):
            data_start_row = i + 1
            break
    
    if data_start_row == 0:
        print("    未找到有效数据行")
        return None
    
    # 提取数据
    data_df = df.iloc[data_start_row:].copy()
    
    results = []
    for _, row in data_df.iterrows():
        commodity_name = clean_string_value(row.iloc[0])
        if not commodity_name or commodity_name in ['', 'nan']:
            continue
        
        # 过滤掉层级标记（以空格开头的分类）
        if commodity_name.startswith('　　　　　　'):
            # 这是具体商品
            commodity_clean = commodity_name.strip('　').strip()
        elif commodity_name.startswith('　　'):
            # 这是商品大类
            commodity_clean = commodity_name.strip('　').strip()
        else:
            commodity_clean = commodity_name.strip()
        
        if not commodity_clean:
            continue
        
        result = {
            'STAT_TYPE': '04' if is_export else '05',
            'STAT_NAME': '主出商品' if is_export else '主进商品',
            'STAT_CONTENT_RAW': commodity_name,
            'STAT_CONTENT_CLEANSE': commodity_clean,
        }
        
        # 数量单位
        unit = clean_string_value(row.iloc[1])
        
        # 根据出口/进口设置相应字段
        if is_export:
            result.update({
                # 当月数据
                'MON_E_AMOUNT': clean_numeric_value(row.iloc[2]),      # 第一数量
                'MON_E_AMOUNT_UNIT': unit,                             # 数量单位
                'MON_E_AMOUNT_YOY': clean_numeric_value(row.iloc[3]),  # 数量同比
                'MON_E_CNY_AMOUNT': clean_numeric_value(row.iloc[4]),  # 人民币金额
                'MON_E_CNY_YOY': clean_numeric_value(row.iloc[5]),     # 人民币同比
                'MON_E_USD_AMOUNT': clean_numeric_value(row.iloc[6]),  # 美元金额
                'MON_E_USD_YOY': clean_numeric_value(row.iloc[7]),     # 美元同比
                # 累计数据
                'ACC_E_AMOUNT': clean_numeric_value(row.iloc[8]),      # 第一数量
                'ACC_E_AMOUNT_UNIT': unit,                             # 数量单位
                'ACC_E_AMOUNT_YOY': clean_numeric_value(row.iloc[9]),  # 数量同比
                'ACC_E_CNY_AMOUNT': clean_numeric_value(row.iloc[10]), # 人民币金额
                'ACC_E_CNY_YOY': clean_numeric_value(row.iloc[11]),    # 人民币同比
                'ACC_E_USD_AMOUNT': clean_numeric_value(row.iloc[12]), # 美元金额
                'ACC_E_USD_YOY': clean_numeric_value(row.iloc[13]) if len(row) > 13 else None, # 美元同比
            })
        else:
            result.update({
                # 当月数据
                'MON_I_AMOUNT': clean_numeric_value(row.iloc[2]),      # 第一数量
                'MON_I_AMOUNT_UNIT': unit,                             # 数量单位
                'MON_I_AMOUNT_YOY': clean_numeric_value(row.iloc[3]),  # 数量同比
                'MON_I_CNY_AMOUNT': clean_numeric_value(row.iloc[4]),  # 人民币金额
                'MON_I_CNY_YOY': clean_numeric_value(row.iloc[5]),     # 人民币同比
                'MON_I_USD_AMOUNT': clean_numeric_value(row.iloc[6]),  # 美元金额
                'MON_I_USD_YOY': clean_numeric_value(row.iloc[7]),     # 美元同比
                # 累计数据
                'ACC_I_AMOUNT': clean_numeric_value(row.iloc[8]),      # 第一数量
                'ACC_I_AMOUNT_UNIT': unit,                             # 数量单位
                'ACC_I_AMOUNT_YOY': clean_numeric_value(row.iloc[9]),  # 数量同比
                'ACC_I_CNY_AMOUNT': clean_numeric_value(row.iloc[10]), # 人民币金额
                'ACC_I_CNY_YOY': clean_numeric_value(row.iloc[11]),    # 人民币同比
                'ACC_I_USD_AMOUNT': clean_numeric_value(row.iloc[12]), # 美元金额
                'ACC_I_USD_YOY': clean_numeric_value(row.iloc[13]) if len(row) > 13 else None, # 美元同比
            })
        
        results.append(result)
    
    print(f"    提取到 {len(results)} 条商品数据（{direction}）")
    return results

def convert_wan_yuan_to_yi_yuan(value):
    """
    将万元转换为亿元
    """
    if value is None:
        return None
    return value / 10000

def convert_qian_usd_to_usd(value):
    """
    将千美元转换为美元（这里保持原值，因为Oracle表中存储的是原始单位）
    """
    if value is None:
        return None
    return value * 1000  # 千美元转美元

def process_single_excel_file(excel_path):
    """
    处理单个Excel文件
    """
    filename = os.path.basename(excel_path)
    print(f"处理文件: {filename}")
    
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_path, header=None)
        
        # 确定文件类型
        file_type = determine_file_type(filename)
        
        # 根据文件类型调用相应的处理函数
        if file_type == 'type_1_monthly':
            results = process_type_1_monthly(df, filename)
        elif file_type == 'type_2_country':
            results = process_type_2_country(df, filename)
        elif file_type == 'type_5_trade_mode':
            results = process_type_5_trade_mode(df, filename)
        elif file_type == 'type_6_export_enterprise':
            results = process_type_6_7_enterprise(df, filename, is_export=True)
        elif file_type == 'type_7_import_enterprise':
            results = process_type_6_7_enterprise(df, filename, is_export=False)
        elif file_type == 'type_8_export_commodity':
            results = process_type_8_9_commodity(df, filename, is_export=True)
        elif file_type == 'type_9_import_commodity':
            results = process_type_8_9_commodity(df, filename, is_export=False)
        else:
            print(f"    未知文件类型: {file_type}")
            return None
        
        if not results:
            print(f"    文件 {filename} 没有提取到有效数据")
            return None
        
        return results
        
    except Exception as e:
        print(f"    处理文件 {filename} 时出错: {e}")
        return None

def convert_results_to_dataframe(results_list, stat_date):
    """
    将处理结果转换为DataFrame
    """
    if not results_list:
        return None
    
    # 目标CSV文件的列名
    output_columns = [
        "STAT_DATE", "STAT_TYPE", "STAT_NAME", "STAT_CODE", "STAT_CONTENT_RAW",
        "STAT_CONTENT_CLEANSE", "ACC_A_CNY_AMOUNT", "ACC_A_CNY_YOY", "ACC_A_USD_AMOUNT",
        "ACC_A_USD_YOY", "MON_A_CNY_AMOUNT", "MON_A_CNY_YOY", "MON_A_USD_AMOUNT",
        "MON_A_USD_YOY", "ACC_E_CNY_AMOUNT", "ACC_E_CNY_YOY", "ACC_E_USD_AMOUNT",
        "ACC_E_USD_YOY", "MON_E_CNY_AMOUNT", "MON_E_CNY_YOY", "MON_E_USD_AMOUNT",
        "MON_E_USD_YOY", "ACC_I_CNY_AMOUNT", "ACC_I_CNY_YOY", "ACC_I_USD_AMOUNT",
        "ACC_I_USD_YOY", "MON_I_CNY_AMOUNT", "MON_I_CNY_YOY", "MON_I_USD_AMOUNT",
        "MON_I_USD_YOY", "ACC_E_AMOUNT", "ACC_E_AMOUNT_UNIT", "ACC_E_AMOUNT_YOY",
        "MON_E_AMOUNT", "MON_E_AMOUNT_UNIT", "MON_E_AMOUNT_YOY", "ACC_I_AMOUNT",
        "ACC_I_AMOUNT_UNIT", "ACC_I_AMOUNT_YOY", "MON_I_AMOUNT", "MON_I_AMOUNT_UNIT",
        "MON_I_AMOUNT_YOY", "RANK_MARKERS", "DATA_SOURCE", "EMPHASIS_OR_EMERGING_MARK",
        "CREATE_TIME"
    ]
    
    # 创建DataFrame
    df_data = []
    for result in results_list:
        row = {}
        
        # 设置基础字段
        row['STAT_DATE'] = stat_date
        row['STAT_CODE'] = ''
        row['DATA_SOURCE'] = '02'  # 北京
        row['EMPHASIS_OR_EMERGING_MARK'] = ''
        row['RANK_MARKERS'] = ''
        row['CREATE_TIME'] = datetime.datetime.now().strftime('%Y/%m/%d %H:%M:%S')
        
        # 复制结果字段
        for key, value in result.items():
            if key in output_columns:
                # 金额字段需要单位转换
                if 'CNY_AMOUNT' in key and value is not None:
                    row[key] = convert_wan_yuan_to_yi_yuan(value)
                elif 'USD_AMOUNT' in key and value is not None:
                    row[key] = convert_qian_usd_to_usd(value)
                else:
                    row[key] = value
        
        # 确保所有列都存在
        for col in output_columns:
            if col not in row:
                if 'AMOUNT' in col or 'YOY' in col:
                    row[col] = None
                else:
                    row[col] = ''
        
        df_data.append(row)
    
    # 创建DataFrame
    output_df = pd.DataFrame(df_data, columns=output_columns)
    
    # 数据类型处理
    for col in output_df.columns:
        if 'AMOUNT' in col or 'YOY' in col:
            output_df[col] = pd.to_numeric(output_df[col], errors='coerce')
        else:
            output_df[col] = output_df[col].fillna('').astype(str)
    
    return output_df

def transform_single_excel_to_csv(excel_path, output_dir):
    """
    转换单个Excel文件为CSV
    """
    filename = os.path.basename(excel_path)
    stat_date = extract_date_from_filename(filename)
    
    print(f"\n处理: {filename}")
    print(f"日期: {stat_date}")
    
    # 处理Excel文件
    results = process_single_excel_file(excel_path)
    if not results:
        return None
    
    # 转换为DataFrame
    output_df = convert_results_to_dataframe(results, stat_date)
    if output_df is None:
        return None
    
    # 保存CSV文件
    csv_filename = f"T_STATISTICAL_CUS_TOTAL_BEIJING_{os.path.splitext(filename)[0]}.csv"
    csv_path = os.path.join(output_dir, csv_filename)
    output_df.to_csv(csv_path, index=False, encoding='utf-8-sig', float_format='%.4f')
    
    print(f"已转换并保存: {csv_filename} ({len(output_df)} 条记录)")
    return output_df

def batch_transform_beijing_excel_files(input_dir, output_dir):
    """
    批量转换北京Excel文件
    """
    # 创建输出目录
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    # 获取所有Excel文件
    excel_files = []
    for file in os.listdir(input_dir):
        if file.endswith('.xls') and not file.startswith('~'):
            excel_files.append(file)
    
    if not excel_files:
        print(f"在目录 {input_dir} 中没有找到Excel文件")
        return
    
    print(f"找到 {len(excel_files)} 个Excel文件")
    
    # 统计信息
    total_files = 0
    successful_files = 0
    total_records = 0
    failed_files = []
    
    for excel_file in sorted(excel_files):
        total_files += 1
        
        input_path = os.path.join(input_dir, excel_file)
        
        print(f"\n{'='*80}")
        print(f"处理文件 {total_files}/{len(excel_files)}")
        print(f"{'='*80}")
        
        try:
            result_df = transform_single_excel_to_csv(input_path, output_dir)
            if result_df is not None:
                successful_files += 1
                total_records += len(result_df)
            else:
                failed_files.append(excel_file)
        except Exception as e:
            print(f"处理出错: {e}")
            failed_files.append(excel_file)
    
    print(f"\n{'='*80}")
    print("批量转换完成！")
    print(f"{'='*80}")
    print(f"总文件数: {total_files}")
    print(f"成功处理: {successful_files}")
    print(f"失败文件: {len(failed_files)}")
    print(f"总记录数: {total_records}")
    print(f"输出目录: {output_dir}")
    
    if failed_files:
        print(f"\n失败的文件:")
        for f in failed_files:
            print(f"  - {f}")

def main():
    """
    主函数
    """
    input_directory = "北京海关统计数据_最终版"
    output_directory = "北京海关数据_完整转换结果"
    
    print("=" * 100)
    print("北京海关统计数据完整批量处理系统")
    print("支持7种类型文件：（1）月度表、（2）国别地区、（5）贸易方式、（6）（7）企业性质、（8）（9）商品量值")
    print("=" * 100)
    print(f"输入目录: {input_directory}")
    print(f"输出目录: {output_directory}")
    print(f"开始时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 批量转换Excel文件为CSV
    batch_transform_beijing_excel_files(input_directory, output_directory)
    
    print(f"\n结束时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 100)
    print("处理完成！")
    print("=" * 100)

if __name__ == '__main__':
    main()