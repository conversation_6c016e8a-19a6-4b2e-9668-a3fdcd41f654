from DrissionPage import ChromiumPage
import time
import os
import re
import shutil
import pandas as pd
from datetime import datetime

# 定义北京海关要抓取的表格种类配置（基于6月数据全量的北京文件夹中的文件）
BEIJING_TARGET_TABLES = {
    "(1)": "北京地区进出口商品总值表B：月度表",
    "(2)": "北京地区进出口商品国别（地区）总值表",
    "(5)": "北京地区进出口商品贸易方式总值表",
    "(6)": "北京地区出口商品贸易方式企业性质总值表",
    "(7)": "北京地区进口商品贸易方式企业性质总值表",
    "(8)": "北京地区出口主要商品量值表",
    "(9)": "北京地区进口主要商品量值表"
}

def is_target_table(text):
    """检查链接文本是否是我们需要的目标表格"""
    # 检查是否包含目标表格的关键特征
    for key, table_name in BEIJING_TARGET_TABLES.items():
        # 提取表格的关键词进行匹配
        if key in text and "北京地区" in text:
            if "(1)" in text and "总值表" in text and ("月度表" in text or "B" in text):
                return True, key
            elif "(2)" in text and "国别" in text and "总值表" in text:
                return True, key
            elif "(5)" in text and "贸易方式" in text and "总值表" in text and "企业性质" not in text:
                return True, key
            elif "(6)" in text and "出口" in text and "贸易方式" in text and "企业性质" in text:
                return True, key
            elif "(7)" in text and "进口" in text and "贸易方式" in text and "企业性质" in text:
                return True, key
            elif "(8)" in text and "出口" in text and "主要商品" in text and "量值表" in text:
                return True, key
            elif "(9)" in text and "进口" in text and "主要商品" in text and "量值表" in text:
                return True, key
    return False, None

def main():
    """主函数：爬取北京海关数据"""

    print("=== 北京海关数据爬虫 ===")

    # 创建基础下载目录
    base_dir = "北京海关统计数据"
    if not os.path.exists(base_dir):
        os.makedirs(base_dir)
        print(f"创建基础下载目录: {base_dir}")

    # 创建临时下载目录
    temp_download_dir = "temp_download_beijing"
    if not os.path.exists(temp_download_dir):
        os.makedirs(temp_download_dir)
        print(f"创建临时下载目录: {temp_download_dir}")

    # 设置ChromiumPage
    page = ChromiumPage(timeout=30)
    page.set.download_path(os.path.abspath(temp_download_dir))
    print(f"设置下载目录: {temp_download_dir}")

    # 北京海关统计数据页面
    beijing_url = "http://beijing.customs.gov.cn/beijing_customs/434756/434804/2941702/434773/434774/index.html"

    try:
        print(f"\n正在访问北京海关统计数据页面...")
        page.get(beijing_url)
        time.sleep(5)  # 等待页面完全加载

        print(f"页面标题: {page.title}")
        print(f"当前URL: {page.url}")

        # 查找所有链接
        print("\n开始查找数据链接...")
        all_links = page.eles('tag:a')

        target_links = []

        for link in all_links:
            link_text = link.text.strip()
            href = link.attr('href')

            if not href or not link_text:
                continue

            # 检查是否包含目标年份
            if any(year in link_text for year in ['2024', '2025']):
                # 使用新的目标表格检查函数
                is_target, table_key = is_target_table(link_text)

                if is_target:
                    # 构建完整URL
                    if href.startswith('/'):
                        full_url = "http://beijing.customs.gov.cn" + href
                    elif not href.startswith('http'):
                        current_url = page.url
                        base_path = '/'.join(current_url.split('/')[:-1]) + '/'
                        full_url = base_path + href
                    else:
                        full_url = href

                    target_links.append({
                        'text': link_text,
                        'url': full_url,
                        'year': '2024' if '2024' in link_text else '2025',
                        'table_key': table_key,
                        'table_name': BEIJING_TARGET_TABLES[table_key]
                    })

                    print(f"找到目标链接: {table_key} - {link_text[:50]}...")

        print(f"\n总共找到 {len(target_links)} 个目标链接")

        # 保存链接信息
        with open('北京海关数据链接.txt', 'w', encoding='utf-8') as f:
            for link in target_links:
                f.write(f"{link['year']},{link['table_key']},{link['table_name']},{link['text']},{link['url']}\n")

        print("链接信息已保存到: 北京海关数据链接.txt")

        # 开始下载文件
        download_count = 0
        error_count = 0

        for i, link_info in enumerate(target_links):
            try:
                print(f"\n处理第 {i+1}/{len(target_links)} 个链接...")
                print(f"标题: {link_info['text']}")
                print(f"URL: {link_info['url']}")

                # 访问详情页
                page.get(link_info['url'])
                time.sleep(3)

                # 生成文件名
                clean_text = re.sub(r'[\\/*?:"<>|]', '', link_info['text'])
                file_name = f"{link_info['year']}_{clean_text}.xlsx"
                file_path = os.path.join(base_dir, file_name)

                # 检查文件是否已存在
                if os.path.exists(file_path):
                    print(f"文件已存在，跳过: {file_name}")
                    continue

                # 查找下载按钮
                download_buttons = page.eles("下载")

                if download_buttons:
                    print(f"找到 {len(download_buttons)} 个下载按钮")

                    # 清空临时目录
                    for temp_file in os.listdir(temp_download_dir):
                        temp_file_path = os.path.join(temp_download_dir, temp_file)
                        if os.path.isfile(temp_file_path):
                            os.remove(temp_file_path)

                    # 尝试点击每个下载按钮，直到找到可点击的
                    download_success = False
                    for i, btn in enumerate(download_buttons):
                        try:
                            print(f"尝试点击第 {i+1} 个下载按钮...")

                            # 检查按钮是否可见和可点击
                            if btn.states.is_displayed and btn.states.is_enabled:
                                # 滚动到按钮位置
                                btn.scroll.to_see()
                                time.sleep(1)

                                # 点击下载
                                btn.click()
                                print("下载按钮点击成功")

                                # 等待下载完成
                                downloaded = False
                                start_time = time.time()
                                timeout = 60

                                while not downloaded and time.time() - start_time < timeout:
                                    time.sleep(2)

                                    temp_files = os.listdir(temp_download_dir)
                                    for temp_file in temp_files:
                                        if temp_file.endswith(('.xls', '.xlsx')) and not temp_file.endswith('.crdownload'):
                                            temp_file_path = os.path.join(temp_download_dir, temp_file)
                                            shutil.copy2(temp_file_path, file_path)
                                            print(f"文件下载成功: {file_name}")
                                            downloaded = True
                                            download_count += 1
                                            download_success = True
                                            break

                                if downloaded:
                                    break
                                else:
                                    print(f"第 {i+1} 个按钮点击后未开始下载")
                            else:
                                print(f"第 {i+1} 个下载按钮不可见或不可点击")

                        except Exception as e:
                            print(f"点击第 {i+1} 个下载按钮时出错: {e}")
                            continue

                    if not download_success:
                        print("所有下载按钮都无法正常工作")
                        error_count += 1

                else:
                    print("未找到下载按钮，尝试提取表格数据...")

                    # 查找表格
                    tables = page.eles('tag:table')
                    if tables:
                        print(f"找到 {len(tables)} 个表格")

                        # 选择最大的表格
                        max_table = None
                        max_rows = 0

                        for table in tables:
                            rows = table.eles('tag:tr')
                            if len(rows) > max_rows:
                                max_rows = len(rows)
                                max_table = table

                        if max_table and max_rows > 5:  # 至少要有5行数据
                            try:
                                html_content = max_table.html
                                dfs = pd.read_html(html_content, header=0)
                                df = dfs[0]
                                df.to_excel(file_path, index=False)
                                print(f"表格数据提取成功: {file_name}")
                                download_count += 1
                            except Exception as e:
                                print(f"表格数据提取失败: {e}")
                                error_count += 1
                        else:
                            print("未找到合适的表格数据")
                            error_count += 1
                    else:
                        print("页面上没有表格数据")
                        error_count += 1

            except Exception as e:
                print(f"处理链接时出错: {e}")
                error_count += 1

            # 防止请求过快
            time.sleep(2)

        print(f"\n=== 下载完成 ===")
        print(f"成功下载: {download_count} 个文件")
        print(f"失败: {error_count} 个文件")

    except Exception as e:
        print(f"主程序执行出错: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # 清理临时目录
        try:
            if os.path.exists(temp_download_dir):
                shutil.rmtree(temp_download_dir)
                print(f"临时目录已清理: {temp_download_dir}")
        except Exception as e:
            print(f"清理临时目录出错: {e}")

        # 关闭浏览器
        try:
            page.quit()
            print("浏览器已关闭")
        except:
            pass

if __name__ == "__main__":
    main()