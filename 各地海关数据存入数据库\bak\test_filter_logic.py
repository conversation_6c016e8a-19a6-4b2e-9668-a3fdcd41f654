def test_filter_logic():
    """测试筛选逻辑"""

    # 从调试信息中获取的实际链接文本
    test_links = [
        "（13）北京地区部分进口商品主要贸易方式量值表（2025年1-6月）",
        "（12）北京地区部分出口商品主要贸易方式量值表（2025年1-6月）",
        "（11）北京地区自部分国家（地区）进口商品类章金额表（2025年1-6月）",
        "（10）北京地区对部分国家（地区）出口商品类章金额表（2025年1-6月）",
        "（9）北京地区进口主要商品量值表（2025年1-6月）",
        "（8）北京地区出口主要商品量值表（2025年1-6月）",
        "（7）北京地区进口商品贸易方式企业性质总值表（2025年1-6月）",
        "（6）北京地区出口商品贸易方式企业性质总值表（2025年1-6月）",
        "（5）北京地区进出口商品贸易方式总值表（2025年1-6月）",
        "（4）北京地区进出口商品类章总值表（2025年1-6月）"
    ]

    print("=== 测试筛选逻辑 ===")

    for text in test_links:
        print(f"\n测试: {text}")

        # 检查是否是我们需要的表格
        if "北京地区" in text:
            print("  ✓ 包含'北京地区'")

            if "（5）" in text and "贸易方式" in text and "总值表" in text and "企业性质" not in text:
                print("  ✓ 匹配 (5): 北京地区进出口商品贸易方式总值表")
            elif "（6）" in text and "出口" in text and "贸易方式" in text and "企业性质" in text:
                print("  ✓ 匹配 (6): 北京地区出口商品贸易方式企业性质总值表")
            elif "（7）" in text and "进口" in text and "贸易方式" in text and "企业性质" in text:
                print("  ✓ 匹配 (7): 北京地区进口商品贸易方式企业性质总值表")
            elif "（8）" in text and "出口" in text and "主要商品" in text and "量值表" in text:
                print("  ✓ 匹配 (8): 北京地区出口主要商品量值表")
            elif "（9）" in text and "进口" in text and "主要商品" in text and "量值表" in text:
                print("  ✓ 匹配 (9): 北京地区进口主要商品量值表")
            else:
                print("  ✗ 不匹配任何目标表格")
        else:
            print("  ✗ 不包含'北京地区'")

if __name__ == "__main__":
    test_filter_logic()